<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

try {
    $data = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $required = ['username', 'password', 'email', 'full_name'];
    foreach ($required as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            throw new Exception("$field is required");
        }
    }
    
    $conn = getConnection();
    
    // Check if username exists
    $stmt = $conn->prepare("SELECT user_id FROM users WHERE username = ?");
    $stmt->execute([$data['username']]);
    if ($stmt->fetch()) {
        throw new Exception('Username already exists');
    }
    
    // Check if email exists
    $stmt = $conn->prepare("SELECT user_id FROM users WHERE email = ?");
    $stmt->execute([$data['email']]);
    if ($stmt->fetch()) {
        throw new Exception('Email already exists');
    }
    
    // Hash password
    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
    
    // Insert new user
    $stmt = $conn->prepare("
        INSERT INTO users (username, password, email, full_name, phone_number, address) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $data['username'],
        $hashedPassword,
        $data['email'],
        $data['full_name'],
        $data['phone_number'] ?? null,
        $data['address'] ?? null
    ]);
    
    $userId = $conn->lastInsertId();
    
    // Create wishlist for user
    $stmt = $conn->prepare("INSERT INTO wishlists (user_id) VALUES (?)");
    $stmt->execute([$userId]);
    
    // Create cart for user
    $stmt = $conn->prepare("INSERT INTO carts (user_id) VALUES (?)");
    $stmt->execute([$userId]);
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Registration successful',
        'data' => [
            'user_id' => $userId,
            'username' => $data['username'],
            'email' => $data['email'],
            'full_name' => $data['full_name']
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 