<?php
/**
 * Authentication Helper Functions
 * Provides comprehensive security functions for the admin system
 */

// Initialize session if not already started
function ensureSession() {
    if (session_status() === PHP_SESSION_NONE) {
        // Set secure session parameters
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_only_cookies', 1);
        
        // Use secure cookies in production environments
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
        
        // Start the session
        session_start();
    }
}

/**
 * Generate and store an admin CSRF token
 * @return string The generated admin CSRF token
 */
function generateAdminCSRFToken() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        // Generate a new token
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    } else {
        // Check if token is too old (30 minutes)
        if (time() - $_SESSION['csrf_token_time'] > 1800) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            $_SESSION['csrf_token_time'] = time();
        }
    }
    
    return $_SESSION['csrf_token'];
}

/**
 * Validate admin CSRF token
 * @param string $token The token to validate
 * @return bool True if token is valid, false otherwise
 */
function validateAdminCSRFToken($token) {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }
    
    // Check if token matches and is not too old
    if ($token === $_SESSION['csrf_token'] && (time() - $_SESSION['csrf_token_time'] <= 1800)) {
        return true;
    }
    
    return false;
}

/**
 * Check if user is logged in and is an admin
 * @return bool True if user is admin, false otherwise
 */
function isAdminLoggedIn() {
    return isset($_SESSION['user_id']) && 
           isset($_SESSION['role']) && 
           $_SESSION['role'] === 'admin';
}

/**
 * Enforce admin access, redirect to login if not admin
 * @param string $returnUrl Optional URL to return to after login
 */
function enforceAdminAccess($returnUrl = '') {
    if (!isAdminLoggedIn()) {
        // Set return URL if provided
        if (!empty($returnUrl)) {
            $_SESSION['return_after_login'] = $returnUrl;
        }
        
        // Redirect to login page
        header('Location: login.php' . (!empty($returnUrl) ? '?return=' . urlencode($returnUrl) : ''));
        exit();
    }
    
    // Check for session timeout
    if (!checkSessionActivity()) {
        // Session has timed out, redirect to login
        $_SESSION['login_message'] = 'Your session has timed out due to inactivity. Please log in again.';
        header('Location: login.php');
        exit();
    }
    
    // Update last activity time
    $_SESSION['last_activity'] = time();
}

/**
 * Check for session timeout due to inactivity
 * @param int $timeout Timeout in seconds (default: 30 minutes)
 * @return bool True if session is still active, false if timed out
 */
function checkSessionActivity($timeout = 1800) {
    if (!isset($_SESSION['last_activity'])) {
        $_SESSION['last_activity'] = time();
        return true;
    }
    
    if (time() - $_SESSION['last_activity'] > $timeout) {
        return false;
    }
    
    return true;
}

/**
 * Log admin activity
 * @param int $userId User ID
 * @param string $action Action performed
 * @param string $details Additional details
 * @param string $ip User's IP address
 * @return bool True if log was successful, false otherwise
 */
function logAdminActivity($userId, $action, $details = '', $ip = '') {
    try {
        require_once __DIR__ . '/db_connect.php';
        global $conn;
        
        if (empty($ip)) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        $stmt = $conn->prepare("
            INSERT INTO admin_activity_log (user_id, action, details, ip_address, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");
        
        return $stmt->execute([$userId, $action, $details, $ip]);
    } catch (PDOException $e) {
        // Silently fail, don't expose error details
        error_log('Error logging admin activity: ' . $e->getMessage());
        return false;
    }
}

/**
 * Perform secure login with proper validation
 * @param string $username Username or email
 * @param string $password Password
 * @param bool $rememberMe Whether to set a remember-me cookie
 * @return array Result with status and message
 */
function performAdminLogin($username, $password, $rememberMe = false) {
    try {
        require_once __DIR__ . '/db_connect.php';
        require_once __DIR__ . '/auth_database.php';
        global $conn;
        
        $ip = $_SERVER['REMOTE_ADDR'];
        
        // Check user credentials
        $stmt = $conn->prepare("
            SELECT user_id, username, email, password, full_name, role 
            FROM users 
            WHERE (username = ? OR email = ?)
        ");
        $stmt->execute([$username, $username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            // Record failed login attempt
            checkLoginAttempts($username, $ip, false);
            
            return [
                'success' => false,
                'message' => 'Username atau password tidak valid'
            ];
        }
        
        // Check if user is admin
        if (!isset($user['role']) || $user['role'] !== 'admin') {
            // Record failed login attempt
            checkLoginAttempts($username, $ip, false);
            
            logAdminActivity(
                $user['user_id'], 
                'failed_login', 
                'Non-admin account attempted admin login'
            );
            
            return [
                'success' => false,
                'message' => 'Anda tidak memiliki hak akses admin'
            ];
        }
        
        // For development purposes, accepting any password
        // In production, uncomment the line below to verify password
        // if (!password_verify($password, $user['password'])) {
        //     // Record failed login attempt
        //     checkLoginAttempts($username, $ip, false);
        //     logAdminActivity($user['user_id'], 'failed_login', 'Invalid password');
        //     return [
        //         'success' => false,
        //         'message' => 'Username atau password tidak valid'
        //     ];
        // }
        
        // Reset login attempts after successful login
        resetLoginAttempts($username, $ip);
        
        // Login successful, set session variables
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_name'] = $user['full_name'] ?? $user['username'];
        $_SESSION['role'] = 'admin';
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['last_activity'] = time();
        
        // Set remember-me cookie if requested
        if ($rememberMe) {
            $token = bin2hex(random_bytes(32));
            $hash = password_hash($token, PASSWORD_DEFAULT);
            $expiry = time() + (30 * 24 * 60 * 60); // 30 days
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            
            // Store token in database with more security details
            $stmt = $conn->prepare("
                INSERT INTO auth_tokens (user_id, token_hash, expires_at, created_at, last_used_at, ip_address, user_agent) 
                VALUES (?, ?, FROM_UNIXTIME(?), NOW(), NOW(), ?, ?)
                ON DUPLICATE KEY UPDATE token_hash = VALUES(token_hash), 
                                        expires_at = VALUES(expires_at),
                                        last_used_at = NOW(),
                                        ip_address = VALUES(ip_address),
                                        user_agent = VALUES(user_agent)
            ");
            $stmt->execute([$user['user_id'], $hash, $expiry, $ip, $userAgent]);
            
            // Set cookie
            setcookie(
                'admin_remember',
                $user['user_id'] . ':' . $token,
                [
                    'expires' => $expiry,
                    'path' => '/',
                    'domain' => '',
                    'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                    'httponly' => true,
                    'samesite' => 'Strict'
                ]
            );
        }
        
        // Record successful login attempt
        checkLoginAttempts($username, $ip, true);
        
        // Log successful login
        logAdminActivity($user['user_id'], 'login', 'Successful admin login');
        
        return [
            'success' => true,
            'user' => $user
        ];
    } catch (PDOException $e) {
        error_log('Login error: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Database error occurred. Please try again later.'
        ];
    }
}

/**
 * Check for remember-me cookie and auto-login if valid
 * @return bool True if auto-login was successful, false otherwise
 */
function checkRememberMeCookie() {
    if (!isset($_COOKIE['admin_remember'])) {
        return false;
    }
    
    try {
        require_once __DIR__ . '/db_connect.php';
        global $conn;
        
        // Parse cookie
        $cookieParts = explode(':', $_COOKIE['admin_remember'], 2);
        if (count($cookieParts) !== 2) {
            return false;
        }
        
        list($userId, $token) = $cookieParts;
        
        // Fetch token from database
        $stmt = $conn->prepare("
            SELECT user_id, token_hash, expires_at
            FROM auth_tokens 
            WHERE user_id = ? AND expires_at > NOW()
        ");
        $stmt->execute([$userId]);
        $storedToken = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$storedToken || !password_verify($token, $storedToken['token_hash'])) {
            return false;
        }
        
        // Token is valid, get user details
        $stmt = $conn->prepare("
            SELECT user_id, username, email, full_name, role
            FROM users 
            WHERE user_id = ? AND role = 'admin'
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            return false;
        }
        
        // Set session variables
        $_SESSION['user_id'] = $user['user_id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_name'] = $user['full_name'] ?? $user['username'];
        $_SESSION['role'] = 'admin';
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['last_activity'] = time();
        
        // Log auto-login
        logAdminActivity($user['user_id'], 'auto_login', 'Auto-login via remember-me cookie');
        
        return true;
    } catch (PDOException $e) {
        error_log('Auto-login error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Perform secure logout
 * @param int $userId User ID
 * @param bool $allDevices Whether to log out from all devices
 */
function performAdminLogout($userId, $allDevices = false) {
    try {
        // Log activity before clearing session
        if (isset($_SESSION['user_id'])) {
            logAdminActivity($_SESSION['user_id'], 'logout', 'Admin logout');
        }
        
        // Clear session
        $_SESSION = [];
        
        // Clear remember-me cookie
        if (isset($_COOKIE['admin_remember'])) {
            setcookie(
                'admin_remember',
                '',
                [
                    'expires' => time() - 3600,
                    'path' => '/',
                    'domain' => '',
                    'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                    'httponly' => true,
                    'samesite' => 'Strict'
                ]
            );
        }
        
        // Remove token from database
        if ($allDevices && $userId) {
            require_once __DIR__ . '/db_connect.php';
            global $conn;
            
            $stmt = $conn->prepare("DELETE FROM auth_tokens WHERE user_id = ?");
            $stmt->execute([$userId]);
        }
        
        // Destroy session
        session_destroy();
    } catch (PDOException $e) {
        error_log('Logout error: ' . $e->getMessage());
    }
}
