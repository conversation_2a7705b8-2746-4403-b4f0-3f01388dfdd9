<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: admin_login.php');
    exit;
}

// Database connection
$conn = getConnection();

// Process user status toggle
if (isset($_GET['action']) && $_GET['action'] === 'toggle_status') {
    try {
        $user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if ($user_id <= 0) {
            throw new Exception("ID pengguna tidak valid");
        }
        
        // Get current status
        $stmt = $conn->prepare("SELECT user_id, status, username FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception("Pengguna tidak ditemukan");
        }
        
        // Don't allow deactivating self
        if ($user_id == $_SESSION['user_id']) {
            throw new Exception("Anda tidak dapat menonaktifkan akun Anda sendiri");
        }
        
        // Toggle status
        $new_status = ($user['status'] === 'active') ? 'inactive' : 'active';
        $status_text = ($new_status === 'active') ? 'aktif' : 'nonaktif';
        
        $stmt = $conn->prepare("UPDATE users SET status = ? WHERE user_id = ?");
        $stmt->execute([$new_status, $user_id]);
        
        $_SESSION['alert_message'] = "Status pengguna \"".htmlspecialchars($user['username'])."\" berhasil diubah menjadi $status_text";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to users page
    header('Location: users.php');
    exit;
}

// Process user role change
if (isset($_GET['action']) && $_GET['action'] === 'change_role') {
    try {
        $user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
        $new_role = isset($_POST['role']) ? $_POST['role'] : '';
        
        if ($user_id <= 0) {
            throw new Exception("ID pengguna tidak valid");
        }
        
        // Validate role
        $valid_roles = ['customer', 'admin', 'staff'];
        if (!in_array($new_role, $valid_roles)) {
            throw new Exception("Peran tidak valid");
        }
        
        // Get user data
        $stmt = $conn->prepare("SELECT user_id, role, username FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception("Pengguna tidak ditemukan");
        }
        
        // Don't allow changing own role
        if ($user_id == $_SESSION['user_id']) {
            throw new Exception("Anda tidak dapat mengubah peran Anda sendiri");
        }
        
        // Update role
        $stmt = $conn->prepare("UPDATE users SET role = ? WHERE user_id = ?");
        $stmt->execute([$new_role, $user_id]);
        
        $_SESSION['alert_message'] = "Peran pengguna \"".htmlspecialchars($user['username'])."\" berhasil diubah menjadi $new_role";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to users page
    header('Location: users.php');
    exit;
}

// Process user deletion
if (isset($_GET['action']) && $_GET['action'] === 'delete') {
    try {
        $user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if ($user_id <= 0) {
            throw new Exception("ID pengguna tidak valid");
        }
        
        // Get user data
        $stmt = $conn->prepare("SELECT user_id, username FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception("Pengguna tidak ditemukan");
        }
        
        // Don't allow deleting self
        if ($user_id == $_SESSION['user_id']) {
            throw new Exception("Anda tidak dapat menghapus akun Anda sendiri");
        }
        
        // Check if user has orders
        $stmt = $conn->prepare("SELECT COUNT(*) FROM orders WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $order_count = $stmt->fetchColumn();
        
        if ($order_count > 0) {
            throw new Exception("Tidak dapat menghapus: pengguna memiliki $order_count pesanan. Nonaktifkan saja akun ini.");
        }
        
        // Begin transaction
        $conn->beginTransaction();
        
        // Delete user's cart if exists
        $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ?");
        $stmt->execute([$user_id]);
        
        // Delete user's wishlist if exists
        $stmt = $conn->prepare("DELETE FROM wishlist WHERE user_id = ?");
        $stmt->execute([$user_id]);
        
        // Delete user's reviews if exists
        $stmt = $conn->prepare("DELETE FROM reviews WHERE user_id = ?");
        $stmt->execute([$user_id]);
        
        // Delete user's address if exists
        $stmt = $conn->prepare("DELETE FROM user_addresses WHERE user_id = ?");
        $stmt->execute([$user_id]);
        
        // Finally delete the user
        $stmt = $conn->prepare("DELETE FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['alert_message'] = "Pengguna \"".htmlspecialchars($user['username'])."\" berhasil dihapus";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to users page
    header('Location: users.php');
    exit;
}

// Process password reset
if (isset($_GET['action']) && $_GET['action'] === 'reset_password') {
    try {
        $user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
        $new_password = isset($_POST['new_password']) ? $_POST['new_password'] : '';
        $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
        
        if ($user_id <= 0) {
            throw new Exception("ID pengguna tidak valid");
        }
        
        // Validate password
        if (empty($new_password)) {
            throw new Exception("Password baru wajib diisi");
        }
        
        if (strlen($new_password) < 8) {
            throw new Exception("Password harus terdiri dari minimal 8 karakter");
        }
        
        if ($new_password !== $confirm_password) {
            throw new Exception("Konfirmasi password tidak sesuai");
        }
        
        // Get user data
        $stmt = $conn->prepare("SELECT user_id, username FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception("Pengguna tidak ditemukan");
        }
        
        // Hash new password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        
        // Update password
        $stmt = $conn->prepare("UPDATE users SET password = ? WHERE user_id = ?");
        $stmt->execute([$hashed_password, $user_id]);
        
        $_SESSION['alert_message'] = "Password pengguna \"".htmlspecialchars($user['username'])."\" berhasil direset";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to users page
    header('Location: users.php');
    exit;
}

// Process bulk actions
if (isset($_POST['bulk_action']) && !empty($_POST['selected_users'])) {
    try {
        $bulk_action = $_POST['bulk_action'];
        $selected_users = $_POST['selected_users'];
        
        if (!is_array($selected_users) || empty($selected_users)) {
            throw new Exception("Tidak ada pengguna yang dipilih");
        }
        
        // Validate user IDs
        $user_ids = array_map('intval', $selected_users);
        $user_ids = array_filter($user_ids, function($id) { return $id > 0; });
        
        if (empty($user_ids)) {
            throw new Exception("Tidak ada pengguna valid yang dipilih");
        }
        
        // Remove current user from selection to prevent self-modifications
        $current_user_key = array_search($_SESSION['user_id'], $user_ids);
        if ($current_user_key !== false) {
            unset($user_ids[$current_user_key]);
        }
        
        if (empty($user_ids)) {
            throw new Exception("Tidak dapat melakukan aksi pada diri sendiri");
        }
        
        // Begin transaction
        $conn->beginTransaction();
        
        $count = count($user_ids);
        $placeholders = implode(',', array_fill(0, $count, '?'));
        
        switch ($bulk_action) {
            case 'activate':
                $stmt = $conn->prepare("UPDATE users SET status = 'active' WHERE user_id IN ($placeholders)");
                $stmt->execute($user_ids);
                $_SESSION['alert_message'] = "Berhasil mengaktifkan $count pengguna";
                break;
                
            case 'deactivate':
                $stmt = $conn->prepare("UPDATE users SET status = 'inactive' WHERE user_id IN ($placeholders)");
                $stmt->execute($user_ids);
                $_SESSION['alert_message'] = "Berhasil menonaktifkan $count pengguna";
                break;
                
            case 'change_role':
                if (!isset($_POST['bulk_role']) || empty($_POST['bulk_role'])) {
                    throw new Exception("Peran tidak dipilih");
                }
                
                $new_role = $_POST['bulk_role'];
                $valid_roles = ['customer', 'admin', 'staff'];
                if (!in_array($new_role, $valid_roles)) {
                    throw new Exception("Peran tidak valid");
                }
                
                $stmt = $conn->prepare("UPDATE users SET role = ? WHERE user_id IN ($placeholders)");
                $params = array_merge([$new_role], $user_ids);
                $stmt->execute($params);
                
                $_SESSION['alert_message'] = "Berhasil mengubah peran $count pengguna menjadi $new_role";
                break;
                
            case 'delete':
                // Check if any users have orders
                $stmt = $conn->prepare("
                    SELECT user_id, username, (SELECT COUNT(*) FROM orders WHERE orders.user_id = users.user_id) as order_count 
                    FROM users 
                    WHERE user_id IN ($placeholders)
                ");
                $stmt->execute($user_ids);
                $users_with_orders = [];
                
                while ($user = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    if ($user['order_count'] > 0) {
                        $users_with_orders[] = $user['username'] . " (" . $user['order_count'] . " pesanan)";
                    }
                }
                
                if (!empty($users_with_orders)) {
                    throw new Exception("Tidak dapat menghapus beberapa pengguna karena memiliki pesanan: " . implode(", ", $users_with_orders));
                }
                
                // Delete users' related data
                $stmt = $conn->prepare("DELETE FROM cart WHERE user_id IN ($placeholders)");
                $stmt->execute($user_ids);
                
                $stmt = $conn->prepare("DELETE FROM wishlist WHERE user_id IN ($placeholders)");
                $stmt->execute($user_ids);
                
                $stmt = $conn->prepare("DELETE FROM reviews WHERE user_id IN ($placeholders)");
                $stmt->execute($user_ids);
                
                $stmt = $conn->prepare("DELETE FROM user_addresses WHERE user_id IN ($placeholders)");
                $stmt->execute($user_ids);
                
                // Delete users
                $stmt = $conn->prepare("DELETE FROM users WHERE user_id IN ($placeholders)");
                $stmt->execute($user_ids);
                
                $_SESSION['alert_message'] = "Berhasil menghapus $count pengguna";
                break;
                
            default:
                throw new Exception("Aksi tidak dikenal");
        }
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to users page
    header('Location: users.php');
    exit;
}

// Redirect to users page if no action was taken
header('Location: users.php');
exit;
