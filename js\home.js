// Product stock data
const productStocks = {
  1: 50,  // Pensil 2B Joyko
  2: 100, // <PERSON><PERSON>l Sosro
  3: 30,  // Buku Sidu A4 70gr
  4: 75,  // <PERSON><PERSON> Special
  5: 40,  // Stabilo Boss
  6: 60,  // <PERSON><PERSON>-<PERSON>
  7: 25,  // Pensil Warna Faber-Castell
  8: 80,  // <PERSON><PERSON>
  9: 45,  // <PERSON>der Clip <PERSON>
  10: 120, // <PERSON>pi ABC Susu
  11: 35,  // Penggaris Butterfly
  12: 65   // Good Day Freeze
};

// Function to update stock display
function updateStockDisplay(productId, newStock) {
  const stockElement = document.getElementById(`stock-${productId}`);
  if (stockElement) {
    stockElement.textContent = `Stok: ${newStock}`;
    
    // Change color if stock is low
    if (newStock < 10) {
      stockElement.style.color = 'red';
      stockElement.style.fontWeight = 'bold';
    } else {
      stockElement.style.color = '';
      stockElement.style.fontWeight = '';
    }
  }
}

// Function to open modal
function openModal(modalId) {
  document.getElementById(modalId).style.display = 'block';
  document.body.style.overflow = 'hidden';
}

// Function to close modal
function closeModal(modalId) {
  document.getElementById(modalId).style.display = 'none';
  document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
window.onclick = function(event) {
  if (event.target.className === 'product-modal') {
    event.target.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// Initialize quantity selectors
document.addEventListener('DOMContentLoaded', function() {
  // Quantity selector functionality
  document.querySelectorAll('.quantity-selector').forEach(selector => {
    const decreaseBtn = selector.querySelector('.decrease');
    const increaseBtn = selector.querySelector('.increase');
    const qtyInput = selector.querySelector('.qty-input');
    
    decreaseBtn.addEventListener('click', () => {
      let value = parseInt(qtyInput.value);
      if (value > 1) {
        qtyInput.value = value - 1;
      }
    });
    
    increaseBtn.addEventListener('click', () => {
      let value = parseInt(qtyInput.value);
      qtyInput.value = value + 1;
    });
  });

  // Update cart counter on page load
  updateCartCounter();
  
  // Initialize stock display
  for (const [productId, stock] of Object.entries(productStocks)) {
    updateStockDisplay(productId, stock);
  }
});

// Function to add to cart with quantity from modal
function addToCartWithQuantity(id, name, price, image) {
  const modal = document.querySelector('.product-modal[style*="display: block"]');
  if (!modal) return;

  const quantity = parseInt(modal.querySelector('.qty-input').value);
  const currentStock = productStocks[id];
  
  if (!currentStock || currentStock <= 0) {
    showToast('Maaf, stok produk ini sudah habis', 'danger');
    return;
  }
  
  if (quantity <= 0) {
    showToast('Jumlah tidak valid', 'danger');
    return;
  }
  
  if (currentStock < quantity) {
    showToast(`Maaf, stok hanya tersisa ${currentStock} item`, 'warning');
    return;
  }
  
  // Update stock
  productStocks[id] = currentStock - quantity;
  updateStockDisplay(id, productStocks[id]);
  
  // Update cart
  let cart = JSON.parse(localStorage.getItem('cart')) || [];
  const existingItem = cart.find(item => item.id === id);
  
  if (existingItem) {
    existingItem.quantity += quantity;
  } else {
    cart.push({
      id: id,
      name: name,
      price: price,
      image: image,
      quantity: quantity
    });
  }
  
  localStorage.setItem('cart', JSON.stringify(cart));
  updateCartCounter();
  showToast(`${quantity} ${name} telah ditambahkan ke keranjang`, 'success');
  closeModal(modal.id);
  modal.querySelector('.qty-input').value = 1;
}

// Function to add to cart (single item) with stock reservation
function addToCart(id, name, price, image, stock) {
  const currentStock = productStocks[id];

  if (!currentStock || currentStock <= 0) {
    showToast(`Maaf, stok ${name} sudah habis`, 'danger');
    return;
  }

  // Reserve stock via API instead of immediately reducing
  fetch('api/stock_reservation.php', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: `action=reserve&product_id=${id}&quantity=1`
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Update cart
      let cart = JSON.parse(localStorage.getItem('cart')) || [];
      const existingItem = cart.find(item => item.id === id);

      if (existingItem) {
        existingItem.quantity += 1;
      } else {
        cart.push({
          id: id,
          name: name,
          price: price,
          image: image,
          quantity: 1
        });
      }

      localStorage.setItem('cart', JSON.stringify(cart));
      updateCartCounter();
      showToast(`1 ${name} telah ditambahkan ke keranjang`, 'success');

      // Update available stock display (reserved stock)
      if (data.available_stock !== undefined) {
        updateStockDisplay(id, data.available_stock);
      }
    } else {
      showToast(`Gagal menambahkan ${name}: ${data.message}`, 'danger');
    }
  })
  .catch(error => {
    console.error('Error:', error);
    showToast(`Gagal menambahkan ${name} ke keranjang`, 'danger');
  });
}

// Update cart counter
function updateCartCounter() {
  const cart = JSON.parse(localStorage.getItem('cart')) || [];
  const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
  document.querySelector('.cart-count').textContent = totalItems;
}

// Show toast notification
function showToast(message, type) {
  let background = '';
  switch(type) {
    case 'success':
      background = 'linear-gradient(to right, #00b09b, #96c93d)';
      break;
    case 'danger':
      background = 'linear-gradient(to right, #ff416c, #ff4b2b)';
      break;
    case 'warning':
      background = 'linear-gradient(to right, #f46b45, #eea849)';
      break;
    default:
      background = 'linear-gradient(to right, #00b09b, #96c93d)';
  }
  
  Toastify({
    text: message,
    duration: 3000,
    close: true,
    gravity: "top",
    position: "right",
    backgroundColor: background,
    stopOnFocus: true
  }).showToast();
}

// Search products function
function searchProducts() {
  const searchTerm = document.querySelector('.search-bar').value.toLowerCase();
  const productCards = document.querySelectorAll('.product-card');
  
  productCards.forEach(card => {
    const title = card.querySelector('.product-title').textContent.toLowerCase();
    card.style.display = title.includes(searchTerm) ? 'block' : 'none';
  });
}

// Event listeners for search
const searchBar = document.querySelector('.search-bar');
if (searchBar) {
  searchBar.addEventListener('keyup', function(event) {
    if (event.key === 'Enter') {
      searchProducts();
    }
  });
  
  const searchIcon = document.querySelector('.fa-search');
  if (searchIcon) {
    searchIcon.addEventListener('click', searchProducts);
  }
}