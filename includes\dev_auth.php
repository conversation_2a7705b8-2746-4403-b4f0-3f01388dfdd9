<?php
/**
 * Development Authentication Helper
 * This file provides authentication functions for development and testing
 */

/**
 * Check if user is authenticated and set default session if needed
 */
function ensureUserAuthenticated($redirect_to_login = true) {
    // Check if user is already logged in
    if (isset($_SESSION['user_id']) && $_SESSION['user_id']) {
        return true;
    }

    // For development/testing, you can enable auto-login by setting this to true
    $auto_login_enabled = true;

    if ($auto_login_enabled && !$redirect_to_login) {
        $_SESSION['user_id'] = 1;
        $_SESSION['user_email'] = '<EMAIL>';
        $_SESSION['user_name'] = 'Admin User';
        $_SESSION['username'] = 'admin';
        $_SESSION['full_name'] = 'Admin User';
        $_SESSION['role'] = 'customer';
        $_SESSION['firebase_user_id'] = 'test_firebase_uid';
        $_SESSION['local_user_id'] = 1;
        $_SESSION['last_activity'] = time();
        return true;
    }

    // Redirect to login if not authenticated
    if ($redirect_to_login) {
        $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        header('Location: simple_login.php');
        exit;
    }

    return false;
}

/**
 * Get current user ID
 */
function getCurrentUserId() {
    ensureUserAuthenticated();
    return $_SESSION['user_id'] ?? null;
}

/**
 * Get current user data
 */
function getCurrentUserData() {
    ensureUserAuthenticated();
    return [
        'user_id' => $_SESSION['user_id'] ?? null,
        'email' => $_SESSION['user_email'] ?? null,
        'name' => $_SESSION['user_name'] ?? null,
        'username' => $_SESSION['username'] ?? null,
        'full_name' => $_SESSION['full_name'] ?? null,
        'role' => $_SESSION['role'] ?? 'customer'
    ];
}

/**
 * Check if current user is admin (session-based)
 */
function isCurrentUserAdmin() {
    ensureUserAuthenticated();
    return ($_SESSION['role'] ?? '') === 'admin';
}

/**
 * Require authentication (redirect to login if not authenticated)
 */
function requireAuth($redirect_page = 'login.php') {
    if (!ensureUserAuthenticated(true)) {
        $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
        header("Location: $redirect_page");
        exit;
    }
}
?>
