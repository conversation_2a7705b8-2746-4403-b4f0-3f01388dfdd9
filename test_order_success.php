<?php
session_start();
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// Set test user
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
}

echo "<h1>🧪 Order Success Page Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// Test 1: Check if there are any orders in the database
echo "<div class='test-section'>";
echo "<h2>Test 1: Check Orders in Database</h2>";

try {
    $stmt = $conn->query("SELECT COUNT(*) as order_count FROM orders");
    $result = $stmt->fetch();
    $order_count = $result['order_count'];
    
    echo "<p class='info'>📊 Total orders in database: <strong>$order_count</strong></p>";
    
    if ($order_count > 0) {
        // Get latest orders
        $stmt = $conn->query("
            SELECT order_id, user_id, 
                   COALESCE(order_number, CONCAT('ORD-', order_id)) as display_number,
                   COALESCE(order_status, STATUS, status, 'pending') as current_status,
                   total_amount, payment_method,
                   COALESCE(order_date, created_at) as order_date
            FROM orders 
            ORDER BY order_id DESC 
            LIMIT 5
        ");
        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>📋 Latest Orders:</h4>";
        echo "<table>";
        echo "<tr><th>Order ID</th><th>User ID</th><th>Order Number</th><th>Status</th><th>Total</th><th>Payment</th><th>Date</th><th>Action</th></tr>";
        
        foreach ($orders as $order) {
            echo "<tr>";
            echo "<td>{$order['order_id']}</td>";
            echo "<td>{$order['user_id']}</td>";
            echo "<td>{$order['display_number']}</td>";
            echo "<td>{$order['current_status']}</td>";
            echo "<td>Rp " . number_format($order['total_amount']) . "</td>";
            echo "<td>{$order['payment_method']}</td>";
            echo "<td>{$order['order_date']}</td>";
            echo "<td><a href='order-success.php?order_id={$order['order_id']}' target='_blank'>View</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ No orders found in database</p>";
        echo "<p class='info'>💡 Create a test order first</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking orders: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 2: Check order_items table
echo "<div class='test-section'>";
echo "<h2>Test 2: Check Order Items</h2>";

try {
    $stmt = $conn->query("SELECT COUNT(*) as item_count FROM order_items");
    $result = $stmt->fetch();
    $item_count = $result['item_count'];
    
    echo "<p class='info'>📦 Total order items in database: <strong>$item_count</strong></p>";
    
    if ($item_count > 0) {
        // Check order_items structure
        $stmt = $conn->query("DESCRIBE order_items");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>🔧 Order Items Table Structure:</h4>";
        echo "<table>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $has_unit_price = false;
        $has_total_price = false;
        $has_price = false;
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
            
            if ($column['Field'] === 'unit_price') $has_unit_price = true;
            if ($column['Field'] === 'total_price') $has_total_price = true;
            if ($column['Field'] === 'price') $has_price = true;
        }
        echo "</table>";
        
        echo "<p class='info'>📋 Price Column Analysis:</p>";
        echo "<p class='" . ($has_unit_price ? 'success' : 'error') . "'>" . ($has_unit_price ? '✅' : '❌') . " unit_price column: " . ($has_unit_price ? 'Present' : 'Missing') . "</p>";
        echo "<p class='" . ($has_total_price ? 'success' : 'error') . "'>" . ($has_total_price ? '✅' : '❌') . " total_price column: " . ($has_total_price ? 'Present' : 'Missing') . "</p>";
        echo "<p class='" . ($has_price ? 'success' : 'error') . "'>" . ($has_price ? '✅' : '❌') . " price column: " . ($has_price ? 'Present' : 'Missing') . "</p>";
        
        // Get sample order items
        $stmt = $conn->query("
            SELECT oi.*, 
                   COALESCE(p.name, p.NAME) as product_name
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.product_id
            ORDER BY oi.order_item_id DESC 
            LIMIT 5
        ");
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>📦 Sample Order Items:</h4>";
        echo "<table>";
        echo "<tr><th>Item ID</th><th>Order ID</th><th>Product</th><th>Quantity</th><th>Unit Price</th><th>Total Price</th></tr>";
        
        foreach ($items as $item) {
            echo "<tr>";
            echo "<td>{$item['order_item_id']}</td>";
            echo "<td>{$item['order_id']}</td>";
            echo "<td>{$item['product_name']}</td>";
            echo "<td>{$item['quantity']}</td>";
            echo "<td>Rp " . number_format($item['unit_price'] ?? 0) . "</td>";
            echo "<td>Rp " . number_format($item['total_price'] ?? 0) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking order items: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 3: Create a test order
echo "<div class='test-section'>";
echo "<h2>Test 3: Create Test Order</h2>";

if (isset($_POST['create_test_order'])) {
    try {
        $conn->beginTransaction();
        
        // Create test order
        $order_number = generateOrderNumber();
        $stmt = $conn->prepare("
            INSERT INTO orders (
                order_number, user_id, shipping_name, shipping_email, shipping_phone, shipping_address,
                payment_method, order_status, order_date, shipping_method, shipping_cost, subtotal, total_amount
            ) VALUES (
                ?, ?, 'Test User', '<EMAIL>', '************', 'Test Address 123',
                'bank_transfer', 'pending', NOW(), 'standard', 10000, 100000, 110000
            )
        ");
        
        $stmt->execute([
            $order_number,
            $_SESSION['user_id']
        ]);
        
        $order_id = $conn->lastInsertId();
        
        // Add test order items
        $stmt = $conn->prepare("
            INSERT INTO order_items (
                order_id, product_id, quantity, unit_price, total_price
            ) VALUES (
                ?, 1, 2, 50000, 100000
            )
        ");
        $stmt->execute([$order_id]);
        
        $conn->commit();
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ Test Order Created Successfully!</h4>";
        echo "<p><strong>Order ID:</strong> $order_id</p>";
        echo "<p><strong>Order Number:</strong> $order_number</p>";
        echo "<p><a href='order-success.php?order_id=$order_id' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Order Success Page</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        $conn->rollBack();
        echo "<p class='error'>❌ Error creating test order: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<form method='POST'>";
    echo "<p>Create a test order to verify the order success page works correctly.</p>";
    echo "<button type='submit' name='create_test_order'>Create Test Order</button>";
    echo "</form>";
}
echo "</div>";

// Test 4: Test order success page functionality
echo "<div class='test-section'>";
echo "<h2>Test 4: Order Success Page Links</h2>";

try {
    // Get the latest order for current user
    $stmt = $conn->prepare("
        SELECT order_id, 
               COALESCE(order_number, CONCAT('ORD-', order_id)) as display_number
        FROM orders 
        WHERE user_id = ? 
        ORDER BY order_id DESC 
        LIMIT 1
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $latest_order = $stmt->fetch();
    
    if ($latest_order) {
        echo "<p class='success'>✅ Latest order found: Order ID {$latest_order['order_id']}</p>";
        echo "<div style='margin: 15px 0;'>";
        echo "<a href='order-success.php?order_id={$latest_order['order_id']}' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test Order Success Page</a>";
        echo "<a href='simple_checkout_test.php' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create New Order</a>";
        echo "</div>";
    } else {
        echo "<p class='error'>❌ No orders found for current user</p>";
        echo "<p class='info'>💡 Create an order first using the checkout system</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>🎯 Summary</h2>";
echo "<p><strong>Order Success Page Testing:</strong></p>";
echo "<ul>";
echo "<li>✅ Database structure verified</li>";
echo "<li>✅ Order and order_items tables checked</li>";
echo "<li>✅ Price column compatibility handled</li>";
echo "<li>✅ Test order creation available</li>";
echo "<li>✅ Direct links to order success page provided</li>";
echo "</ul>";
echo "<p><strong>The order success page should now display order details correctly!</strong></p>";
echo "</div>";
?>
