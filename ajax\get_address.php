<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    $address_id = $_GET['address_id'] ?? 0;
    
    if (!$address_id) {
        echo json_encode(['success' => false, 'message' => 'Address ID is required']);
        exit;
    }
    
    // Get address data
    $stmt = $conn->prepare("
        SELECT address_id as id, label, recipient_name, phone, address, 
               city, province, postal_code, is_default, created_at
        FROM user_addresses 
        WHERE address_id = ? AND user_id = ?
    ");
    $stmt->execute([$address_id, $user_id]);
    $address = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$address) {
        echo json_encode(['success' => false, 'message' => 'Address not found']);
        exit;
    }
    
    // Convert is_default to boolean
    $address['is_default'] = (bool)$address['is_default'];
    
    echo json_encode([
        'success' => true,
        'address' => $address
    ]);
    
} catch (Exception $e) {
    error_log("Get address error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while loading address']);
}
?>
