<?php
session_start();
require_once '../includes/db_connect.php';
require_once 'auth.php';
checkAdmin();

// Get date range from request
$start_date = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$end_date = $_GET['end_date'] ?? date('Y-m-d'); // Today

// Sales Report
try {
    $stmt = $conn->prepare("
        SELECT 
            DATE(created_at) as order_date,
            COUNT(*) as total_orders,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as avg_order_value
        FROM orders 
        WHERE DATE(created_at) BETWEEN ? AND ? 
        AND status != 'cancelled'
        GROUP BY DATE(created_at)
        ORDER BY order_date DESC
    ");
    $stmt->execute([$start_date, $end_date]);
    $daily_sales = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Summary statistics
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_orders,
            SUM(total_amount) as total_revenue,
            AVG(total_amount) as avg_order_value,
            COUNT(DISTINCT user_id) as unique_customers
        FROM orders 
        WHERE DATE(created_at) BETWEEN ? AND ? 
        AND status != 'cancelled'
    ");
    $stmt->execute([$start_date, $end_date]);
    $summary = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error = "Error loading sales data: " . $e->getMessage();
}

// Top Products
try {
    $stmt = $conn->prepare("
        SELECT 
            p.NAME as product_name,
            SUM(oi.quantity) as total_sold,
            SUM(oi.quantity * oi.price) as total_revenue,
            COUNT(DISTINCT o.order_id) as order_count
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.order_id
        JOIN products p ON oi.product_id = p.product_id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        AND o.status != 'cancelled'
        GROUP BY oi.product_id, p.NAME
        ORDER BY total_sold DESC
        LIMIT 10
    ");
    $stmt->execute([$start_date, $end_date]);
    $top_products = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $top_products = [];
}

// User Registration Stats
try {
    $stmt = $conn->prepare("
        SELECT 
            DATE(created_at) as reg_date,
            COUNT(*) as new_users
        FROM users 
        WHERE DATE(created_at) BETWEEN ? AND ?
        AND role = 'customer'
        GROUP BY DATE(created_at)
        ORDER BY reg_date DESC
    ");
    $stmt->execute([$start_date, $end_date]);
    $user_registrations = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $user_registrations = [];
}
?>

<?php
$page = 'reports';
$page_title = 'Reports & Analytics';

// Include header
require_once 'includes/header.php';
?>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,.1);
    }
</style>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chart-bar me-2"></i>Reports & Analytics</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="dashboard.php">Dashboard</a></li>
                            <li class="breadcrumb-item active">Reports</li>
                        </ol>
                    </nav>
                </div>

                <!-- Date Range Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3 align-items-end">
                            <div class="col-md-4">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-2"></i>Filter
                                </button>
                                <a href="reports.php" class="btn btn-outline-secondary">Reset</a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h3 class="text-primary mb-3"><?php echo $summary['total_orders'] ?? 0; ?></h3>
                            <p class="text-muted mb-0">
                                <i class="fas fa-shopping-cart me-2"></i>Total Orders
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h3 class="text-success mb-3">Rp <?php echo number_format($summary['total_revenue'] ?? 0, 0, ',', '.'); ?></h3>
                            <p class="text-muted mb-0">
                                <i class="fas fa-money-bill-wave me-2"></i>Total Revenue
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h3 class="text-info mb-3">Rp <?php echo number_format($summary['avg_order_value'] ?? 0, 0, ',', '.'); ?></h3>
                            <p class="text-muted mb-0">
                                <i class="fas fa-calculator me-2"></i>Avg Order Value
                            </p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <h3 class="text-warning mb-3"><?php echo $summary['unique_customers'] ?? 0; ?></h3>
                            <p class="text-muted mb-0">
                                <i class="fas fa-users me-2"></i>Unique Customers
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Sales Chart -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Daily Sales</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Top Products -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Products</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($top_products)): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($top_products as $index => $product): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                                <div>
                                                    <span class="badge bg-primary rounded-pill me-2"><?php echo $index + 1; ?></span>
                                                    <strong><?php echo htmlspecialchars($product['product_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo $product['total_sold']; ?> sold • 
                                                        Rp <?php echo number_format($product['total_revenue'], 0, ',', '.'); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted text-center">No sales data available for this period.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Daily Sales Table -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>Daily Sales Report</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Orders</th>
                                        <th>Revenue</th>
                                        <th>Avg Order Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($daily_sales)): ?>
                                        <?php foreach ($daily_sales as $day): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($day['order_date'])); ?></td>
                                                <td><?php echo $day['total_orders']; ?></td>
                                                <td>Rp <?php echo number_format($day['total_sales'], 0, ',', '.'); ?></td>
                                                <td>Rp <?php echo number_format($day['avg_order_value'], 0, ',', '.'); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted">No sales data available for this period.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sales Chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesData = <?php echo json_encode(array_reverse($daily_sales)); ?>;
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: salesData.map(item => new Date(item.order_date).toLocaleDateString()),
                datasets: [{
                    label: 'Revenue (Rp)',
                    data: salesData.map(item => item.total_sales),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1,
                    fill: true
                }, {
                    label: 'Orders',
                    data: salesData.map(item => item.total_orders),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Revenue (Rp)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Orders'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });
    </script>
</body>
</html>
