// Firebase Configuration for Tewuneed Marketplace
// This file contains the Firebase configuration and initialization

// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDz5t6mlBzXq7bjK3PGOGyEBo_WzjsHYME",
    authDomain: "tewuneed-marketplace.firebaseapp.com",
    projectId: "tewuneed-marketplace",
    storageBucket: "tewuneed-marketplace.appspot.com",
    messagingSenderId: "999093621738",
    appId: "1:999093621738:web:87b68aa3a5a5ebca395893",
    measurementId: "G-8WNLD8T7GY"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = firebase.auth();

// Initialize Firestore only if available
let db = null;
try {
    if (firebase.firestore) {
        db = firebase.firestore();
        console.log('Firebase Firestore initialized');
    } else {
        console.warn('Firebase Firestore not available - some features may be limited');
    }
} catch (error) {
    console.warn('Firebase Firestore initialization failed:', error.message);
}

// Configure Firebase Auth settings
auth.useDeviceLanguage();

// Set persistence to LOCAL (session persists across browser sessions)
auth.setPersistence(firebase.auth.Auth.Persistence.LOCAL)
    .then(() => {
        console.log('Firebase Auth persistence set to LOCAL');
    })
    .catch((error) => {
        console.error('Error setting Firebase Auth persistence:', error);
    });

// Auth state observer
auth.onAuthStateChanged((user) => {
    if (user) {
        console.log('User is signed in:', user.email);
        
        // Sync user session with backend if not already done
        if (!sessionStorage.getItem('firebase_session_synced')) {
            syncFirebaseSession(user);
        }
    } else {
        console.log('User is signed out');
        // Clear session sync flag
        sessionStorage.removeItem('firebase_session_synced');
    }
});

// Function to sync Firebase user with backend session
function syncFirebaseSession(user) {
    // Determine user role (customize this logic as needed)
    let userRole = 'customer';
    if (user.email === '<EMAIL>' || user.email === '<EMAIL>') {
        userRole = 'admin';
    }

    fetch('ajax/firebase_login.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            uid: user.uid,
            email: user.email,
            displayName: user.displayName || user.email.split('@')[0],
            role: userRole,
            photoURL: user.photoURL || ''
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Firebase session synced successfully');
            sessionStorage.setItem('firebase_session_synced', 'true');
        } else {
            console.error('Failed to sync Firebase session:', data.message);
        }
    })
    .catch(error => {
        console.error('Error syncing Firebase session:', error);
    });
}

// Function to handle Firebase logout
function firebaseLogout() {
    return auth.signOut().then(() => {
        console.log('User signed out from Firebase');
        
        // Clear session sync flag
        sessionStorage.removeItem('firebase_session_synced');
        
        // Clear backend session
        return fetch('logout.php', {
            method: 'POST',
            credentials: 'same-origin'
        });
    }).then(() => {
        // Redirect to login page
        window.location.href = 'login.php';
    }).catch((error) => {
        console.error('Error during logout:', error);
    });
}

// Function to get current Firebase user
function getCurrentFirebaseUser() {
    return auth.currentUser;
}

// Function to check if user is authenticated
function isFirebaseAuthenticated() {
    return auth.currentUser !== null;
}

// Export functions for global use
window.firebaseAuth = {
    auth: auth,
    db: db,
    logout: firebaseLogout,
    getCurrentUser: getCurrentFirebaseUser,
    isAuthenticated: isFirebaseAuthenticated,
    syncSession: syncFirebaseSession
};
