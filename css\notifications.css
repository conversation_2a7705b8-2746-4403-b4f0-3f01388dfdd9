/**
 * Custom Notification Styles
 * Provides styling for the notification system
 */

#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    pointer-events: none;
    max-width: 400px;
}

.notification {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    padding: 0;
    pointer-events: auto;
    transform: translateX(100%);
    animation: slideIn 0.3s ease-out forwards;
    border-left: 4px solid;
    max-width: 100%;
    word-wrap: break-word;
}

.notification-success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.notification-danger,
.notification-error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.notification-warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.notification-info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.4;
}

.notification-message {
    flex: 1;
    margin-right: 10px;
    color: #333;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    font-weight: bold;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #333;
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification.removing {
    animation: slideOut 0.3s ease-in forwards;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    #notification-container {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .notification {
        margin-bottom: 8px;
    }
    
    .notification-content {
        padding: 10px 12px;
        font-size: 13px;
    }
}

/* Enhanced cart notification styles */
.notification.cart-notification {
    border-left-width: 6px;
}

.notification.cart-notification .notification-message {
    font-weight: 600;
    color: #155724;
}

.notification.cart-notification::before {
    content: "🛒";
    margin-right: 8px;
    font-size: 16px;
}

/* Toastify override styles for consistency */
.toastify {
    font-family: inherit !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.toastify.on {
    animation: toastify-slide-in 0.3s ease-out !important;
}

@keyframes toastify-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Success notification with icon */
.notification-success .notification-message::before {
    content: "✓";
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #28a745;
    color: white;
    border-radius: 50%;
    text-align: center;
    font-size: 10px;
    line-height: 16px;
    margin-right: 8px;
    font-weight: bold;
}

/* Error notification with icon */
.notification-danger .notification-message::before,
.notification-error .notification-message::before {
    content: "✕";
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    text-align: center;
    font-size: 10px;
    line-height: 16px;
    margin-right: 8px;
    font-weight: bold;
}

/* Warning notification with icon */
.notification-warning .notification-message::before {
    content: "⚠";
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #ffc107;
    color: #333;
    border-radius: 50%;
    text-align: center;
    font-size: 10px;
    line-height: 16px;
    margin-right: 8px;
    font-weight: bold;
}

/* Info notification with icon */
.notification-info .notification-message::before {
    content: "i";
    display: inline-block;
    width: 16px;
    height: 16px;
    background-color: #17a2b8;
    color: white;
    border-radius: 50%;
    text-align: center;
    font-size: 10px;
    line-height: 16px;
    margin-right: 8px;
    font-weight: bold;
}

/* Stacked notifications spacing */
.notification:nth-child(1) { z-index: 1000; }
.notification:nth-child(2) { z-index: 999; margin-top: -5px; }
.notification:nth-child(3) { z-index: 998; margin-top: -10px; }
.notification:nth-child(n+4) { display: none; } /* Hide excess notifications */

/* Hover effects */
.notification:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

.notification:hover .notification-close {
    opacity: 1;
}

/* Focus states for accessibility */
.notification-close:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}
