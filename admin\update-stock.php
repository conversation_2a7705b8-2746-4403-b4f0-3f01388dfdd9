<?php
// Aktifkan error reporting untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Mulai sesi jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set header untuk JSON response
header('Content-Type: application/json');

require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Cek apakah user adalah admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

$conn = getDbConnection();

// Cek permintaan AJAX untuk memperbarui stok
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ambil data dari request AJAX
    $product_id = isset($_POST['product_id']) ? filter_var($_POST['product_id'], FILTER_VALIDATE_INT) : 0;
    $new_stock = isset($_POST['new_stock']) ? filter_var($_POST['new_stock'], FILTER_VALIDATE_INT) : -1;
    
    // Validasi input
    if (!$product_id || $new_stock < 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Input tidak valid. Stok harus berupa angka positif.'
        ]);
        exit;
    }
    
    try {
        // Mulai transaksi
        $conn->beginTransaction();
        
        // Dapatkan stok saat ini
        $stmt = $conn->prepare("SELECT stock, name FROM products WHERE product_id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            throw new Exception('Produk tidak ditemukan.');
        }
        
        // Update stok
        $stmt = $conn->prepare("UPDATE products SET stock = ?, updated_at = NOW() WHERE product_id = ?");
        $stmt->execute([$new_stock, $product_id]);
        
        // Log perubahan stok jika fungsi tersedia
        if (function_exists('logActivity')) {
            $details = "Perubahan stok produk '{$product['name']}' dari {$product['stock']} menjadi {$new_stock}";
            logActivity($_SESSION['user_id'], 'stock_update', $details);
        }
        
        // Commit transaksi
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => "Stok produk '{$product['name']}' berhasil diperbarui dari {$product['stock']} menjadi {$new_stock}",
            'old_stock' => $product['stock'],
            'new_stock' => $new_stock,
            'product_id' => $product_id
        ]);
        
    } catch (Exception $e) {
        // Rollback transaksi
        $conn->rollBack();
        
        echo json_encode([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ]);
    }
    
    exit; // Akhiri eksekusi setelah mengirim respons JSON
}

// Dapatkan semua produk
$stmt = $conn->prepare("
    SELECT p.product_id, p.NAME, p.price, p.stock, p.is_active, c.name AS category_name 
    FROM products p 
    JOIN categories c ON p.category_id = c.category_id 
    ORDER BY p.NAME ASC
");
$stmt->execute();
$products = $stmt->fetchAll();
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Update Product Stock</h5>
                </div>
                <div class="card-body">
                    <?php if ($status_message): ?>
                    <div class="alert <?php echo $status_class; ?> alert-dismissible fade show" role="alert">
                        <?php echo $status_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="productsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nama Produk</th>
                                    <th>Kategori</th>
                                    <th>Harga</th>
                                    <th>Stok Saat Ini</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                <tr>
                                    <td><?php echo $product['product_id']; ?></td>
                                    <td><?php echo htmlspecialchars($product['NAME']); ?></td>
                                    <td><?php echo htmlspecialchars($product['category_name']); ?></td>
                                    <td>Rp <?php echo number_format($product['price'], 0, ',', '.'); ?></td>
                                    <td class="<?php echo $product['stock'] > 0 ? 'text-success' : 'text-danger'; ?>">
                                        <strong><?php echo $product['stock']; ?></strong>
                                    </td>
                                    <td>
                                        <?php if ($product['is_active']): ?>
                                        <span class="badge bg-success">Aktif</span>
                                        <?php else: ?>
                                        <span class="badge bg-danger">Nonaktif</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-primary btn-sm update-stock-btn" 
                                                data-bs-toggle="modal" data-bs-target="#updateStockModal"
                                                data-product-id="<?php echo $product['product_id']; ?>"
                                                data-product-name="<?php echo htmlspecialchars($product['NAME']); ?>"
                                                data-current-stock="<?php echo $product['stock']; ?>">
                                            <i class="fas fa-edit"></i> Update Stok
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Stock Modal -->
<div class="modal fade" id="updateStockModal" tabindex="-1" aria-labelledby="updateStockModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="updateStockModalLabel">Update Stok Produk</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" id="product_id" name="product_id">
                    <div class="mb-3">
                        <label for="product_name" class="form-label">Nama Produk</label>
                        <input type="text" class="form-control" id="product_name" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="current_stock" class="form-label">Stok Saat Ini</label>
                        <input type="text" class="form-control" id="current_stock" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="new_stock" class="form-label">Stok Baru</label>
                        <input type="number" class="form-control" id="new_stock" name="new_stock" min="0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" name="update_stock" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#productsTable').DataTable({
        order: [[0, 'asc']],
        pageLength: 25,
        language: {
            search: "Cari:",
            lengthMenu: "Tampilkan _MENU_ entri",
            info: "Menampilkan _START_ hingga _END_ dari _TOTAL_ entri",
            infoEmpty: "Menampilkan 0 hingga 0 dari 0 entri",
            infoFiltered: "(disaring dari _MAX_ total entri)",
            paginate: {
                first: "Pertama",
                last: "Terakhir",
                next: "Selanjutnya",
                previous: "Sebelumnya"
            }
        }
    });
    
    // Handle update stock button click
    $('.update-stock-btn').click(function() {
        var productId = $(this).data('product-id');
        var productName = $(this).data('product-name');
        var currentStock = $(this).data('current-stock');
        
        $('#product_id').val(productId);
        $('#product_name').val(productName);
        $('#current_stock').val(currentStock);
        $('#new_stock').val(currentStock);
    });
    
    // Form validation
    $('form').submit(function(e) {
        var newStock = $('#new_stock').val();
        if (newStock === '' || parseInt(newStock) < 0) {
            e.preventDefault();
            alert('Stok harus berupa angka positif.');
        }
    });
});
</script>

<?php require_once '../includes/admin_footer.php'; ?>
