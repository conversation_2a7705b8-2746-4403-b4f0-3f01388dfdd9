/* Profile Animations CSS */

/* Ripple effect for buttons */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Button loading animation */
.btn-loading {
    position: relative;
    pointer-events: none;
}

/* Success pulse animation for buttons */
.btn-success-pulse {
    animation: success-pulse 1s ease-in-out;
}

@keyframes success-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Address card hover effects */
.address-card {
    transition: all 0.3s ease;
}

/* Form field animations */
.form-control.is-invalid.animate__shakeX {
    animation-duration: 0.5s;
}

/* Modal animations */
.modal-dialog.animate__fadeInDown {
    animation-duration: 0.5s;
}

/* Tab content animations */
.tab-content.active {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Order history card hover effect */
.order-card {
    transition: all 0.3s ease;
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    border-color: #007bff;
}

/* Profile photo upload hover effect */
.profile-photo-container {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.profile-photo-container:hover .profile-photo-overlay {
    opacity: 1;
}

.profile-photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

.profile-photo-overlay i {
    color: white;
    font-size: 1.5rem;
}