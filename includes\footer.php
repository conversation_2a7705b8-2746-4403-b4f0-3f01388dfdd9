    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white pt-5 pb-4">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h3 class="fw-bold mb-4" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">TEWUNEED</h3>
                    <p class="text-white-50 mb-4">Your trusted e-commerce partner providing quality products, competitive prices, and exceptional customer service since day one.</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-sm rounded-circle" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5 class="fw-bold mb-3">Quick Links</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="index.php" class="text-decoration-none text-white-50 hover-link">Home</a></li>
                        <li class="mb-2"><a href="Products.php" class="text-decoration-none text-white-50 hover-link">Products</a></li>
                        <li class="mb-2"><a href="about.php" class="text-decoration-none text-white-50 hover-link">About Us</a></li>
                        <li class="mb-2"><a href="contact.php" class="text-decoration-none text-white-50 hover-link">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5 class="fw-bold mb-3">Customer Service</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="faq.php" class="text-decoration-none text-white-50 hover-link">FAQs</a></li>
                        <li class="mb-2"><a href="shipping-policy.php" class="text-decoration-none text-white-50 hover-link">Shipping Policy</a></li>
                        <li class="mb-2"><a href="return-policy.php" class="text-decoration-none text-white-50 hover-link">Return Policy</a></li>
                        <li class="mb-2"><a href="terms.php" class="text-decoration-none text-white-50 hover-link">Terms & Conditions</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 mb-4">
                    <h5 class="fw-bold mb-3">Contact Info</h5>
                    <div class="text-white-50">
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-map-marker-alt me-3 text-primary"></i>
                            <span>Aruan, Laguboti, North Sumatra</span>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-phone me-3 text-primary"></i>
                            <span>+62 ************</span>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <i class="fas fa-envelope me-3 text-primary"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-clock me-3 text-primary"></i>
                            <span>Mon - Fri: 9:00 AM - 6:00 PM</span>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="my-4 border-secondary">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-white-50">&copy; 2024 TEWUNEED. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-white-50">Made with <i class="fas fa-heart text-danger"></i> for better shopping experience</p>
                </div>
            </div>
        </div>
    </footer>

    <style>
        .hover-link:hover {
            color: #667eea !important;
            transition: color 0.3s ease;
        }

        /* Ensure dropdown works properly */
        .dropdown-menu {
            z-index: 1050;
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.5rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #495057;
        }

        .dropdown-toggle::after {
            margin-left: 0.5rem;
        }
    </style>

    <!-- Back to Top Button -->
    <a href="#" class="btn btn-primary back-to-top" role="button">
        <i class="fas fa-arrow-up"></i>
    </a>

    <!-- jQuery (load first) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Real-time Client - Disabled due to missing file -->
    <!-- <script src="js/realtime_client.js"></script> -->

    <!-- Custom Scripts -->
    <script>
    // UNIVERSAL DROPDOWN FIX - Works on all pages
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 Page loaded - Universal dropdown fix');

        // Wait for Bootstrap to be fully loaded
        setTimeout(function() {
            const userDropdown = document.getElementById('userDropdown');

            if (userDropdown) {
                console.log('✅ User dropdown found');

                // Remove existing event listeners by cloning
                const newDropdown = userDropdown.cloneNode(true);
                userDropdown.parentNode.replaceChild(newDropdown, userDropdown);

                // Add simple click handler
                newDropdown.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('🖱️ Dropdown clicked');

                    const menu = this.nextElementSibling;
                    if (menu && menu.classList.contains('dropdown-menu')) {

                        // Close all other dropdowns
                        document.querySelectorAll('.dropdown-menu.show').forEach(function(otherMenu) {
                            if (otherMenu !== menu) {
                                otherMenu.classList.remove('show');
                            }
                        });

                        // Toggle this dropdown
                        if (menu.classList.contains('show')) {
                            menu.classList.remove('show');
                            this.setAttribute('aria-expanded', 'false');
                            console.log('📤 Dropdown closed');
                        } else {
                            menu.classList.add('show');
                            this.setAttribute('aria-expanded', 'true');
                            console.log('📥 Dropdown opened');
                        }
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!newDropdown.contains(e.target)) {
                        const menu = newDropdown.nextElementSibling;
                        if (menu && menu.classList.contains('show')) {
                            menu.classList.remove('show');
                            newDropdown.setAttribute('aria-expanded', 'false');
                            console.log('📤 Dropdown closed (outside click)');
                        }
                    }
                });

                console.log('✅ Universal dropdown handler attached');

            } else {
                console.log('ℹ️ User dropdown not found - user may not be logged in');
            }
        }, 300);

        // Verify Bootstrap is loaded
        if (typeof bootstrap !== 'undefined') {
            console.log('✅ Bootstrap is loaded');
        } else {
            console.log('❌ Bootstrap not loaded');
        }

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // Back to top button
        var backToTopButton = document.querySelector('.back-to-top');

        if (backToTopButton) {
            backToTopButton.style.display = 'none';

            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.style.display = 'block';
                } else {
                    backToTopButton.style.display = 'none';
                }
            });

            backToTopButton.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({top: 0, behavior: 'smooth'});
            });
        }
    });
    </script>

    <!-- Page Specific Scripts -->
    <?php if (isset($page_scripts)): ?>
    <?php echo $page_scripts; ?>
    <?php endif; ?>

    <!-- Customer Real-time Widget -->
    <?php if (isset($_SESSION['user_id']) && !isset($_SESSION['admin_logged_in'])): ?>
    <?php include 'customer_realtime_widget.php'; ?>
    <?php endif; ?>
</body>
</html>