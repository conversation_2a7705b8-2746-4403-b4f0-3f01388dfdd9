// Model Produk (models/Product.js)
const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: { type: String, required: true },
  price: { type: Number, required: true },
  stock: { type: Number, required: true, default: 0 },
  image: { type: String, required: true },
  category: { type: String, required: true },
  rating: { type: Number, default: 4.0 },
  description: { type: String },
  details: {
    weight: String,
    dimensions: String,
    ingredients: String,
    nutrition: Object
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

module.exports = mongoose.model('Product', productSchema);