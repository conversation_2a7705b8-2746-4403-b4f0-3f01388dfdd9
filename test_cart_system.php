<?php
/**
 * TEWUNEED - Cart System Testing
 * Comprehensive testing for shopping cart functionality
 */

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

// Test configuration
$test_user_id = 999999; // Test user ID
$test_product_id = 1;   // Test product ID
$test_results = [];
$total_tests = 0;
$passed_tests = 0;

/**
 * Test helper function
 */
function runTest($test_name, $test_function) {
    global $test_results, $total_tests, $passed_tests;
    
    $total_tests++;
    $start_time = microtime(true);
    
    try {
        $result = $test_function();
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        
        if ($result['success']) {
            $passed_tests++;
            $test_results[] = [
                'name' => $test_name,
                'status' => 'PASS',
                'message' => $result['message'],
                'time' => $execution_time . 'ms',
                'details' => $result['details'] ?? null
            ];
        } else {
            $test_results[] = [
                'name' => $test_name,
                'status' => 'FAIL',
                'message' => $result['message'],
                'time' => $execution_time . 'ms',
                'error' => $result['error'] ?? null
            ];
        }
    } catch (Exception $e) {
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        $test_results[] = [
            'name' => $test_name,
            'status' => 'ERROR',
            'message' => 'Test execution failed',
            'time' => $execution_time . 'ms',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 1: Database Connection
 */
function testDatabaseConnection() {
    global $conn;
    
    try {
        $stmt = $conn->query("SELECT 1");
        return [
            'success' => true,
            'message' => 'Database connection successful',
            'details' => 'Connection established and query executed'
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 2: Cart Table Structure
 */
function testCartTableStructure() {
    global $conn;
    
    try {
        $stmt = $conn->query("DESCRIBE cart");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $required_columns = ['cart_id', 'user_id', 'product_id', 'quantity', 'created_at'];
        $existing_columns = array_column($columns, 'Field');
        
        $missing_columns = array_diff($required_columns, $existing_columns);
        
        if (empty($missing_columns)) {
            return [
                'success' => true,
                'message' => 'Cart table structure is valid',
                'details' => 'All required columns present: ' . implode(', ', $existing_columns)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Cart table structure incomplete',
                'error' => 'Missing columns: ' . implode(', ', $missing_columns)
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to check cart table structure',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 3: Add Item to Cart
 */
function testAddToCart() {
    global $conn, $test_user_id, $test_product_id;
    
    try {
        // Clean up any existing test data
        $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ? AND product_id = ?");
        $stmt->execute([$test_user_id, $test_product_id]);
        
        // Add item to cart
        $stmt = $conn->prepare("
            INSERT INTO cart (user_id, product_id, quantity, created_at) 
            VALUES (?, ?, 1, NOW())
        ");
        $result = $stmt->execute([$test_user_id, $test_product_id]);
        
        if ($result) {
            // Verify item was added
            $stmt = $conn->prepare("SELECT * FROM cart WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$test_user_id, $test_product_id]);
            $cart_item = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($cart_item) {
                return [
                    'success' => true,
                    'message' => 'Item successfully added to cart',
                    'details' => "Cart ID: {$cart_item['cart_id']}, Quantity: {$cart_item['quantity']}"
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Item not found in cart after insertion',
                    'error' => 'Verification failed'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Failed to add item to cart',
                'error' => 'Insert operation failed'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Add to cart test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 4: Update Cart Quantity
 */
function testUpdateCartQuantity() {
    global $conn, $test_user_id, $test_product_id;
    
    try {
        // Update quantity
        $new_quantity = 3;
        $stmt = $conn->prepare("
            UPDATE cart SET quantity = ?, updated_at = NOW() 
            WHERE user_id = ? AND product_id = ?
        ");
        $result = $stmt->execute([$new_quantity, $test_user_id, $test_product_id]);
        
        if ($result) {
            // Verify update
            $stmt = $conn->prepare("SELECT quantity FROM cart WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$test_user_id, $test_product_id]);
            $quantity = $stmt->fetchColumn();
            
            if ($quantity == $new_quantity) {
                return [
                    'success' => true,
                    'message' => 'Cart quantity updated successfully',
                    'details' => "New quantity: $quantity"
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Quantity update verification failed',
                    'error' => "Expected: $new_quantity, Got: $quantity"
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Failed to update cart quantity',
                'error' => 'Update operation failed'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Update cart quantity test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 5: Remove Item from Cart
 */
function testRemoveFromCart() {
    global $conn, $test_user_id, $test_product_id;
    
    try {
        // Remove item
        $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ? AND product_id = ?");
        $result = $stmt->execute([$test_user_id, $test_product_id]);
        
        if ($result) {
            // Verify removal
            $stmt = $conn->prepare("SELECT COUNT(*) FROM cart WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$test_user_id, $test_product_id]);
            $count = $stmt->fetchColumn();
            
            if ($count == 0) {
                return [
                    'success' => true,
                    'message' => 'Item successfully removed from cart',
                    'details' => 'Cart item count: 0'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Item removal verification failed',
                    'error' => "Items still in cart: $count"
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Failed to remove item from cart',
                'error' => 'Delete operation failed'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Remove from cart test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 6: Cart Count Function
 */
function testCartCount() {
    global $conn, $test_user_id;
    
    try {
        // Add multiple items
        $test_items = [
            ['product_id' => 1, 'quantity' => 2],
            ['product_id' => 2, 'quantity' => 1],
            ['product_id' => 3, 'quantity' => 3]
        ];
        
        // Clean up first
        $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ?");
        $stmt->execute([$test_user_id]);
        
        // Add test items
        foreach ($test_items as $item) {
            $stmt = $conn->prepare("
                INSERT INTO cart (user_id, product_id, quantity, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$test_user_id, $item['product_id'], $item['quantity']]);
        }
        
        // Test count
        $stmt = $conn->prepare("SELECT SUM(quantity) as total FROM cart WHERE user_id = ?");
        $stmt->execute([$test_user_id]);
        $total_count = $stmt->fetchColumn();
        
        $expected_count = array_sum(array_column($test_items, 'quantity'));
        
        // Clean up
        $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ?");
        $stmt->execute([$test_user_id]);
        
        if ($total_count == $expected_count) {
            return [
                'success' => true,
                'message' => 'Cart count function working correctly',
                'details' => "Total items: $total_count"
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Cart count mismatch',
                'error' => "Expected: $expected_count, Got: $total_count"
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Cart count test failed',
            'error' => $e->getMessage()
        ];
    }
}

// Run all tests
runTest('Database Connection', 'testDatabaseConnection');
runTest('Cart Table Structure', 'testCartTableStructure');
runTest('Add Item to Cart', 'testAddToCart');
runTest('Update Cart Quantity', 'testUpdateCartQuantity');
runTest('Remove Item from Cart', 'testRemoveFromCart');
runTest('Cart Count Function', 'testCartCount');

// Calculate success rate
$success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEWUNEED - Cart System Test Results</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-error { color: #fd7e14; }
        .test-card { margin-bottom: 1rem; }
        .success-rate {
            font-size: 2rem;
            font-weight: bold;
        }
        .success-high { color: #28a745; }
        .success-medium { color: #ffc107; }
        .success-low { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4">
                        <i class="fas fa-shopping-cart me-3"></i>
                        Cart System Test Results
                    </h1>
                    <p class="lead">Comprehensive testing of shopping cart functionality</p>
                </div>

                <!-- Summary Card -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-md-3">
                                <h5>Total Tests</h5>
                                <span class="display-6"><?php echo $total_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Passed</h5>
                                <span class="display-6 test-pass"><?php echo $passed_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Failed</h5>
                                <span class="display-6 test-fail"><?php echo $total_tests - $passed_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Success Rate</h5>
                                <span class="success-rate <?php 
                                    echo $success_rate >= 80 ? 'success-high' : 
                                         ($success_rate >= 60 ? 'success-medium' : 'success-low'); 
                                ?>"><?php echo $success_rate; ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="row">
                    <?php foreach ($test_results as $test): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card test-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><?php echo htmlspecialchars($test['name']); ?></h6>
                                <span class="badge <?php 
                                    echo $test['status'] === 'PASS' ? 'bg-success' : 
                                         ($test['status'] === 'FAIL' ? 'bg-danger' : 'bg-warning'); 
                                ?>">
                                    <?php echo $test['status']; ?>
                                </span>
                            </div>
                            <div class="card-body">
                                <p class="mb-2">
                                    <i class="fas <?php 
                                        echo $test['status'] === 'PASS' ? 'fa-check-circle test-pass' : 
                                             ($test['status'] === 'FAIL' ? 'fa-times-circle test-fail' : 'fa-exclamation-triangle test-error'); 
                                    ?> me-2"></i>
                                    <?php echo htmlspecialchars($test['message']); ?>
                                </p>
                                
                                <?php if (isset($test['details'])): ?>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <?php echo htmlspecialchars($test['details']); ?>
                                </small>
                                <?php endif; ?>
                                
                                <?php if (isset($test['error'])): ?>
                                <div class="alert alert-danger alert-sm mt-2 mb-0">
                                    <small><strong>Error:</strong> <?php echo htmlspecialchars($test['error']); ?></small>
                                </div>
                                <?php endif; ?>
                                
                                <div class="text-end mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo $test['time']; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary me-2">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                    <a href="admin/dashboard.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                    </a>
                    <button onclick="window.location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-2"></i>Run Tests Again
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
