<?php
require_once '../includes/autoload.php';

header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

// Handle different request methods
switch ($method) {
    case 'GET':
        try {
            $db = get_db_connection();
            
            if (isset($_GET['id'])) {
                // Get single promotion
                $stmt = $db->prepare("
                    SELECT p.*, 
                           (SELECT COUNT(*) FROM promotion_products pp WHERE pp.promotion_id = p.id) as product_count
                    FROM promotions p
                    WHERE p.id = ? AND p.status = 'active'
                ");
                $stmt->execute([$_GET['id']]);
                $promotion = $stmt->fetch();

                if ($promotion) {
                    // Get promotion products
                    $stmt = $db->prepare("
                        SELECT p.*, pp.discount_percentage
                        FROM products p
                        JOIN promotion_products pp ON p.id = pp.product_id
                        WHERE pp.promotion_id = ? AND p.status = 'active'
                    ");
                    $stmt->execute([$promotion['id']]);
                    $promotion['products'] = $stmt->fetchAll();

                    echo json_encode(['success' => true, 'data' => $promotion]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Promotion not found']);
                }
            } else {
                // Get all active promotions
                $stmt = $db->prepare("
                    SELECT p.*, 
                           (SELECT COUNT(*) FROM promotion_products pp WHERE pp.promotion_id = p.id) as product_count
                    FROM promotions p
                    WHERE p.status = 'active'
                    AND p.start_date <= NOW()
                    AND p.end_date >= NOW()
                    ORDER BY p.start_date DESC
                ");
                $stmt->execute();
                $promotions = $stmt->fetchAll();

                echo json_encode(['success' => true, 'data' => $promotions]);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Database error']);
        }
        break;

    case 'POST':
        // Add new promotion (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['name']) || !isset($data['start_date']) || !isset($data['end_date'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Name, start date and end date are required']);
            break;
        }

        try {
            $db = get_db_connection();
            $db->beginTransaction();

            // Create promotion
            $stmt = $db->prepare("
                INSERT INTO promotions (name, description, start_date, end_date, status)
                VALUES (?, ?, ?, ?, 'active')
            ");
            $result = $stmt->execute([
                $data['name'],
                $data['description'] ?? '',
                $data['start_date'],
                $data['end_date']
            ]);

            if (!$result) {
                throw new PDOException('Failed to create promotion');
            }

            $promotion_id = $db->lastInsertId();

            // Add promotion products
            if (isset($data['products']) && is_array($data['products'])) {
                $stmt = $db->prepare("
                    INSERT INTO promotion_products (promotion_id, product_id, discount_percentage)
                    VALUES (?, ?, ?)
                ");

                foreach ($data['products'] as $product) {
                    if (!isset($product['product_id']) || !isset($product['discount_percentage'])) {
                        continue;
                    }

                    $stmt->execute([
                        $promotion_id,
                        $product['product_id'],
                        $product['discount_percentage']
                    ]);
                }
            }

            $db->commit();

            echo json_encode([
                'success' => true,
                'message' => 'Promotion added successfully',
                'data' => ['id' => $promotion_id]
            ]);
        } catch (PDOException $e) {
            $db->rollBack();
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to add promotion']);
        }
        break;

    case 'PUT':
        // Update promotion (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Promotion ID is required']);
            break;
        }

        try {
            $db = get_db_connection();
            $db->beginTransaction();

            // Update promotion details
            $updates = [];
            $params = [];

            $fields = ['name', 'description', 'start_date', 'end_date', 'status'];
            foreach ($fields as $field) {
                if (isset($data[$field])) {
                    $updates[] = "$field = ?";
                    $params[] = $data[$field];
                }
            }

            if (!empty($updates)) {
                $params[] = $data['id'];
                $query = "UPDATE promotions SET " . implode(', ', $updates) . " WHERE id = ?";
                
                $stmt = $db->prepare($query);
                if (!$stmt->execute($params)) {
                    throw new PDOException('Failed to update promotion');
                }
            }

            // Update promotion products
            if (isset($data['products']) && is_array($data['products'])) {
                // Remove existing products
                $stmt = $db->prepare("DELETE FROM promotion_products WHERE promotion_id = ?");
                $stmt->execute([$data['id']]);

                // Add new products
                $stmt = $db->prepare("
                    INSERT INTO promotion_products (promotion_id, product_id, discount_percentage)
                    VALUES (?, ?, ?)
                ");

                foreach ($data['products'] as $product) {
                    if (!isset($product['product_id']) || !isset($product['discount_percentage'])) {
                        continue;
                    }

                    $stmt->execute([
                        $data['id'],
                        $product['product_id'],
                        $product['discount_percentage']
                    ]);
                }
            }

            $db->commit();
            echo json_encode(['success' => true, 'message' => 'Promotion updated successfully']);
        } catch (PDOException $e) {
            $db->rollBack();
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update promotion']);
        }
        break;

    case 'DELETE':
        // Delete promotion (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Promotion ID is required']);
            break;
        }

        try {
            $db = get_db_connection();
            $db->beginTransaction();

            // Remove promotion products
            $stmt = $db->prepare("DELETE FROM promotion_products WHERE promotion_id = ?");
            $stmt->execute([$data['id']]);

            // Soft delete promotion
            $stmt = $db->prepare("UPDATE promotions SET status = 'deleted' WHERE id = ?");
            $result = $stmt->execute([$data['id']]);

            if ($result) {
                $db->commit();
                echo json_encode(['success' => true, 'message' => 'Promotion deleted successfully']);
            } else {
                throw new PDOException('Failed to delete promotion');
            }
        } catch (PDOException $e) {
            $db->rollBack();
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete promotion']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
} 