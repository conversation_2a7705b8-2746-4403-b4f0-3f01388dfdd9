<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

try {
    $conn = getConnection();
    
    $stmt = $conn->prepare("
        SELECT 
            p.promotion_id,
            p.name,
            p.description,
            p.discount_type,
            p.discount_value,
            p.start_date,
            p.end_date
        FROM promotions p
        WHERE p.is_active = 1
        AND p.start_date <= CURRENT_TIMESTAMP
        AND p.end_date >= CURRENT_TIMESTAMP
        ORDER BY p.end_date ASC
    ");
    $stmt->execute();
    $promotions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'success',
        'data' => $promotions
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 