<?php
session_start();
header('Content-Type: application/json');

// Enable CORS for development
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Check if user is logged in
if (isset($_SESSION['user_id'])) {
    echo json_encode([
        'status' => 'success',
        'data' => [
            'username' => $_SESSION['username'] ?? 'User',
            'user_id' => $_SESSION['user_id']
        ]
    ]);
} else {
    echo json_encode([
        'status' => 'error',
        'message' => 'Not logged in'
    ]);
}
?> 