<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

session_start();

try {
    if (!isset($_SESSION['user'])) {
        throw new Exception('User not logged in');
    }
    
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['product_id'])) {
        throw new Exception('Product ID is required');
    }
    
    $conn = getConnection();
    
    // Get user's wishlist
    $stmt = $conn->prepare("
        SELECT wishlist_id
        FROM wishlists
        WHERE user_id = ?
        LIMIT 1
    ");
    $stmt->execute([$_SESSION['user']['id']]);
    $wishlist = $stmt->fetch();
    
    if (!$wishlist) {
        // Create new wishlist if doesn't exist
        $stmt = $conn->prepare("
            INSERT INTO wishlists (user_id)
            VALUES (?)
        ");
        $stmt->execute([$_SESSION['user']['id']]);
        $wishlistId = $conn->lastInsertId();
    } else {
        $wishlistId = $wishlist['wishlist_id'];
    }
    
    // Check if product exists
    $stmt = $conn->prepare("
        SELECT product_id 
        FROM products 
        WHERE product_id = ? 
        AND is_active = 1
    ");
    $stmt->execute([$data['product_id']]);
    if (!$stmt->fetch()) {
        throw new Exception('Product not found');
    }
    
    // Check if product is already in wishlist
    $stmt = $conn->prepare("
        SELECT wishlist_item_id
        FROM wishlist_items
        WHERE wishlist_id = ? AND product_id = ?
    ");
    $stmt->execute([$wishlistId, $data['product_id']]);
    if ($stmt->fetch()) {
        throw new Exception('Product already in wishlist');
    }
    
    // Add product to wishlist
    $stmt = $conn->prepare("
        INSERT INTO wishlist_items (wishlist_id, product_id)
        VALUES (?, ?)
    ");
    $stmt->execute([$wishlistId, $data['product_id']]);
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Product added to wishlist'
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 