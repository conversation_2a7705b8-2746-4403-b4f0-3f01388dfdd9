/**
 * Unified Order Status Synchronization System
 * Handles real-time status updates between admin and user interfaces
 */

class UnifiedStatusSync {
    constructor(options = {}) {
        this.options = {
            debug: options.debug || false,
            checkInterval: options.checkInterval || 5000, // 5 seconds
            syncKey: 'tewuneed_unified_status_sync',
            lastCheckKey: 'tewuneed_last_status_check',
            ...options
        };
        
        this.isAdmin = window.location.pathname.includes('/admin/');
        this.isUserPanel = window.location.pathname.includes('my-orders.php') || 
                          window.location.pathname.includes('order-detail.php');
        this.intervalId = null;
        this.lastCheckTime = this.getLastCheckTime();
        
        this.statusMapping = {
            // Indonesian to English mapping
            'dibuat': 'pending',
            'diproses': 'processing', 
            'dikirim': 'shipped',
            'terkirim': 'delivered',
            'dibatalkan': 'cancelled',
            // English values (keep as is)
            'pending': 'pending',
            'processing': 'processing',
            'shipped': 'shipped', 
            'delivered': 'delivered',
            'cancelled': 'cancelled',
            // Additional mappings
            'paid': 'processing'
        };
        
        this.statusConfig = {
            'pending': { label: 'Pending', color: 'warning', icon: 'fa-clock' },
            'processing': { label: 'Processing', color: 'info', icon: 'fa-cog' },
            'shipped': { label: 'Shipped', color: 'primary', icon: 'fa-truck' },
            'delivered': { label: 'Delivered', color: 'success', icon: 'fa-check-circle' },
            'cancelled': { label: 'Cancelled', color: 'danger', icon: 'fa-times-circle' }
        };
        
        this.init();
    }
    
    init() {
        this.log('Initializing Unified Status Sync', {
            isAdmin: this.isAdmin,
            isUserPanel: this.isUserPanel,
            currentPath: window.location.pathname
        });
        
        // Start monitoring
        this.startMonitoring();
        
        // Setup cross-tab communication
        this.setupStorageListener();
        
        // Process any pending updates
        setTimeout(() => {
            this.processPendingUpdates();
        }, 1000);
        
        // Setup page visibility handling
        this.setupVisibilityHandling();
    }
    
    startMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        
        this.intervalId = setInterval(() => {
            this.checkForUpdates();
        }, this.options.checkInterval);
        
        this.log('Started monitoring for status updates');
    }
    
    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            this.log('Stopped monitoring');
        }
    }
    
    setupStorageListener() {
        window.addEventListener('storage', (e) => {
            if (e.key === this.options.syncKey) {
                this.log('Storage event detected', e);
                this.processPendingUpdates();
            }
        });
        
        // Also listen for custom events (same-tab communication)
        window.addEventListener('orderStatusUpdate', (e) => {
            this.log('Custom status update event', e.detail);
            this.handleStatusUpdate(e.detail);
        });
    }
    
    setupVisibilityHandling() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopMonitoring();
            } else {
                this.startMonitoring();
                // Check immediately when page becomes visible
                setTimeout(() => this.checkForUpdates(), 100);
            }
        });
    }
    
    async checkForUpdates() {
        try {
            // Check localStorage first for immediate updates
            this.processPendingUpdates();
            
            // Then check server for database updates
            if (this.isUserPanel) {
                await this.checkServerUpdates();
            }
        } catch (error) {
            this.log('Error checking for updates', error);
        }
    }
    
    async checkServerUpdates() {
        try {
            const response = await fetch('/tewuneed2/ajax/check-order-updates.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    last_check: this.lastCheckTime
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success && data.updates && data.updates.length > 0) {
                this.log('Server updates received', data.updates);
                data.updates.forEach(update => {
                    this.handleStatusUpdate({
                        order_id: update.order_id,
                        new_status: update.new_status,
                        admin_note: update.admin_note || '',
                        source: 'server',
                        timestamp: new Date(update.updated_at).getTime()
                    });
                });
                this.updateLastCheckTime();
            }
        } catch (error) {
            this.log('Error checking server updates', error);
        }
    }
    
    processPendingUpdates() {
        const updates = this.getStoredUpdates();
        
        Object.keys(updates).forEach(orderId => {
            const update = updates[orderId];
            
            // Only process recent updates (within last 10 minutes)
            const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
            if (update.timestamp > tenMinutesAgo && !update.processed) {
                this.handleStatusUpdate(update);
                
                // Mark as processed
                update.processed = true;
                this.storeUpdate(orderId, update);
            }
        });
    }
    
    handleStatusUpdate(update) {
        this.log('Handling status update', update);
        
        const standardizedStatus = this.standardizeStatus(update.new_status);
        
        if (this.isAdmin) {
            this.updateAdminInterface(update.order_id, standardizedStatus, update);
        } else if (this.isUserPanel) {
            this.updateUserInterface(update.order_id, standardizedStatus, update);
        }
    }
    
    updateAdminInterface(orderId, status, update) {
        // Update admin orders page
        const selectElement = document.querySelector(`select[data-order-id="${orderId}"]`);
        if (selectElement && selectElement.value !== status) {
            selectElement.value = status;
            selectElement.classList.add('status-updated');
            
            setTimeout(() => {
                selectElement.classList.remove('status-updated');
            }, 2000);
            
            this.showNotification(`Order #${orderId} status updated to ${status}`, 'success');
        }
        
        // Update admin order detail page
        const statusBadge = document.getElementById(`status-${orderId}`);
        if (statusBadge) {
            this.updateStatusBadge(statusBadge, status);
        }
    }
    
    updateUserInterface(orderId, status, update) {
        // Update user my-orders page
        const statusBadge = document.getElementById(`status-${orderId}`);
        if (statusBadge) {
            this.updateStatusBadge(statusBadge, status);
        }
        
        // Update order detail page
        const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);
        if (orderCard) {
            const badge = orderCard.querySelector('.status-badge');
            if (badge) {
                this.updateStatusBadge(badge, status);
            }
        }
        
        // Show notification to user
        if (update.source === 'admin_update') {
            this.showUserNotification(update);
        }
    }
    
    updateStatusBadge(badge, status) {
        const config = this.statusConfig[status] || this.statusConfig['pending'];
        
        badge.className = `badge bg-${config.color} status-badge status-update-animation`;
        badge.innerHTML = `<i class="fas ${config.icon} me-1"></i>${config.label}`;
        badge.dataset.currentStatus = status;
        
        // Add animation
        setTimeout(() => {
            badge.classList.remove('status-update-animation');
        }, 1000);
    }
    
    showUserNotification(update) {
        const config = this.statusConfig[update.new_status] || this.statusConfig['pending'];
        
        const notification = document.createElement('div');
        notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
        notification.innerHTML = `
            <h6><i class="fas fa-sync-alt me-2"></i>Order Status Updated!</h6>
            <p class="mb-1">Order ${update.order_number || '#' + update.order_id}</p>
            <p class="mb-1">Status: <span class="badge bg-${config.color}">${config.label}</span></p>
            ${update.admin_note ? `<p class="mb-0"><small>${update.admin_note}</small></p>` : ''}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
        notification.innerHTML = `
            <i class="fas fa-sync-alt me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    // Admin function to store status update
    storeStatusUpdate(orderId, newStatus, adminNote = '', adminName = 'Admin') {
        const standardizedStatus = this.standardizeStatus(newStatus);
        
        const update = {
            order_id: orderId,
            new_status: standardizedStatus,
            original_status: newStatus,
            admin_note: adminNote,
            admin_name: adminName,
            timestamp: Date.now(),
            source: 'admin_update',
            processed: false
        };
        
        this.storeUpdate(orderId, update);
        
        // Trigger custom event for same-tab communication
        window.dispatchEvent(new CustomEvent('orderStatusUpdate', {
            detail: update
        }));
        
        this.log('Stored status update', update);
        return update;
    }
    
    storeUpdate(orderId, update) {
        const updates = this.getStoredUpdates();
        updates[orderId] = update;
        localStorage.setItem(this.options.syncKey, JSON.stringify(updates));
    }
    
    getStoredUpdates() {
        try {
            return JSON.parse(localStorage.getItem(this.options.syncKey) || '{}');
        } catch (error) {
            this.log('Error parsing stored updates', error);
            return {};
        }
    }
    
    standardizeStatus(status) {
        return this.statusMapping[status] || status;
    }
    
    getLastCheckTime() {
        return parseInt(localStorage.getItem(this.options.lastCheckKey) || Date.now());
    }
    
    updateLastCheckTime() {
        this.lastCheckTime = Date.now();
        localStorage.setItem(this.options.lastCheckKey, this.lastCheckTime.toString());
    }
    
    log(message, data = null) {
        if (this.options.debug) {
            console.log(`[UnifiedStatusSync] ${message}`, data || '');
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.unifiedStatusSync = new UnifiedStatusSync({
            debug: true
        });
        
        // Add compatibility functions for legacy calls
        window.orderStatusSyncLiveOrderStatus = function(orderId, status) {
            if (window.unifiedStatusSync) {
                return window.unifiedStatusSync.storeStatusUpdate(orderId, status);
            }
            return false;
        };
        
        console.log('[UnifiedStatusSync] Initialized successfully');
    } catch (error) {
        console.error('[UnifiedStatusSync] Failed to initialize:', error);
    }
});
