/*
 * TEWUNEED - Custom CSS Styles
 * Version: 1.0.0
 * Last Updated: May 2025
 */

/* ======= General Styles ======= */
:root {
    --primary: #0d6efd;
    --primary-dark: #0b5ed7;
    --secondary: #6c757d;
    --success: #198754;
    --danger: #dc3545;
    --warning: #ffc107;
    --info: #0dcaf0;
    --light: #f8f9fa;
    --dark: #212529;
    --body-bg: #f8f9fa;
    --body-color: #212529;
    --font-family-sans-serif: 'Poppins', sans-serif;
    --font-family-heading: 'Poppins', sans-serif;
    --transition: all 0.3s ease;
    --box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);
    --box-shadow-sm: 0 .125rem .25rem rgba(0,0,0,.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0,0,0,.175);
    --border-radius: 0.375rem;
    --border-radius-sm: 0.25rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 2rem;
    --border-radius-circle: 50%;
}

body {
    font-family: var(--font-family-sans-serif);
    background-color: var(--body-bg);
    color: var(--body-color);
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: 600;
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
}

/* Buttons */
.btn {
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary);
    color: white;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* ======= Header Styles ======= */
.navbar {
    box-shadow: var(--box-shadow-sm);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem !important;
}

.nav-link.active {
    color: var(--primary) !important;
    font-weight: 600;
}

/* ======= Footer Styles ======= */
.footer {
    background-color: var(--dark);
    color: white;
}

.footer h4, .footer h5 {
    position: relative;
    padding-bottom: 0.8rem;
    margin-bottom: 1.5rem;
}

.footer h4::after, .footer h5::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50px;
    height: 2px;
    background-color: var(--primary);
}

.hover-text-primary:hover {
    color: var(--primary) !important;
}

.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0.8;
    box-shadow: var(--box-shadow);
}

.back-to-top:hover {
    opacity: 1;
}

/* ======= Home Page Styles ======= */
.hero-section {
    padding: 5rem 0;
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(13, 110, 253, 0.2) 100%);
    border-radius: var(--border-radius-lg);
    margin-bottom: 3rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--dark);
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--secondary);
}

.featured-section {
    padding: 4rem 0;
}

.section-title {
    position: relative;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.section-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 80px;
    height: 3px;
    background-color: var(--primary);
}

.section-title.text-center::after {
    left: 50%;
    transform: translateX(-50%);
}

/* ======= Product Card Styles ======= */
.product-card {
    border: none;
    transition: var(--transition);
    margin-bottom: 1.5rem;
    overflow: hidden;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow);
}

.product-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .card-img-top {
    transform: scale(1.05);
}

.product-card .card-body {
    padding: 1.5rem;
}

.product-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    height: 48px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-category {
    color: var(--secondary);
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.product-price {
    font-weight: 700;
    color: var(--primary);
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.product-price .old-price {
    text-decoration: line-through;
    color: var(--secondary);
    font-weight: 400;
    font-size: 1rem;
    margin-right: 0.5rem;
}

.product-rating {
    color: var(--warning);
    margin-bottom: 1rem;
}

.product-card .btn {
    width: 100%;
    margin-top: 0.5rem;
}

/* ======= Product Detail Modal Styles ======= */
.product-detail-modal .modal-content {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.product-detail-modal .modal-header {
    background-color: var(--primary);
    color: white;
    border-bottom: none;
}

.product-detail-modal .modal-body {
    padding: 2rem;
}

.product-detail-img {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
}

.product-detail-title {
    font-weight: 700;
    margin-bottom: 1rem;
}

.product-detail-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 1rem;
}

.product-detail-description {
    margin-bottom: 1.5rem;
}

.product-detail-meta {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 1.5rem;
}

.product-detail-meta-item {
    flex: 0 0 50%;
    margin-bottom: 0.5rem;
}

.product-detail-meta-label {
    font-weight: 600;
    margin-right: 0.5rem;
}

/* ======= About Page Styles ======= */
.about-img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
}

.team-img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    margin-bottom: 1rem;
    border: 5px solid rgba(13, 110, 253, 0.1);
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: var(--primary);
    background-color: rgba(13, 110, 253, 0.1);
    margin-right: 0.5rem;
    transition: var(--transition);
}

.social-icon:hover {
    color: white;
    background-color: var(--primary);
}

/* ======= Contact Page Styles ======= */
.contact-info-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(13, 110, 253, 0.1);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.25rem;
}

.contact-form-wrapper {
    background-color: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 2rem;
}

/* ======= FAQ Page Styles ======= */
.accordion-item {
    border: none;
    margin-bottom: 1rem;
}

.accordion-button {
    background-color: white;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--box-shadow-sm);
    font-weight: 600;
}

.accordion-button:not(.collapsed) {
    color: var(--primary);
    background-color: rgba(13, 110, 253, 0.1);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: rgba(13, 110, 253, 0.25);
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230d6efd'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

/* ======= Cart Page Styles ======= */
.cart-item {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    padding: 1rem;
    margin-bottom: 1rem;
}

.cart-item-img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.cart-summary {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    padding: 1.5rem;
}

.cart-total {
    font-size: 1.25rem;
    font-weight: 700;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0,0,0,0.1);
}

/* ======= Responsive Styles ======= */
@media (max-width: 991.98px) {
    .hero-title {
        font-size: 2.5rem;
    }
}

@media (max-width: 767.98px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .cart-item {
        flex-direction: column;
    }
    
    .cart-item-img {
        margin-bottom: 1rem;
    }
}

@media (max-width: 575.98px) {
    .hero-title {
        font-size: 1.75rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .btn {
        padding: 0.375rem 1rem;
    }
}

/* ======= Animation Keyframes ======= */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fadeIn {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slideInUp {
    animation: slideInUp 0.5s ease-in-out;
}
