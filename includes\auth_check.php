<?php
/**
 * Authentication helper functions
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database connection and auth functions
require_once __DIR__ . '/firebase_auth.php';
require_once __DIR__ . '/db_connect.php';

/**
 * Check if user is logged in
 * Note: Function moved to firebase_auth.php to avoid conflicts
 * Use isLoggedIn() from firebase_auth.php instead
 */

/**
 * Check if current user is an admin (session-based)
 * @return bool True if user is an admin
 */
function isSessionAdmin() {
    if (!isLoggedIn()) {
        return false;
    }

    // Admin check logic - simplified for this example
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

/**
 * Require user to be logged in
 * Redirects to login page if not logged in
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: /login.php');
        exit;
    }
}

/**
 * Require user to be an admin
 * Redirects to dashboard if not an admin
 */
function requireAdmin() {
    if (!isSessionAdmin()) {
        header('Location: /dashboard.php');
        exit;
    }
}
?>
