<!-- Real-time Communication Widget -->
<div id="realtimeWidget" class="position-fixed" style="bottom: 20px; right: 20px; z-index: 1050;">
    <!-- Main Widget Button -->
    <div class="card shadow-lg" style="width: 60px; height: 60px; border-radius: 50%; cursor: pointer;" 
         id="widgetToggle" data-bs-toggle="tooltip" title="Real-time Communication">
        <div class="card-body d-flex align-items-center justify-content-center p-0">
            <i class="fas fa-comments fa-lg text-primary"></i>
            <span id="unreadBadge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="display: none;">
                0
            </span>
        </div>
    </div>

    <!-- Widget Panel -->
    <div id="widgetPanel" class="card shadow-lg mt-2" style="width: 400px; height: 500px; display: none;">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
                <i class="fas fa-broadcast-tower me-2"></i>Real-time Hub
            </h6>
            <div class="d-flex align-items-center">
                <span id="connectionStatus" class="badge bg-success me-2">
                    <i class="fas fa-circle"></i> Online
                </span>
                <button type="button" class="btn-close btn-close-white" id="closeWidget"></button>
            </div>
        </div>

        <div class="card-body p-0">
            <!-- Tabs -->
            <ul class="nav nav-tabs" id="widgetTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="events-tab" data-bs-toggle="tab" data-bs-target="#events" type="button" role="tab">
                        <i class="fas fa-bell me-1"></i>Events
                        <span id="eventsCount" class="badge bg-danger ms-1" style="display: none;">0</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">
                        <i class="fas fa-users me-1"></i>Users
                        <span id="onlineCount" class="badge bg-success ms-1">0</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="broadcast-tab" data-bs-toggle="tab" data-bs-target="#broadcast" type="button" role="tab">
                        <i class="fas fa-bullhorn me-1"></i>Broadcast
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="widgetTabContent" style="height: 400px;">
                <!-- Events Tab -->
                <div class="tab-pane fade show active" id="events" role="tabpanel">
                    <div class="p-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Recent Events</h6>
                            <button class="btn btn-sm btn-outline-primary" id="refreshEvents">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div id="eventsList" style="height: 320px; overflow-y: auto;">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                <p>No recent events</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Tab -->
                <div class="tab-pane fade" id="users" role="tabpanel">
                    <div class="p-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Online Users</h6>
                            <button class="btn btn-sm btn-outline-primary" id="refreshUsers">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                        <div id="usersList" style="height: 320px; overflow-y: auto;">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-user-slash fa-2x mb-2"></i>
                                <p>No users online</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Broadcast Tab -->
                <div class="tab-pane fade" id="broadcast" role="tabpanel">
                    <div class="p-3">
                        <h6 class="mb-3">Send Broadcast</h6>
                        <form id="broadcastForm">
                            <div class="mb-3">
                                <label class="form-label">Target</label>
                                <select class="form-select" name="target_type" required>
                                    <option value="">Select target...</option>
                                    <option value="all">All Users</option>
                                    <option value="user">Specific Customer</option>
                                    <option value="admin">All Admins</option>
                                </select>
                            </div>
                            <div class="mb-3" id="specificUserDiv" style="display: none;">
                                <label class="form-label">User ID</label>
                                <input type="number" class="form-control" name="target_id" placeholder="Enter user ID">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Priority</label>
                                <select class="form-select" name="priority">
                                    <option value="normal">Normal</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Title</label>
                                <input type="text" class="form-control" name="title" placeholder="Notification title" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Message</label>
                                <textarea class="form-control" name="message" rows="3" placeholder="Your message..." required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-paper-plane me-2"></i>Send Broadcast
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Notifications Container -->
<div id="realtime-notifications" class="position-fixed" style="top: 20px; right: 20px; z-index: 1060; max-width: 350px;"></div>

<!-- Real-time Widget Styles -->
<style>
#realtimeWidget .card {
    transition: all 0.3s ease;
}

#realtimeWidget .card:hover {
    transform: scale(1.05);
}

#widgetPanel {
    animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.event-item {
    border-left: 4px solid #007bff;
    transition: all 0.2s ease;
}

.event-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.event-item.priority-high {
    border-left-color: #fd7e14;
}

.event-item.priority-urgent {
    border-left-color: #dc3545;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.user-item {
    transition: all 0.2s ease;
}

.user-item:hover {
    background-color: #f8f9fa;
}

.status-online { color: #28a745; }
.status-away { color: #ffc107; }
.status-offline { color: #6c757d; }

#realtime-notifications .alert {
    margin-bottom: 10px;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
</style>

<!-- Real-time Widget JavaScript -->
<script>
class RealtimeWidget {
    constructor() {
        this.isOpen = false;
        this.client = null;
        this.events = [];
        this.users = [];
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initRealtimeClient();
    }
    
    setupEventListeners() {
        // Toggle widget
        document.getElementById('widgetToggle').addEventListener('click', () => {
            this.toggle();
        });
        
        // Close widget
        document.getElementById('closeWidget').addEventListener('click', () => {
            this.close();
        });
        
        // Refresh buttons
        document.getElementById('refreshEvents').addEventListener('click', () => {
            this.refreshEvents();
        });
        
        document.getElementById('refreshUsers').addEventListener('click', () => {
            this.refreshUsers();
        });
        
        // Broadcast form
        document.getElementById('broadcastForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.sendBroadcast();
        });
        
        // Target type change
        document.querySelector('[name="target_type"]').addEventListener('change', (e) => {
            const specificDiv = document.getElementById('specificUserDiv');
            specificDiv.style.display = e.target.value === 'user' ? 'block' : 'none';
        });
    }
    
    initRealtimeClient() {
        // Check if RealtimeClient is available
        if (typeof RealtimeClient === 'undefined') {
            console.warn('RealtimeClient not available, using mock client');
            this.client = this.createMockClient();
            this.updateConnectionStatus(false);
            return;
        }

        try {
            this.client = new RealtimeClient({
                debug: true,
                showNotifications: false // We'll handle notifications in the widget
            });

            // Event handlers
            this.client.on('connected', () => {
                this.updateConnectionStatus(true);
            });

            this.client.on('connection_lost', () => {
                this.updateConnectionStatus(false);
            });

            this.client.on('event', (event) => {
                this.addEvent(event);
            });

            this.client.on('stats_updated', (stats) => {
                this.updateStats(stats);
            });

            this.client.on('online_users_updated', (users) => {
                this.updateUsers(users);
            });
        } catch (error) {
            console.error('Failed to initialize RealtimeClient:', error);
            this.client = this.createMockClient();
            this.updateConnectionStatus(false);
        }
    }

    createMockClient() {
        return {
            on: () => {},
            getStats: () => {},
            getOnlineUsers: () => {},
            sendNotification: () => Promise.resolve()
        };
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        document.getElementById('widgetPanel').style.display = 'block';
        this.isOpen = true;
        this.refreshEvents();
        this.refreshUsers();
    }
    
    close() {
        document.getElementById('widgetPanel').style.display = 'none';
        this.isOpen = false;
    }
    
    updateConnectionStatus(connected) {
        const statusEl = document.getElementById('connectionStatus');
        if (connected) {
            statusEl.className = 'badge bg-success me-2';
            statusEl.innerHTML = '<i class="fas fa-circle"></i> Online';
        } else {
            statusEl.className = 'badge bg-danger me-2';
            statusEl.innerHTML = '<i class="fas fa-circle"></i> Offline';
        }
    }
    
    updateStats(stats) {
        // Update unread badge
        const unreadBadge = document.getElementById('unreadBadge');
        const eventsCount = document.getElementById('eventsCount');
        const onlineCount = document.getElementById('onlineCount');
        
        if (stats.unread_events > 0) {
            unreadBadge.textContent = stats.unread_events;
            unreadBadge.style.display = 'block';
            eventsCount.textContent = stats.unread_events;
            eventsCount.style.display = 'inline';
        } else {
            unreadBadge.style.display = 'none';
            eventsCount.style.display = 'none';
        }
        
        onlineCount.textContent = stats.online_users;
    }
    
    addEvent(event) {
        this.events.unshift(event);
        this.renderEvents();
        
        // Show notification
        this.showNotification(event);
        
        // Update stats
        this.client.getStats();
    }
    
    renderEvents() {
        const container = document.getElementById('eventsList');
        
        if (this.events.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <p>No recent events</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.events.slice(0, 20).map(event => `
            <div class="event-item p-3 mb-2 border rounded priority-${event.priority}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${event.title}</h6>
                        <p class="mb-1 small">${event.message}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            ${new Date(event.created_at).toLocaleString()}
                        </small>
                    </div>
                    <span class="badge bg-${this.getPriorityColor(event.priority)}">${event.priority}</span>
                </div>
            </div>
        `).join('');
    }
    
    updateUsers(users) {
        this.users = users;
        this.renderUsers();
    }
    
    renderUsers() {
        const container = document.getElementById('usersList');
        
        if (this.users.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-user-slash fa-2x mb-2"></i>
                    <p>No users online</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.users.map(user => `
            <div class="user-item p-2 mb-1 border rounded">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${user.full_name || user.username}</strong>
                        <br>
                        <small class="text-muted">${user.user_type}</small>
                    </div>
                    <div class="text-end">
                        <span class="status-${user.status}">
                            <i class="fas fa-circle"></i> ${user.status}
                        </span>
                        <br>
                        <small class="text-muted">${user.current_page || 'Unknown page'}</small>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    refreshEvents() {
        this.client.getStats();
    }
    
    refreshUsers() {
        this.client.getOnlineUsers();
    }
    
    async sendBroadcast() {
        const form = document.getElementById('broadcastForm');
        const formData = new FormData(form);
        
        const targetType = formData.get('target_type');
        const targetId = formData.get('target_id') || null;
        const title = formData.get('title');
        const message = formData.get('message');
        const priority = formData.get('priority');
        
        try {
            if (targetType === 'all') {
                await this.client.sendNotification('all', null, title, message, null, priority);
            } else {
                await this.client.sendNotification(targetType, targetId, title, message, null, priority);
            }
            
            form.reset();
            this.showSuccessMessage('Broadcast sent successfully!');
            
        } catch (error) {
            this.showErrorMessage('Failed to send broadcast: ' + error.message);
        }
    }
    
    showNotification(event) {
        const container = document.getElementById('realtime-notifications');
        
        const notification = document.createElement('div');
        notification.className = `alert alert-${this.getPriorityColor(event.priority)} alert-dismissible fade show`;
        notification.innerHTML = `
            <strong>${event.title}</strong><br>
            ${event.message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        container.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }
    
    showErrorMessage(message) {
        this.showMessage(message, 'danger');
    }
    
    showMessage(message, type) {
        const container = document.getElementById('realtime-notifications');
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        container.appendChild(alert);
        
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }
    
    getPriorityColor(priority) {
        const colors = {
            low: 'secondary',
            normal: 'info',
            high: 'warning',
            urgent: 'danger'
        };
        return colors[priority] || 'info';
    }
}

// Initialize widget when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.realtimeWidget = new RealtimeWidget();
});
</script>
