<?php
/**
 * Print Order Page
 */

session_start();
require_once '../includes/admin_auth.php';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';
require_once '../includes/order_status_helper.php';

$order_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$order_id) {
    die('Invalid order ID');
}

try {
    $conn = getDbConnection();

    // Get order details
    $stmt = $conn->prepare("
        SELECT o.*, u.full_name, u.email, u.phone_number as phone
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.user_id
        WHERE o.order_id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        die('Order not found');
    }

    // Get order items
    $stmt = $conn->prepare("
        SELECT oi.*, p.name as product_name, p.image
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.product_id
        WHERE oi.order_id = ?
    ");
    $stmt->execute([$order_id]);
    $order_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    die('Error loading order: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Order #<?php echo htmlspecialchars($order['order_number'] ?? $order_id); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            font-size: 12px;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .order-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .customer-info, .order-details {
            width: 48%;
        }
        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .total-section {
            margin-top: 20px;
            text-align: right;
        }
        .total-row {
            margin: 5px 0;
        }
        .grand-total {
            font-weight: bold;
            font-size: 14px;
            border-top: 2px solid #000;
            padding-top: 5px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="no-print" style="margin-bottom: 20px;">
        <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Print Order
        </button>
        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">
            Close
        </button>
    </div>

    <div class="header">
        <div class="company-name">TEWUNEED</div>
        <div>Toko Online Terpercaya</div>
        <div>Email: <EMAIL> | Phone: (021) 1234-5678</div>
    </div>

    <div class="order-info">
        <div class="customer-info">
            <div class="section-title">Customer Information</div>
            <div><strong>Name:</strong> <?php echo htmlspecialchars($order['full_name'] ?? 'Guest'); ?></div>
            <div><strong>Email:</strong> <?php echo htmlspecialchars($order['email'] ?? 'N/A'); ?></div>
            <div><strong>Phone:</strong> <?php echo htmlspecialchars($order['phone'] ?? 'N/A'); ?></div>
            <div><strong>Address:</strong><br><?php echo nl2br(htmlspecialchars($order['shipping_address'] ?? 'N/A')); ?></div>
        </div>

        <div class="order-details">
            <div class="section-title">Order Details</div>
            <div><strong>Order Number:</strong> <?php echo htmlspecialchars($order['order_number'] ?? '#' . $order_id); ?></div>
            <div><strong>Order Date:</strong> <?php echo date('M j, Y g:i A', strtotime($order['order_date'] ?? $order['created_at'])); ?></div>
            <div><strong>Status:</strong> 
                <?php 
                $status = $order['order_status'] ?? $order['status'] ?? 'pending';
                $config = getStatusConfig($status);
                echo htmlspecialchars($config['label']);
                ?>
            </div>
            <div><strong>Payment Method:</strong> <?php echo ucwords(str_replace('_', ' ', $order['payment_method'] ?? 'N/A')); ?></div>
        </div>
    </div>

    <div class="section-title">Order Items</div>
    <table class="items-table">
        <thead>
            <tr>
                <th>Product</th>
                <th>Quantity</th>
                <th class="text-right">Unit Price</th>
                <th class="text-right">Subtotal</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            $subtotal = 0;
            foreach ($order_items as $item): 
                $item_total = $item['price'] * $item['quantity'];
                $subtotal += $item_total;
            ?>
            <tr>
                <td><?php echo htmlspecialchars($item['product_name'] ?? 'Product #' . $item['product_id']); ?></td>
                <td><?php echo $item['quantity']; ?></td>
                <td class="text-right">Rp <?php echo number_format($item['price'], 0, ',', '.'); ?></td>
                <td class="text-right">Rp <?php echo number_format($item_total, 0, ',', '.'); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <div class="total-section">
        <div class="total-row">
            <strong>Subtotal: Rp <?php echo number_format($subtotal, 0, ',', '.'); ?></strong>
        </div>
        <div class="total-row">
            <strong>Shipping: Rp <?php echo number_format($order['shipping_cost'] ?? 0, 0, ',', '.'); ?></strong>
        </div>
        <div class="total-row grand-total">
            <strong>Grand Total: Rp <?php echo number_format(($order['total_amount'] ?? ($subtotal + ($order['shipping_cost'] ?? 0))), 0, ',', '.'); ?></strong>
        </div>
    </div>

    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This is a computer-generated document. No signature required.</p>
        <p>Printed on: <?php echo date('M j, Y g:i A'); ?></p>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() { window.print(); };
    </script>
</body>
</html>
