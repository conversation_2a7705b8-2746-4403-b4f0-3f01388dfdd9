<?php
// Database configuration for AJAX requests
// This version suppresses error output to prevent JSON corruption

$host = 'localhost';
$dbname = 'db_tewuneed';
$username = 'root';
$password = '';

// Disable error reporting for AJAX to prevent output corruption
error_reporting(0);
ini_set('display_errors', 0);

try {
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    // Log error instead of displaying it
    error_log("Database connection failed: " . $e->getMessage());
    throw new Exception("Database connection failed");
}
