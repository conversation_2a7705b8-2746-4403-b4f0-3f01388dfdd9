<?php
/**
 * Check for new notifications (real-time endpoint)
 */

session_start();
require_once '../includes/db_connect.php';
require_once '../includes/notification_functions.php';

header('Content-Type: application/json');

try {
    // Check if user is authenticated
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('User not authenticated');
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $lastCheck = intval($input['last_check'] ?? 0);
    $userId = $_SESSION['user_id'];

    // Convert JavaScript timestamp to PHP timestamp
    $lastCheckTime = date('Y-m-d H:i:s', $lastCheck / 1000);

    $newNotifications = [];
    $stats = [];

    // Check for new notifications since last check
    try {
        $stmt = $conn->prepare("
            SELECT 
                notification_id,
                title,
                message,
                order_id,
                status_logo,
                created_at,
                is_read
            FROM order_notifications 
            WHERE user_id = ? 
            AND created_at > ?
            ORDER BY created_at DESC
        ");
        $stmt->execute([$userId, $lastCheckTime]);
        $newNotifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Table might not exist, try alternative approach
        error_log("Notification table error: " . $e->getMessage());
        
        // Check for order status changes as notifications
        $stmt = $conn->prepare("
            SELECT 
                o.order_id,
                CONCAT('Order #', o.order_id, ' Status Update') as title,
                CONCAT('Your order status has been updated to: ', COALESCE(o.order_status, o.status)) as message,
                o.order_id,
                '📦' as status_logo,
                o.updated_at as created_at,
                0 as is_read,
                CONCAT('notif_', o.order_id, '_', UNIX_TIMESTAMP(o.updated_at)) as notification_id
            FROM orders o
            WHERE o.user_id = ? 
            AND o.updated_at > ?
            ORDER BY o.updated_at DESC
            LIMIT 10
        ");
        $stmt->execute([$userId, $lastCheckTime]);
        $newNotifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get updated statistics
    try {
        $stats = getNotificationStats($conn, $userId);
    } catch (Exception $e) {
        // Fallback stats calculation
        $stats = [
            'total' => count($newNotifications),
            'unread' => count($newNotifications),
            'today' => count(array_filter($newNotifications, function($n) {
                return date('Y-m-d', strtotime($n['created_at'])) === date('Y-m-d');
            })),
            'this_week' => count(array_filter($newNotifications, function($n) {
                return strtotime($n['created_at']) > strtotime('-1 week');
            }))
        ];
    }

    // Simulate some additional notifications for testing
    if (count($newNotifications) === 0 && isset($input['simulate']) && $input['simulate']) {
        $simulatedNotifications = [
            [
                'notification_id' => 'sim_' . time(),
                'title' => '🎉 Welcome Bonus!',
                'message' => 'Selamat! Anda mendapat bonus poin untuk pembelian pertama.',
                'order_id' => null,
                'status_logo' => '🎁',
                'created_at' => date('Y-m-d H:i:s'),
                'is_read' => 0
            ],
            [
                'notification_id' => 'sim_' . (time() + 1),
                'title' => '📦 Order Update',
                'message' => 'Pesanan Anda sedang dalam proses packaging.',
                'order_id' => 1,
                'status_logo' => '📦',
                'created_at' => date('Y-m-d H:i:s'),
                'is_read' => 0
            ]
        ];
        
        $newNotifications = array_merge($newNotifications, $simulatedNotifications);
        $stats['total'] += count($simulatedNotifications);
        $stats['unread'] += count($simulatedNotifications);
        $stats['today'] += count($simulatedNotifications);
    }

    // Add random promotional notifications occasionally
    if (rand(1, 100) <= 5) { // 5% chance
        $promoNotifications = [
            [
                'notification_id' => 'promo_' . time(),
                'title' => '🔥 Flash Sale!',
                'message' => 'Diskon 50% untuk semua produk elektronik. Buruan sebelum kehabisan!',
                'order_id' => null,
                'status_logo' => '🔥',
                'created_at' => date('Y-m-d H:i:s'),
                'is_read' => 0
            ]
        ];
        
        $newNotifications = array_merge($newNotifications, $promoNotifications);
        $stats['total'] += 1;
        $stats['unread'] += 1;
        $stats['today'] += 1;
    }

    echo json_encode([
        'success' => true,
        'new_notifications' => $newNotifications,
        'has_new' => count($newNotifications) > 0,
        'stats' => $stats,
        'last_check_time' => time() * 1000,
        'debug' => [
            'user_id' => $userId,
            'last_check_time' => $lastCheckTime,
            'current_time' => date('Y-m-d H:i:s'),
            'new_count' => count($newNotifications)
        ]
    ]);

} catch (Exception $e) {
    error_log("Error checking notifications: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'new_notifications' => [],
        'stats' => [
            'total' => 0,
            'unread' => 0,
            'today' => 0,
            'this_week' => 0
        ]
    ]);
}
?>
