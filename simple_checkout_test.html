<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Checkout Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            display: none;
        }
        .loading.show {
            display: inline;
        }
        .debug {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>🧪 Simple Checkout Test</h1>
        <p>This is a minimal test form to isolate the checkout issue.</p>
        
        <form method="POST" action="checkout.php" id="simple-checkout-form">
            <div class="form-group">
                <label for="shipping_name">Full Name *</label>
                <input type="text" id="shipping_name" name="shipping_name" value="Test User" required>
            </div>
            
            <div class="form-group">
                <label for="shipping_email">Email *</label>
                <input type="email" id="shipping_email" name="shipping_email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="shipping_phone">Phone *</label>
                <input type="text" id="shipping_phone" name="shipping_phone" value="081234567890" required>
            </div>
            
            <div class="form-group">
                <label for="shipping_address">Address *</label>
                <textarea id="shipping_address" name="shipping_address" rows="3" required>Test Address 123, Test City</textarea>
            </div>
            
            <div class="form-group">
                <label for="shipping_method">Shipping Method</label>
                <select id="shipping_method" name="shipping_method">
                    <option value="standard">Standard (Rp 10.000)</option>
                    <option value="express">Express (Rp 25.000)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="payment_method">Payment Method *</label>
                <select id="payment_method" name="payment_method" required>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="cod">Cash on Delivery</option>
                    <option value="qris">QRIS</option>
                </select>
            </div>
            
            <button type="submit" id="submit-btn">
                <span class="btn-text">🚀 Submit Test Order</span>
                <span class="loading">⏳ Processing...</span>
            </button>
        </form>
        
        <div class="debug" id="debug-log">
            <strong>Debug Log:</strong><br>
            <span id="debug-content">Ready to test...</span>
        </div>
    </div>

    <script>
        // Debug logging function
        function debugLog(message) {
            const debugContent = document.getElementById('debug-content');
            const timestamp = new Date().toLocaleTimeString();
            debugContent.innerHTML += `<br>[${timestamp}] ${message}`;
            console.log(`[${timestamp}] ${message}`);
        }

        document.addEventListener('DOMContentLoaded', function() {
            debugLog('Page loaded, setting up form...');
            
            const form = document.getElementById('simple-checkout-form');
            const submitBtn = document.getElementById('submit-btn');
            const btnText = submitBtn.querySelector('.btn-text');
            const loading = submitBtn.querySelector('.loading');
            
            debugLog('Form element: ' + (form ? 'Found' : 'NOT FOUND'));
            debugLog('Submit button: ' + (submitBtn ? 'Found' : 'NOT FOUND'));
            
            if (form) {
                debugLog('Adding submit event listener...');
                
                form.addEventListener('submit', function(e) {
                    debugLog('=== FORM SUBMIT EVENT TRIGGERED ===');
                    debugLog('Event type: ' + e.type);
                    debugLog('Form action: ' + this.action);
                    debugLog('Form method: ' + this.method);
                    
                    // Show loading state
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        btnText.style.display = 'none';
                        loading.classList.add('show');
                        debugLog('Button state changed to loading');
                    }
                    
                    // Validate required fields
                    const requiredFields = ['shipping_name', 'shipping_email', 'shipping_phone', 'shipping_address', 'payment_method'];
                    let allValid = true;
                    
                    for (let field of requiredFields) {
                        const input = document.getElementById(field);
                        if (!input || !input.value.trim()) {
                            debugLog('VALIDATION FAILED: Missing ' + field);
                            allValid = false;
                            break;
                        } else {
                            debugLog('Field ' + field + ': ' + input.value);
                        }
                    }
                    
                    if (!allValid) {
                        e.preventDefault();
                        debugLog('FORM SUBMISSION PREVENTED - Validation failed');
                        
                        // Reset button
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            btnText.style.display = 'inline';
                            loading.classList.remove('show');
                        }
                        return;
                    }
                    
                    debugLog('All validations passed');
                    debugLog('Form will submit to: ' + this.action);
                    debugLog('=== ALLOWING NATURAL FORM SUBMISSION ===');
                    
                    // Set timeout to reset button if something goes wrong
                    setTimeout(function() {
                        if (submitBtn && submitBtn.disabled) {
                            debugLog('TIMEOUT: Resetting button after 10 seconds');
                            submitBtn.disabled = false;
                            btnText.style.display = 'inline';
                            loading.classList.remove('show');
                        }
                    }, 10000);
                });
                
                debugLog('Form setup complete');
            } else {
                debugLog('ERROR: Could not find form element');
            }
            
            // Add click listener to button for additional debugging
            if (submitBtn) {
                submitBtn.addEventListener('click', function(e) {
                    debugLog('=== SUBMIT BUTTON CLICKED ===');
                    debugLog('Button type: ' + this.type);
                    debugLog('Button disabled: ' + this.disabled);
                    debugLog('Form: ' + (this.form ? 'Found' : 'NOT FOUND'));
                });
            }
        });
        
        // Log any JavaScript errors
        window.addEventListener('error', function(e) {
            debugLog('JAVASCRIPT ERROR: ' + e.message + ' at ' + e.filename + ':' + e.lineno);
        });
        
        // Log when page is about to unload (form submission)
        window.addEventListener('beforeunload', function(e) {
            debugLog('Page is about to unload (form submission or navigation)');
        });
        
        debugLog('Script loaded successfully');
    </script>
</body>
</html>
