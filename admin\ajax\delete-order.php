<?php
/**
 * Delete order (admin only) - Fixed version
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in JSON response

session_start();

// Check admin authentication
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access. Admin login required.'
    ]);
    exit;
}

require_once '../../includes/db_connect.php';

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        // Fallback to POST data
        $input = $_POST;
    }

    $order_id = intval($input['order_id'] ?? 0);

    if (!$order_id) {
        throw new Exception('Invalid order ID');
    }

    // Start transaction
    $conn->beginTransaction();

    // Get order details before deletion for logging
    $stmt = $conn->prepare("
        SELECT o.*, u.full_name, u.email, u.username
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.user_id
        WHERE o.order_id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        throw new Exception('Order not found');
    }

    $order_number = $order['order_number'] ?? '#' . $order_id;
    $customer_name = $order['full_name'] ?? $order['username'] ?? 'Unknown Customer';

    // Delete related records in correct order to avoid foreign key constraints

    // 1. Delete order items first (if table exists)
    try {
        $stmt = $conn->prepare("DELETE FROM order_items WHERE order_id = ?");
        $stmt->execute([$order_id]);
        error_log("Deleted order items for order {$order_id}");
    } catch (PDOException $e) {
        error_log("Order items deletion failed (table might not exist): " . $e->getMessage());
        // Continue anyway - table might not exist
    }

    // 2. Delete order status history (if table exists)
    try {
        $stmt = $conn->prepare("DELETE FROM order_status_history WHERE order_id = ?");
        $stmt->execute([$order_id]);
        error_log("Deleted order status history for order {$order_id}");
    } catch (PDOException $e) {
        error_log("Order status history deletion failed (table might not exist): " . $e->getMessage());
        // Continue anyway - table might not exist
    }

    // 3. Delete notifications related to this order (if table exists)
    try {
        $stmt = $conn->prepare("DELETE FROM notifications WHERE user_id = ? AND (message LIKE ? OR message LIKE ?)");
        $stmt->execute([$order['user_id'], '%' . $order_number . '%', '%order ' . $order_id . '%']);
        error_log("Deleted notifications for order {$order_id}");
    } catch (PDOException $e) {
        error_log("Notifications deletion failed (table might not exist): " . $e->getMessage());
        // Continue anyway - table might not exist
    }

    // 4. Finally delete the main order record
    $stmt = $conn->prepare("DELETE FROM orders WHERE order_id = ?");
    $result = $stmt->execute([$order_id]);

    if (!$result) {
        throw new Exception('Failed to delete order from database');
    }

    $affected_rows = $stmt->rowCount();
    if ($affected_rows === 0) {
        throw new Exception('Order not found or already deleted');
    }

    // Commit transaction
    $conn->commit();

    // Log the deletion
    error_log("Order {$order_id} ({$order_number}) deleted successfully by admin " . $_SESSION['user_id']);

    // Store deletion notification for real-time sync
    try {
        $syncKey = 'tewuneed_order_status_sync';
        $existingData = json_decode($_COOKIE[$syncKey] ?? '{}', true);
        if (!$existingData) $existingData = [];

        $existingData[$order_id] = [
            'action' => 'deleted',
            'order_id' => $order_id,
            'order_number' => $order_number,
            'customer_name' => $customer_name,
            'admin_name' => $_SESSION['username'] ?? 'Administrator',
            'admin_note' => 'Order cancelled and deleted by administrator',
            'timestamp' => time() * 1000, // JavaScript timestamp
            'processed' => false
        ];

        // Set cookie for cross-tab communication
        setcookie($syncKey, json_encode($existingData), time() + 3600, '/');

        error_log("Stored deletion notification for real-time sync");
    } catch (Exception $e) {
        error_log("Failed to store deletion notification: " . $e->getMessage());
    }

    echo json_encode([
        'success' => true,
        'message' => 'Order deleted successfully',
        'order_id' => $order_id,
        'order_number' => $order_number,
        'customer_name' => $customer_name,
        'deleted_by' => $_SESSION['username'] ?? $_SESSION['user_id'],
        'sync_data' => [
            'action' => 'deleted',
            'order_id' => $order_id,
            'order_number' => $order_number,
            'admin_name' => $_SESSION['username'] ?? 'Administrator',
            'timestamp' => time() * 1000
        ]
    ]);

} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    error_log("Error deleting order: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
