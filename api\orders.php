<?php
require_once '../includes/autoload.php';

// Enable CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

// Check authentication
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Handle different request methods
switch ($method) {
    case 'GET':
        try {
            $db = get_db_connection();
            
            if (isset($_GET['id'])) {
                // Get single order
                $stmt = $db->prepare("
                    SELECT o.*, u.full_name as customer_name, u.email as customer_email
                    FROM orders o
                    JOIN users u ON o.user_id = u.user_id
                    WHERE o.id = ? AND o.user_id = ?
                ");
                $stmt->execute([$_GET['id'], $_SESSION['user_id']]);
                $order = $stmt->fetch();

                if ($order) {
                    // Get order items
                    $stmt = $db->prepare("
                        SELECT oi.*, p.name as product_name, p.image as product_image
                        FROM order_items oi
                        JOIN products p ON oi.product_id = p.id
                        WHERE oi.order_id = ?
                    ");
                    $stmt->execute([$order['id']]);
                    $order['items'] = $stmt->fetchAll();

                    echo json_encode(['success' => true, 'data' => $order]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Order not found']);
                }
            } else {
                // Get all orders for user
                $page = $_GET['page'] ?? 1;
                $limit = $_GET['limit'] ?? 10;

                $stmt = $db->prepare("
                    SELECT o.*, u.full_name as customer_name
                    FROM orders o
                    JOIN users u ON o.user_id = u.user_id
                    WHERE o.user_id = ?
                    ORDER BY o.created_at DESC
                    LIMIT ? OFFSET ?
                ");
                $stmt->execute([$_SESSION['user_id'], $limit, ($page - 1) * $limit]);
                $orders = $stmt->fetchAll();

                // Get total count
                $stmt = $db->prepare("SELECT COUNT(*) FROM orders WHERE user_id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $total = $stmt->fetchColumn();

                echo json_encode([
                    'success' => true,
                    'data' => [
                        'orders' => $orders,
                        'pagination' => [
                            'total' => $total,
                            'per_page' => $limit,
                            'current_page' => $page,
                            'last_page' => ceil($total / $limit)
                        ]
                    ]
                ]);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Database error']);
        }
        break;

    case 'POST':
        // Create new order
        if (!isset($data['items']) || empty($data['items'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Order items are required']);
            break;
        }

        try {
            $db = get_db_connection();
            $db->beginTransaction();

            // Create order
            $stmt = $db->prepare("
                INSERT INTO orders (user_id, total_amount, status, shipping_address, payment_method)
                VALUES (?, ?, 'pending', ?, ?)
            ");
            $stmt->execute([
                $_SESSION['user_id'],
                $data['total_amount'],
                $data['shipping_address'],
                $data['payment_method']
            ]);
            $order_id = $db->lastInsertId();

            // Add order items
            $stmt = $db->prepare("
                INSERT INTO order_items (order_id, product_id, quantity, price)
                VALUES (?, ?, ?, ?)
            ");

            foreach ($data['items'] as $item) {
                $stmt->execute([
                    $order_id,
                    $item['product_id'],
                    $item['quantity'],
                    $item['price']
                ]);

                // Update product stock
                $db->prepare("
                    UPDATE products 
                    SET stock = stock - ? 
                    WHERE id = ? AND stock >= ?
                ")->execute([$item['quantity'], $item['product_id'], $item['quantity']]);
            }

            // Clear cart
            $db->prepare("DELETE FROM cart_items WHERE user_id = ?")->execute([$_SESSION['user_id']]);

            $db->commit();

            echo json_encode([
                'success' => true,
                'message' => 'Order created successfully',
                'data' => ['order_id' => $order_id]
            ]);
        } catch (PDOException $e) {
            $db->rollBack();
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to create order']);
        }
        break;

    case 'PUT':
        // Update order status (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['id']) || !isset($data['status'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Order ID and status are required']);
            break;
        }

        try {
            $db = get_db_connection();
            $stmt = $db->prepare("UPDATE orders SET status = ? WHERE id = ?");
            $result = $stmt->execute([$data['status'], $data['id']]);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Order status updated']);
            } else {
                throw new PDOException('Failed to update order status');
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update order status']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}
?> 