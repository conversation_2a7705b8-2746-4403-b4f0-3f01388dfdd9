<?php
require_once '../includes/autoload.php';

header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Check if file was uploaded
if (!isset($_FILES['file'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'No file uploaded']);
    exit;
}

$file = $_FILES['file'];
$type = $_POST['type'] ?? 'product'; // product, category, user

// Validate file type
$allowed_types = [
    'product' => ['image/jpeg', 'image/png', 'image/gif'],
    'category' => ['image/jpeg', 'image/png', 'image/gif'],
    'user' => ['image/jpeg', 'image/png']
];

if (!isset($allowed_types[$type]) || !in_array($file['type'], $allowed_types[$type])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid file type']);
    exit;
}

// Validate file size (5MB max)
if ($file['size'] > 5 * 1024 * 1024) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'File too large']);
    exit;
}

try {
    // Create upload directory if it doesn't exist
    $upload_dir = __DIR__ . '/../uploads/' . $type;
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $filepath = $upload_dir . '/' . $filename;

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // Update database if needed
        if (isset($_POST['id'])) {
            $db = get_db_connection();
            
            switch ($type) {
                case 'product':
                    $stmt = $db->prepare("UPDATE products SET image = ? WHERE id = ?");
                    break;
                case 'category':
                    $stmt = $db->prepare("UPDATE categories SET image = ? WHERE id = ?");
                    break;
                case 'user':
                    $stmt = $db->prepare("UPDATE users SET avatar = ? WHERE id = ?");
                    break;
            }

            if (isset($stmt)) {
                $stmt->execute([$filename, $_POST['id']]);
            }
        }

        echo json_encode([
            'success' => true,
            'message' => 'File uploaded successfully',
            'data' => [
                'filename' => $filename,
                'url' => '/uploads/' . $type . '/' . $filename
            ]
        ]);
    } else {
        throw new Exception('Failed to move uploaded file');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to upload file']);
} 