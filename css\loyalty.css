/*CSS styles for the body section*/

body{
    font-family: 'Poppins';
    margin: 60px;
    margin-top: 6px;
    margin-bottom: 6px;
    font-weight: 470;
}

header{
    background-color: white;
    border-radius: 10px;
    margin-bottom: 20px;
}

#logo{
    height: 240px;
    width: 350px;
}

nav{
    background-color: #006ca5;
    height: 10vh;
    width: 100%;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: auto;
}

a{
    position: relative;
    text-decoration: none;
    color: beige;
    font-size: 15px;
}

a::after{
    content: "";
    background-color:aliceblue;
    width: 0;
    height: 3px;
    margin: auto;
    display: block;
}

nav a:hover::after{
    width:100%;
    transition: width 0.1s linear;
}

#intro{
    background-color: #dfebf6;
    color: black;
    text-align: center;
}

#image{
    border-style: none;
    border-radius: 10px;
}

#programlist{
    background-color: #dfebf6;
    color: black;
    text-align: center;
    margin-top: 20px;
    margin-bottom: 2px;
}

.section{
    background-color: #f5f5f5;
    color: #242526;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

#form{
    background-color: #ececec;
    color: black;
    text-align: center;
    margin-left: 90px;
    margin-right: 90px;
}

table{
    width: 100%;
}

td:hover{
    transform: scale(1.03);
    transition: 0.3s;
}

input[type=text]{
    width: 50%;
    padding: 12px 20px;
    margin: 8px 0;
    box-sizing: border-box;
    border: none;
    border-radius: 4px;
    border-bottom: 1.5px solid #152238;
}

label{
    font-weight: bold;
}

select{
    width: 50%;
    padding: 16px 20px;
    border: none;
    border-radius: 4px;
}

textarea{
    width: 50%;
    height: 150px;
    border: none;
    border-bottom: 2px solid #152238;
}

table h3{
    text-align: center;
}

.button{
    background-color: white;
    color: #152238;
    border: 2px solid #152238; 
    transition-duration: 0.4s;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    padding: 15px 32px;
}

.button:hover{
    background-color: #152238;
    color: white;
    cursor: pointer;
}

/*CSS Styles for the footer section*/

footer{
    background-color:#dfebf6;
    border-radius: 10px;
    color: #242526;
    font-size: 14px;
}

.footer{
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    margin: auto;
}

.footer_column{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.footer_column a{
    color: #006ca5;
    font-size: 13px;
    transition: 0.2s;
}

.footer_column a:hover{
    font-weight: bold;
}

.footer_title p{
    display: flex;
    justify-content: center;
    text-align: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
}

.icon_sm{
    transition: 0.2s;
    display:inline-block;
}

.icon_sm:hover{
    color: rgb(72, 134, 192);
}

/*Mobile view*/

@media screen and (max-width:600px) {
    nav{
        flex-direction: column;
        align-items: center;
        height: 50vh;
    }

    body{
        margin: 6px;
    }
    
    #logo{
        width: 100%;
        height: auto;
    }

    #form{
        margin: 6px; 
    }

    #image{
        width: 100%;
        height: auto;
    }

    tr, td{
        display: block;
        width: 100%;
    }

    #form{
        width: auto;
    }

    input[type=text]{
        width: 100%;
    }

    textarea{
        width: 100%;
    }

    select{
        width: 100%;
    }

    .buttons-container{
        text-align: center;
    }

    .button{
        display:block;
        margin-bottom: 10px;
        width: 100%;
    }

    .footer{
        flex-direction: column;
        align-items: center;
    }
}