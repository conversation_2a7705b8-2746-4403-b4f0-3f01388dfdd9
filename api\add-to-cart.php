<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set response header to JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Include required files - Fixed path issues
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';

// Log incoming request for debugging
$input = file_get_contents('php://input');
error_log('Add to Cart Request: ' . $input);

// Parse JSON input
$data = json_decode($input, true);

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Validate input
if (!isset($data['product_id']) || !isset($data['quantity'])) {
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

$product_id = intval($data['product_id']);
$quantity = max(1, intval($data['quantity']));

try {
    // Initialize or create cart
    if (!isset($_SESSION['cart']) || !is_array($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
        error_log('Initialized empty cart array in session');
    }

    // Get database connection
    $conn = $GLOBALS['conn']; // Use global connection from db_connect.php

    // Validate product exists
    $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        $response['message'] = 'Product not found';
        echo json_encode($response);
        exit;
    }

    // Add/update product in cart
    $found = false;
    
    // Use reference to update array item
    if (is_array($_SESSION['cart'])) {
        foreach ($_SESSION['cart'] as &$item) {
            if (isset($item['product_id']) && $item['product_id'] == $product_id) {
                $item['quantity'] = $quantity;
                $found = true;
                break;
            }
        }
        unset($item); // Unset reference after loop
    }

    if (!$found) {
        // Add new product to cart
        $_SESSION['cart'][] = [
            'product_id' => $product_id,
            'quantity' => $quantity,
            'name' => isset($product['name']) ? $product['name'] : 'Product',
            'price' => isset($product['price']) ? $product['price'] : 0,
            'image' => isset($product['image']) ? $product['image'] : null
        ];
        error_log('Added new product to cart: ' . $product_id);
    }

    // Calculate total items in cart
    $cart_count = 0;
    foreach ($_SESSION['cart'] as $item) {
        $cart_count += isset($item['quantity']) ? $item['quantity'] : 1;
    }

    // Debug cart contents
    error_log('Cart contents after adding product: ' . print_r($_SESSION['cart'], true));

    // Prepare success response
    $response = [
        'success' => true,
        'message' => 'Product added to cart successfully',
        'data' => [
            'cart_count' => $cart_count,
            'product_name' => isset($product['name']) ? $product['name'] : 'Product'
        ]
    ];
} catch (Exception $e) {
    error_log('Add to cart error: ' . $e->getMessage());
    $response = [
        'success' => false,
        'message' => 'Error adding product to cart: ' . $e->getMessage(),
        'data' => null
    ];
}

// Return JSON response
echo json_encode($response);
