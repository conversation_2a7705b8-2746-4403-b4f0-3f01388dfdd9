<?php
ob_start(); // <PERSON><PERSON> output buffering
ini_set('display_errors', 0);
error_reporting(0);
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Set response header to JSON
header('Content-Type: application/json');

// Verify that the user is logged in
if (!isset($_SESSION['user_id'])) {
    ob_end_clean();
    echo json_encode([
        'success' => false,
        'message' => 'Silakan login untuk menambahkan produk ke keranjang'
    ]);
    exit;
}

// Validate product_id and quantity
if (!isset($_POST['product_id']) || !is_numeric($_POST['product_id'])) {
    ob_end_clean();
    echo json_encode([
        'success' => false,
        'message' => 'ID produk tidak valid'
    ]);
    exit;
}

$product_id = (int)$_POST['product_id'];
$quantity = isset($_POST['quantity']) && is_numeric($_POST['quantity']) ? (int)$_POST['quantity'] : 1;

if ($quantity <= 0) {
    ob_end_clean();
    echo json_encode([
        'success' => false,
        'message' => 'Jumlah harus lebih dari nol'
    ]);
    exit;
}

try {
    // Begin transaction for data consistency
    $conn->beginTransaction();

    // Check if product exists and has enough stock
    $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ? AND is_active = 1");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        ob_end_clean();
        throw new Exception('Produk tidak tersedia');
    }

    // Simple stock check without reservation system
    if ($product['stock'] < $quantity) {
        ob_end_clean();
        throw new Exception('Stok tidak mencukupi. Tersedia: ' . $product['stock']);
    }

    // Get or create user's cart
    $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$cart) {
        // Create a new cart
        $stmt = $conn->prepare("INSERT INTO carts (user_id, created_at) VALUES (?, NOW())");
        $stmt->execute([$_SESSION['user_id']]);
        $cart_id = $conn->lastInsertId();
    } else {
        $cart_id = $cart['cart_id'];
    }

    // Check if product already in cart
    $stmt = $conn->prepare("SELECT * FROM cart_items WHERE cart_id = ? AND product_id = ?");
    $stmt->execute([$cart_id, $product_id]);
    $cartItem = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($cartItem) {
        // Update existing cart item quantity
        $newTotalQuantity = $cartItem['quantity'] + $quantity;

        // Check if total quantity exceeds stock
        if ($product['stock'] < $newTotalQuantity) {
            ob_end_clean();
            throw new Exception('Stok tidak mencukupi untuk total quantity. Tersedia: ' . $product['stock'] . ', Di cart: ' . $cartItem['quantity']);
        }

        // Update existing cart item quantity to the new total
        $stmt = $conn->prepare("UPDATE cart_items SET quantity = ? WHERE cart_item_id = ?");
        $stmt->execute([$newTotalQuantity, $cartItem['cart_item_id']]);

        // Commit transaction
        $conn->commit();

        // Get updated cart count
        $stmt = $conn->prepare("SELECT SUM(quantity) as count FROM cart_items WHERE cart_id = ?");
        $stmt->execute([$cart_id]);
        $cartCount = $stmt->fetch(PDO::FETCH_ASSOC);

        // Log activity if function exists
        if (function_exists('logActivity')) {
            logActivity($_SESSION['user_id'], 'cart_update', "Updated quantity of product ID: {$product_id} in cart to {$newTotalQuantity}");
        }

        ob_end_clean();
        echo json_encode([
            'success' => true,
            'message' => 'Jumlah ' . ($product['name'] ?? $product['NAME'] ?? 'produk') . ' di keranjang berhasil diperbarui',
            'cart_count' => $cartCount['count'] ?? 0,
            'product_id' => $product_id,
            'product_name' => $product['name'] ?? $product['NAME'] ?? 'Product',
            'quantity' => $quantity,
            'total_quantity' => $newTotalQuantity,
            'available_stock' => $product['stock']
        ]);
        exit;
    }

    // Add new item to cart
    $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity, added_at) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$cart_id, $product_id, $quantity]);

    // Commit transaction
    $conn->commit();

    // Get updated cart count
    $stmt = $conn->prepare("SELECT SUM(quantity) as count FROM cart_items WHERE cart_id = ?");
    $stmt->execute([$cart_id]);
    $cartCount = $stmt->fetch(PDO::FETCH_ASSOC);

    // Log activity if function exists
    if (function_exists('logActivity')) {
        logActivity($_SESSION['user_id'], 'cart_add', "Added product ID: {$product_id} to cart with quantity {$quantity}");
    }

    ob_end_clean();
    echo json_encode([
        'success' => true,
        'message' => ($product['name'] ?? $product['NAME'] ?? 'Produk') . ' berhasil ditambahkan ke keranjang',
        'cart_count' => $cartCount['count'] ?? 0,
        'product_id' => $product_id,
        'product_name' => $product['name'] ?? $product['NAME'] ?? 'Product',
        'quantity' => $quantity,
        'available_stock' => $product['stock']
    ]);
    exit;
    
} catch (Exception $e) {
    // Rollback transaction in case of error
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    ob_end_clean();
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
