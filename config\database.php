<?php
// Database configuration
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_NAME')) define('DB_NAME', 'db_tewuneed');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASS')) define('DB_PASS', '');

/**
 * Get database connection
 * @return PDO
 */
function getConnection() {
    static $conn = null;
    
    if ($conn === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ];
            
            $conn = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch(PDOException $e) {
            error_log("Connection Error: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }
    
    return $conn;
}

/**
 * Get current stock for a product
 * @param int $product_id
 * @return int|false
 */
function getCurrentStock($product_id) {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("SELECT stock FROM products WHERE product_id = :product_id");
        $stmt->execute(['product_id' => $product_id]);
        $result = $stmt->fetch();
        return $result ? (int)$result['stock'] : false;
    } catch (PDOException $e) {
        error_log("Get Stock Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Update product stock
 * @param int $product_id
 * @param int $quantity
 * @return bool
 */
function updateProductStock($product_id, $quantity) {
    try {
        $conn = getConnection();
        $stmt = $conn->prepare("UPDATE products SET stock = stock - :quantity WHERE product_id = :product_id AND stock >= :quantity");
        return $stmt->execute([
            'product_id' => $product_id,
            'quantity' => $quantity
        ]);
    } catch (PDOException $e) {
        error_log("Stock Update Error: " . $e->getMessage());
        return false;
    }
}