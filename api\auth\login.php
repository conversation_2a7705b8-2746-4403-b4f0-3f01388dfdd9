<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

try {
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['username']) || !isset($data['password'])) {
        throw new Exception('Username and password are required');
    }
    
    $conn = getConnection();
    
    // Get user by username
    $stmt = $conn->prepare("
        SELECT user_id, username, password, full_name, role 
        FROM users 
        WHERE username = ?
    ");
    $stmt->execute([$data['username']]);
    $user = $stmt->fetch();
    
    if (!$user || !password_verify($data['password'], $user['password'])) {
        throw new Exception('Invalid username or password');
    }
    
    // Start session
    session_start();
    $_SESSION['user'] = [
        'id' => $user['user_id'],
        'username' => $user['username'],
        'full_name' => $user['full_name'],
        'role' => $user['role']
    ];
    
    // Log login activity for admin
    if ($user['role'] === 'admin') {
        $stmt = $conn->prepare("
            INSERT INTO admin_logs (user_id, activity, ip_address, user_agent) 
            VALUES (?, 'User logged in', ?, ?)
        ");
        $stmt->execute([
            $user['user_id'],
            $_SERVER['REMOTE_ADDR'],
            $_SERVER['HTTP_USER_AGENT']
        ]);
    }
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Login successful',
        'data' => [
            'user_id' => $user['user_id'],
            'username' => $user['username'],
            'full_name' => $user['full_name'],
            'role' => $user['role']
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 