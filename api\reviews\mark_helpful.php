<?php
/**
 * Mark Review as Helpful API
 * API untuk menandai review sebagai helpful/not helpful
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
require_once '../../includes/db_connect.php';
require_once '../../includes/ReviewManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please login to vote']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['review_id']) || !isset($input['is_helpful'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
        exit;
    }
    
    $review_id = (int)$input['review_id'];
    $is_helpful = (bool)$input['is_helpful'];
    $user_id = $_SESSION['user_id'];
    
    // Validate review exists
    $stmt = $conn->prepare("SELECT review_id FROM product_reviews WHERE review_id = ?");
    $stmt->execute([$review_id]);
    
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Review not found']);
        exit;
    }
    
    $reviewManager = new ReviewManager();
    $result = $reviewManager->markReviewHelpful($review_id, $user_id, $is_helpful);
    
    if ($result) {
        echo json_encode([
            'success' => true, 
            'message' => 'Thank you for your feedback!'
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'Failed to record your vote'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Mark helpful error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal server error']);
}
?>
