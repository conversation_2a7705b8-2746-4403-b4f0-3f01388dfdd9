<?php
session_start();
require_once '../config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: admin_login.php');
    exit;
}

// Handle Add Category
if ($_POST['action'] === 'add') {
    $name = $_POST['name'];
    $description = $_POST['description'];
    
    $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
    $stmt->execute([$name, $description]);
    
    echo json_encode(['status' => 'success', 'message' => 'Category added successfully']);
    exit;
}

// Handle Edit Category
if ($_POST['action'] === 'edit') {
    $id = $_POST['id'];
    $name = $_POST['name'];
    $description = $_POST['description'];
    
    $stmt = $pdo->prepare("UPDATE categories SET name=?, description=? WHERE id=?");
    $stmt->execute([$name, $description, $id]);
    
    echo json_encode(['status' => 'success', 'message' => 'Category updated successfully']);
    exit;
}

// Handle Delete Category
if ($_POST['action'] === 'delete') {
    $id = $_POST['id'];
    
    // Check if category is being used by products
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM products WHERE category = ?");
    $stmt->execute([$id]);
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo json_encode(['status' => 'error', 'message' => 'Cannot delete category. It is being used by products.']);
        exit;
    }
    
    $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
    $stmt->execute([$id]);
    
    echo json_encode(['status' => 'success', 'message' => 'Category deleted successfully']);
    exit;
}

// Handle Get Category Details
if ($_GET['action'] === 'get') {
    $id = $_GET['id'];
    
    $stmt = $pdo->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->execute([$id]);
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode($category);
    exit;
} 