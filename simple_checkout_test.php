<?php
session_start();
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// Set test user
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
}

$user_id = $_SESSION['user_id'];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h1>🎉 Form Submitted Successfully!</h1>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px;'>";
    echo "<h3>✅ POST Data Received:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    echo "</div>";
    
    // Simulate order creation
    try {
        $order_number = generateOrderNumber();
        echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px;'>";
        echo "<h3>📋 Order Details:</h3>";
        echo "<p><strong>Order Number:</strong> $order_number</p>";
        echo "<p><strong>User ID:</strong> $user_id</p>";
        echo "<p><strong>Payment Method:</strong> " . ($_POST['payment_method'] ?? 'Not selected') . "</p>";
        echo "<p><strong>Shipping Method:</strong> " . ($_POST['shipping_method'] ?? 'Not selected') . "</p>";
        echo "</div>";
        
        echo "<div style='text-align: center; margin: 20px;'>";
        echo "<a href='order-success.php?test=1' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 18px;'>View Order Success Page</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px;'>";
        echo "<h3>❌ Error:</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    exit;
}

// Get sample cart items
$cart_items = [];
$subtotal = 0;

try {
    $stmt = $conn->prepare("
        SELECT p.product_id, COALESCE(p.name, p.NAME) as name, p.price, p.image, 2 as quantity
        FROM products p 
        WHERE p.is_active = 1 AND p.stock > 0 
        LIMIT 3
    ");
    $stmt->execute();
    $cart_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($cart_items as $item) {
        $subtotal += $item['price'] * $item['quantity'];
    }
} catch (Exception $e) {
    echo "Error getting products: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Checkout Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1>🧪 Simple Checkout Test</h1>
        
        <div class="debug-info">
            <h5>Debug Information:</h5>
            <p><strong>User ID:</strong> <?php echo $user_id; ?></p>
            <p><strong>Cart Items:</strong> <?php echo count($cart_items); ?></p>
            <p><strong>Subtotal:</strong> Rp <?php echo number_format($subtotal); ?></p>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Checkout Form</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" id="simple-checkout-form">
                            <div class="mb-3">
                                <label class="form-label">Full Name *</label>
                                <input type="text" class="form-control" name="shipping_name" value="Test User" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Email *</label>
                                <input type="email" class="form-control" name="shipping_email" value="<EMAIL>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Phone *</label>
                                <input type="tel" class="form-control" name="shipping_phone" value="081234567890" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Address *</label>
                                <textarea class="form-control" name="shipping_address" required>Test Address 123, Jakarta</textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Shipping Method *</label>
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="shipping_method" value="standard" id="standard" checked>
                                    <label class="form-check-label" for="standard">Standard (Rp 10.000)</label>
                                </div>
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="shipping_method" value="express" id="express">
                                    <label class="form-check-label" for="express">Express (Rp 25.000)</label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Payment Method *</label>
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="payment_method" value="bank_transfer" id="bank" checked>
                                    <label class="form-check-label" for="bank">Bank Transfer</label>
                                </div>
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="payment_method" value="cod" id="cod">
                                    <label class="form-check-label" for="cod">Cash on Delivery</label>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" id="submit-btn">
                                    Place Order - Test
                                </button>
                                
                                <button type="button" class="btn btn-secondary" onclick="testFormData()">
                                    Test Form Data
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Order Summary</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($cart_items) > 0): ?>
                            <?php foreach ($cart_items as $item): ?>
                            <div class="d-flex justify-content-between mb-2">
                                <span><?php echo htmlspecialchars($item['name']); ?> (<?php echo $item['quantity']; ?>x)</span>
                                <span>Rp <?php echo number_format($item['price'] * $item['quantity']); ?></span>
                            </div>
                            <?php endforeach; ?>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>Subtotal:</strong>
                                <strong>Rp <?php echo number_format($subtotal); ?></strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Shipping:</span>
                                <span>Rp 10.000</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>Total:</strong>
                                <strong>Rp <?php echo number_format($subtotal + 10000); ?></strong>
                            </div>
                        <?php else: ?>
                            <p>No items in cart</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testFormData() {
            const form = document.getElementById('simple-checkout-form');
            const formData = new FormData(form);
            
            console.log('=== FORM DATA TEST ===');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }
            
            alert('Form data logged to console. Check browser developer tools.');
        }
        
        // Add submit event listener
        document.getElementById('simple-checkout-form').addEventListener('submit', function(e) {
            console.log('=== FORM SUBMIT EVENT ===');
            console.log('Form is submitting...');
            
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = 'Processing...';
        });
        
        console.log('=== SIMPLE CHECKOUT TEST LOADED ===');
        console.log('Form element:', document.getElementById('simple-checkout-form'));
        console.log('Submit button:', document.getElementById('submit-btn'));
    </script>
</body>
</html>
