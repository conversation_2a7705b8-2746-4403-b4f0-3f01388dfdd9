<?php
// Set page variables for header
$page = 'dashboard';
$page_title = 'Dashboard';

// Include header (this will also set up $conn)
require_once 'includes/header.php';

// Get statistics with error handling
try {
    $stmt = $conn->query("SELECT COUNT(*) as total_products FROM products");
    $totalProducts = $stmt->fetch()['total_products'] ?? 0;
} catch (PDOException $e) {
    $totalProducts = 0;
}

try {
    $stmt = $conn->query("SELECT COUNT(*) as total_categories FROM categories");
    $totalCategories = $stmt->fetch()['total_categories'] ?? 0;
} catch (PDOException $e) {
    $totalCategories = 0;
}

try {
    $stmt = $conn->query("SELECT COUNT(*) as total_users FROM users WHERE role = 'customer'");
    $totalUsers = $stmt->fetch()['total_users'] ?? 0;
} catch (PDOException $e) {
    $totalUsers = 0;
}

try {
    $stmt = $conn->query("SELECT COUNT(*) as total_orders FROM orders");
    $totalOrders = $stmt->fetch()['total_orders'] ?? 0;
} catch (PDOException $e) {
    $totalOrders = 0;
}

try {
    $stmt = $conn->query("SELECT COUNT(*) as total_cart_items FROM cart_items");
    $totalCartItems = $stmt->fetch()['total_cart_items'] ?? 0;
} catch (PDOException $e) {
    $totalCartItems = 0;
}

$adminName = $_SESSION['user_name'] ?? 'Admin';

if (isset($user) && is_array($user)) {
    // akses $user['...'] seperti biasa
} else {
    // Inisialisasi $user sebagai array kosong atau tampilkan pesan default
    $user = [];
}
?>

<!-- Additional CSS for dashboard -->
<style>
    .stat-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,.1);
    }
    .product-image-preview {
        max-width: 100px;
        max-height: 100px;
        object-fit: cover;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }
    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<div class="loading-overlay">
    <div class="loading-spinner"></div>
</div>

<!-- Dashboard Content -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div class="btn-group">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
            <i class="fas fa-plus me-2"></i>Add Product
        </button>
        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
            <i class="fas fa-plus me-2"></i>Add Category
        </button>
    </div>
</div>

                <!-- Enhanced Statistics Cards -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="text-primary mb-1"><?php echo $totalProducts; ?></h3>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-box me-2"></i>Total Products
                                    </p>
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT COUNT(*) as low_stock FROM products WHERE stock < 10");
                                        $lowStock = $stmt->fetch()['low_stock'] ?? 0;
                                        if ($lowStock > 0) {
                                            echo "<small class='text-warning'><i class='fas fa-exclamation-triangle me-1'></i>$lowStock low stock</small>";
                                        }
                                    } catch (Exception $e) {}
                                    ?>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-box fa-2x text-primary opacity-25"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="text-success mb-1"><?php echo $totalCategories; ?></h3>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-tags me-2"></i>Categories
                                    </p>
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT c.name, COUNT(p.product_id) as product_count FROM categories c LEFT JOIN products p ON c.category_id = p.category_id GROUP BY c.category_id ORDER BY product_count DESC LIMIT 1");
                                        $topCategory = $stmt->fetch();
                                        if ($topCategory) {
                                            echo "<small class='text-muted'>Top: " . htmlspecialchars($topCategory['name']) . " ({$topCategory['product_count']})</small>";
                                        }
                                    } catch (Exception $e) {}
                                    ?>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-tags fa-2x text-success opacity-25"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="text-warning mb-1"><?php echo $totalUsers; ?></h3>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-users me-2"></i>Total Users
                                    </p>
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT COUNT(*) as new_users FROM users WHERE role = 'customer' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
                                        $newUsers = $stmt->fetch()['new_users'] ?? 0;
                                        echo "<small class='text-success'>+$newUsers this week</small>";
                                    } catch (Exception $e) {}
                                    ?>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-users fa-2x text-warning opacity-25"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3 class="text-info mb-1"><?php echo $totalOrders; ?></h3>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-shopping-cart me-2"></i>Total Orders
                                    </p>
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT COUNT(*) as pending_orders FROM orders WHERE order_status = 'pending'");
                                        $pendingOrders = $stmt->fetch()['pending_orders'] ?? 0;
                                        if ($pendingOrders > 0) {
                                            echo "<small class='text-warning'><i class='fas fa-clock me-1'></i>$pendingOrders pending</small>";
                                        } else {
                                            echo "<small class='text-success'><i class='fas fa-check me-1'></i>All processed</small>";
                                        }
                                    } catch (Exception $e) {}
                                    ?>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-shopping-cart fa-2x text-info opacity-25"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Analytics Row -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>Sales Analytics
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>Order Status Distribution
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="orderStatusChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Revenue and Performance Metrics -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT COALESCE(SUM(total_amount), 0) as total_revenue FROM orders WHERE order_status != 'cancelled'");
                                        $totalRevenue = $stmt->fetch()['total_revenue'] ?? 0;
                                    } catch (Exception $e) {
                                        $totalRevenue = 0;
                                    }
                                    ?>
                                    <h3 class="text-success mb-1">Rp<?php echo number_format($totalRevenue, 0, ',', '.'); ?></h3>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-money-bill-wave me-2"></i>Total Revenue
                                    </p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-money-bill-wave fa-2x text-success opacity-25"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT COALESCE(AVG(total_amount), 0) as avg_order FROM orders WHERE order_status != 'cancelled'");
                                        $avgOrder = $stmt->fetch()['avg_order'] ?? 0;
                                    } catch (Exception $e) {
                                        $avgOrder = 0;
                                    }
                                    ?>
                                    <h3 class="text-primary mb-1">Rp<?php echo number_format($avgOrder, 0, ',', '.'); ?></h3>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-calculator me-2"></i>Avg Order Value
                                    </p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-calculator fa-2x text-primary opacity-25"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT COALESCE(SUM(stock * price), 0) as inventory_value FROM products");
                                        $inventoryValue = $stmt->fetch()['inventory_value'] ?? 0;
                                    } catch (Exception $e) {
                                        $inventoryValue = 0;
                                    }
                                    ?>
                                    <h3 class="text-warning mb-1">Rp<?php echo number_format($inventoryValue, 0, ',', '.'); ?></h3>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-warehouse me-2"></i>Inventory Value
                                    </p>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-warehouse fa-2x text-warning opacity-25"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT COUNT(DISTINCT user_id) as active_customers FROM orders WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)");
                                        $activeCustomers = $stmt->fetch()['active_customers'] ?? 0;
                                    } catch (Exception $e) {
                                        $activeCustomers = 0;
                                    }
                                    ?>
                                    <h3 class="text-info mb-1"><?php echo $activeCustomers; ?></h3>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-user-check me-2"></i>Active Customers
                                    </p>
                                    <small class="text-muted">Last 30 days</small>
                                </div>
                                <div class="stat-icon">
                                    <i class="fas fa-user-check fa-2x text-info opacity-25"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Products Table -->
                <div class="card mt-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><a href="products_admin.php" style="text-decoration:none; color:inherit;">Recent Products</a></h5>
                        <a href="products_admin.php" class="btn btn-outline-primary btn-sm">Lihat Semua Produk</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Image</th>
                                        <th>Name</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Stock</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <?php
                                    try {
                                        $stmt = $conn->query("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.category_id ORDER BY p.created_at DESC LIMIT 10");
                                        while ($product = $stmt->fetch()) {
                                            echo "<tr>";
                                            echo "<td>{$product['product_id']}</td>";
                                            echo "<td>";
                                            if (!empty($product['image'])) {
                                                echo "<img src='../assets/img/{$product['image']}' class='product-image-preview' onerror=\"this.src='../assets/img/no-image.jpg'\">";
                                            } else {
                                                echo "<img src='../assets/img/no-image.jpg' class='product-image-preview'>";
                                            }
                                            echo "</td>";
                                            echo "<td><a href='edit-product.php?id={$product['product_id']}' class='text-decoration-underline'>{$product['NAME']}</a></td>";
                                            echo "<td>" . htmlspecialchars($product['category_name'] ?? 'No Category') . "</td>";
                                            echo "<td>Rp" . number_format($product['price'], 0, ',', '.') . "</td>";
                                            echo "<td>{$product['stock']}</td>";
                                            echo "<td>
                                                    <div class='btn-group'>
                                                        <a href='edit-product.php?id={$product['product_id']}' class='btn btn-sm btn-warning'><i class='fas fa-edit'></i></a>
                                                        <button type='button' class='btn btn-sm btn-danger' onclick='deleteProduct({$product['product_id']})'>
                                                            <i class='fas fa-trash'></i>
                                                        </button>
                                                    </div>
                                                  </td>";
                                            echo "</tr>";
                                        }
                                    } catch (Exception $e) {
                                        echo "<tr><td colspan='7' class='text-center text-muted'>Error loading products: " . htmlspecialchars($e->getMessage()) . "</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end mt-3">
                            <a href="products_admin.php" class="btn btn-primary">Lihat Semua Produk</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Product Modal -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Product</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label class="form-label">Product Name</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Category</label>
                            <select class="form-select" name="category" required>
                                <option value="">Select Category</option>
                                <?php
                                try {
                                    $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
                                    while ($category = $stmt->fetch()) {
                                        echo "<option value='{$category['category_id']}'>" . htmlspecialchars($category['name']) . "</option>";
                                    }
                                } catch (Exception $e) {
                                    echo "<option value=''>Error loading categories</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Price</label>
                            <input type="number" class="form-control" name="price" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Stock</label>
                            <input type="number" class="form-control" name="stock" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Product Image</label>
                            <input type="file" class="form-control" name="image" accept="image/*">
                            <div id="addImagePreview" class="mt-2"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveProduct()">Save Product</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Product Modal -->
    <div class="modal fade" id="editProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Product</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editProductForm" enctype="multipart/form-data">
                        <input type="hidden" name="id">
                        <div class="mb-3">
                            <label class="form-label">Product Name</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Category</label>
                            <select class="form-select" name="category" required>
                                <option value="">Select Category</option>
                                <?php
                                try {
                                    $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
                                    while ($category = $stmt->fetch()) {
                                        echo "<option value='{$category['category_id']}'>" . htmlspecialchars($category['name']) . "</option>";
                                    }
                                } catch (Exception $e) {
                                    echo "<option value=''>Error loading categories</option>";
                                }
                                ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Price</label>
                            <input type="number" class="form-control" name="price" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Stock</label>
                            <input type="number" class="form-control" name="stock" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Product Image</label>
                            <input type="file" class="form-control" name="image" accept="image/*">
                            <div id="currentImage" class="mt-2"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateProduct()">Update Product</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCategoryForm">
                        <div class="mb-3">
                            <label class="form-label">Category Name</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveCategory()">Save Category</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Edit Stok -->
    <div class="modal fade" id="editStockModal" tabindex="-1" aria-labelledby="editStockModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="editStockForm">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editStockModalLabel">Edit Stok Produk</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="product_id" id="editStockProductId">
                        <div class="mb-3">
                            <label for="editStockProductName" class="form-label">Nama Produk</label>
                            <input type="text" class="form-control" id="editStockProductName" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editStockOldValue" class="form-label">Stok Lama</label>
                            <input type="number" class="form-control" id="editStockOldValue" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editStockValue" class="form-label">Stok</label>
                            <input type="number" class="form-control" name="stock" id="editStockValue" min="0" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Update Stok</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Global loading overlay
        const loadingOverlay = document.querySelector('.loading-overlay');

        function showLoading() {
            loadingOverlay.style.display = 'flex';
        }

        function hideLoading() {
            loadingOverlay.style.display = 'none';
        }

        // Hide loading overlay on page load
        document.addEventListener('DOMContentLoaded', function() {
            hideLoading();
        });

        // Improved toast notification
        function showToast(message, type = 'success') {
            Toastify({
                text: message,
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: type === 'success' ? "#28a745" : "#dc3545",
                stopOnFocus: true,
                onClick: function(){} // Callback after click
            }).showToast();
        }

        // Advanced edit product function
        async function editProduct(id, event) {
            try {
                // Make event handling optional
                if (event) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                // Show loading overlay
                showLoading();

                // Get product details
                const response = await fetch(`product_operations.php?action=get&id=${id}`);
                if (!response.ok) throw new Error('Failed to fetch product details');

                const result = await response.json();
                if (result.status === 'error') throw new Error(result.message);
                if (!result.data) throw new Error('No product data received');

                const product = result.data;

                // Get the form and reset it
                const form = document.getElementById('editProductForm');
                form.reset();

                // Fill form with product details
                Object.keys(product).forEach(key => {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input) {
                        input.value = product[key];
                        // Remove any previous validation classes
                        input.classList.remove('is-invalid');
                    }
                });

                // Handle image preview
                const currentImage = document.getElementById('currentImage');
                currentImage.innerHTML = product.image
                    ? `<div class="image-preview-container">
                        <img src="../uploads/${product.image}" class="product-image-preview"
                             onerror="this.src='../assets/img/no-image.jpg'">
                        <div class="remove-image" onclick="removeProductImage(${product.id})">
                            <i class="fas fa-times"></i>
                        </div>
                       </div>`
                    : '<img src="../assets/img/no-image.jpg" class="product-image-preview">';

                // Show modal
                const editModal = new bootstrap.Modal(document.getElementById('editProductModal'));
                editModal.show();

            } catch (error) {
                console.error('Error:', error);
                showToast(error.message || 'Error loading product details', 'error');
            } finally {
                hideLoading();
            }
        }

        // Advanced update product function
        async function updateProduct() {
            try {
                const form = document.getElementById('editProductForm');

                // Advanced validation
                const requiredFields = ['name', 'category', 'price', 'stock'];
                let isValid = true;
                const errors = {};

                requiredFields.forEach(field => {
                    const input = form.querySelector(`[name="${field}"]`);
                    const value = input.value.trim();

                    input.classList.remove('is-invalid');

                    if (!value) {
                        input.classList.add('is-invalid');
                        errors[field] = 'This field is required';
                        isValid = false;
                    } else if (field === 'price' && (isNaN(value) || parseFloat(value) <= 0)) {
                        input.classList.add('is-invalid');
                        errors[field] = 'Price must be a positive number';
                        isValid = false;
                    } else if (field === 'stock' && (isNaN(value) || parseInt(value) < 0)) {
                        input.classList.add('is-invalid');
                        errors[field] = 'Stock cannot be negative';
                        isValid = false;
                    }
                });

                if (!isValid) {
                    Object.keys(errors).forEach(field => {
                        const input = form.querySelector(`[name="${field}"]`);
                        const feedback = input.nextElementSibling;
                        if (feedback && feedback.classList.contains('invalid-feedback')) {
                            feedback.textContent = errors[field];
                        }
                    });
                    return;
                }

                showLoading();

                const formData = new FormData(form);
                formData.append('action', 'edit');

                const response = await fetch('product_operations.php', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) throw new Error('Network response was not ok');

                const data = await response.json();

                if (data.status === 'success') {
                    showToast(data.message);
                    bootstrap.Modal.getInstance(document.getElementById('editProductModal')).hide();
                    // Reload only the products table instead of full page
                    await refreshProductsTable();
                } else {
                    throw new Error(data.message || 'Error updating product');
                }

            } catch (error) {
                console.error('Error:', error);
                showToast(error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // Function to refresh only the products table
        async function refreshProductsTable() {
            try {
                const response = await fetch('get_products_table.php');
                if (!response.ok) throw new Error('Failed to refresh table');

                const html = await response.text();
                document.getElementById('productsTableBody').innerHTML = html;

            } catch (error) {
                console.error('Error:', error);
                showToast('Error refreshing table. Please reload the page.', 'error');
            }
        }

        // Advanced image removal function
        async function removeProductImage(productId) {
            try {
                if (!confirm('Are you sure you want to remove this image?')) return;

                showLoading();

                const formData = new FormData();
                formData.append('action', 'remove_image');
                formData.append('id', productId);

                const response = await fetch('product_operations.php', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) throw new Error('Network response was not ok');

                const data = await response.json();

                if (data.status === 'success') {
                    document.getElementById('currentImage').innerHTML =
                        '<img src="../assets/img/no-image.jpg" class="product-image-preview">';
                    showToast('Image removed successfully');
                    await refreshProductsTable();
                } else {
                    throw new Error(data.message || 'Error removing image');
                }

            } catch (error) {
                console.error('Error:', error);
                showToast(error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        // Image preview function
        function previewImage(input, previewId) {
            const preview = document.getElementById(previewId);
            const file = input.files[0];

            if (file) {
                // Validate file type
                const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!validTypes.includes(file.type)) {
                    showToast('Please select a valid image file (JPEG, PNG, or GIF)', 'error');
                    input.value = '';
                    return;
                }

                // Validate file size (max 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    showToast('Image size should not exceed 2MB', 'error');
                    input.value = '';
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `
                        <div class="image-preview-container">
                            <img src="${e.target.result}" class="product-image-preview">
                            <div class="remove-image" onclick="clearImagePreview('${previewId}', '${input.id}')">
                                <i class="fas fa-times"></i>
                            </div>
                        </div>`;
                };
                reader.readAsDataURL(file);
            }
        }

        // Clear image preview
        function clearImagePreview(previewId, inputId) {
            document.getElementById(previewId).innerHTML =
                '<img src="../assets/img/no-image.jpg" class="product-image-preview">';
            document.getElementById(inputId).value = '';
        }

        // Add event listeners when document is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Add image preview listeners
            const addImageInput = document.querySelector('#addProductForm [name="image"]');
            const editImageInput = document.querySelector('#editProductForm [name="image"]');

            if (addImageInput) {
                addImageInput.addEventListener('change', function() {
                    previewImage(this, 'addImagePreview');
                });
            }

            if (editImageInput) {
                editImageInput.addEventListener('change', function() {
                    previewImage(this, 'currentImage');
                });
            }

            // Add form submission listeners
            const addForm = document.getElementById('addProductForm');
            const editForm = document.getElementById('editProductForm');

            if (addForm) {
                addForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    saveProduct();
                });
            }

            if (editForm) {
                editForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    updateProduct();
                });
            }
        });

        document.querySelectorAll('a.btn-primary').forEach(a => {
            a.addEventListener('click', function(e) {
                // Untuk debugging, log ke console
                console.log('Edit Stok diklik', this.href);
                // Jangan preventDefault di sini!
            });
        });

        function showEditStockModal(productId, productName, stock) {
            document.getElementById('editStockProductId').value = productId;
            document.getElementById('editStockProductName').value = productName;
            document.getElementById('editStockOldValue').value = stock;
            document.getElementById('editStockValue').value = stock;
            var modal = new bootstrap.Modal(document.getElementById('editStockModal'));
            modal.show();
        }

        document.getElementById('editStockForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const productId = document.getElementById('editStockProductId').value;
            const newStock = parseInt(document.getElementById('editStockValue').value);
            if (isNaN(newStock) || newStock < 0) {
                alert('Stok tidak boleh negatif!');
                return;
            }
            try {
                showLoading();
                const response = await fetch('product_actions.php?action=update_stock', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                    body: `product_id=${encodeURIComponent(productId)}&new_stock=${encodeURIComponent(newStock)}`
                });
                const result = await response.json();
                if (result.success) {
                    showToast('Stok berhasil diperbarui!');
                    bootstrap.Modal.getInstance(document.getElementById('editStockModal')).hide();
                    await refreshProductsTable();
                } else {
                    showToast(result.message || 'Gagal update stok', 'error');
                }
            } catch (err) {
                showToast('Gagal update stok', 'error');
            } finally {
                hideLoading();
            }
        });

        // Initialize Charts
        document.addEventListener('DOMContentLoaded', function() {
            initializeSalesChart();
            initializeOrderStatusChart();
        });

        // Sales Chart
        async function initializeSalesChart() {
            try {
                const response = await fetch('analytics_data.php?type=sales');
                const data = await response.json();

                const ctx = document.getElementById('salesChart').getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                        datasets: [{
                            label: 'Sales (Rp)',
                            data: data.sales || [0, 0, 0, 0, 0, 0],
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return 'Rp' + value.toLocaleString();
                                    }
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error loading sales chart:', error);
            }
        }

        // Order Status Chart
        async function initializeOrderStatusChart() {
            try {
                const response = await fetch('analytics_data.php?type=order_status');
                const data = await response.json();

                const ctx = document.getElementById('orderStatusChart').getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.labels || ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'],
                        datasets: [{
                            data: data.values || [0, 0, 0, 0, 0],
                            backgroundColor: [
                                '#ffc107',
                                '#17a2b8',
                                '#007bff',
                                '#28a745',
                                '#dc3545'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error loading order status chart:', error);
            }
        }
    </script>

<?php require_once 'includes/footer.php'; ?>
