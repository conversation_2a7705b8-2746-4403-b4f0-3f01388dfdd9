-- Fix reservation logic to handle cart updates properly

-- Drop and recreate the ReserveStock procedure with better logic
DROP PROCEDURE IF EXISTS ReserveStock;

DELIMITER //
CREATE PROCEDURE ReserveStock(
    IN p_product_id INT,
    IN p_user_id INT,
    IN p_session_id VARCHAR(255),
    IN p_quantity INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE available_stock INT DEFAULT 0;
    DECLARE existing_reservation INT DEFAULT 0;
    DECLARE actual_stock INT DEFAULT 0;
    DECLARE total_reserved INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Database error occurred';
    END;
    
    START TRANSACTION;
    
    -- Clean up expired reservations first
    DELETE FROM stock_reservations 
    WHERE expires_at < NOW() OR status = 'expired';
    
    -- Get actual stock
    SELECT stock INTO actual_stock
    FROM products 
    WHERE product_id = p_product_id;
    
    -- Get current user's existing reservation
    SELECT COALESCE(SUM(quantity), 0) INTO existing_reservation
    FROM stock_reservations 
    WHERE product_id = p_product_id 
    AND (user_id = p_user_id OR session_id = p_session_id)
    AND status = 'active' 
    AND expires_at > NOW();
    
    -- Get total reservations from all other users
    SELECT COALESCE(SUM(quantity), 0) INTO total_reserved
    FROM stock_reservations 
    WHERE product_id = p_product_id 
    AND NOT (user_id = p_user_id OR session_id = p_session_id)
    AND status = 'active' 
    AND expires_at > NOW();
    
    -- Calculate available stock (actual stock minus other users' reservations)
    SET available_stock = actual_stock - total_reserved;
    
    -- Check if there's enough stock for the requested quantity
    IF available_stock < p_quantity THEN
        SET p_success = FALSE;
        SET p_message = CONCAT('Stok tidak mencukupi. Stok tersedia: ', available_stock, ', diminta: ', p_quantity);
        ROLLBACK;
    ELSE
        -- Remove existing reservation for this user/session if any
        DELETE FROM stock_reservations 
        WHERE product_id = p_product_id 
        AND (user_id = p_user_id OR session_id = p_session_id)
        AND status = 'active';
        
        -- Create new reservation with the requested quantity
        INSERT INTO stock_reservations (product_id, user_id, session_id, quantity, expires_at)
        VALUES (p_product_id, p_user_id, p_session_id, p_quantity, CURRENT_TIMESTAMP + INTERVAL 30 MINUTE);
        
        -- Update reserved_stock in products table
        UPDATE products 
        SET reserved_stock = (
            SELECT COALESCE(SUM(quantity), 0) 
            FROM stock_reservations 
            WHERE product_id = p_product_id 
            AND status = 'active' 
            AND expires_at > NOW()
        )
        WHERE product_id = p_product_id;
        
        SET p_success = TRUE;
        SET p_message = CONCAT('Stock reserved successfully. Reserved: ', p_quantity, ', Available: ', available_stock - p_quantity);
        COMMIT;
    END IF;
END//
DELIMITER ;

-- Create a procedure to get detailed stock info for debugging
DELIMITER //
CREATE PROCEDURE GetStockInfo(
    IN p_product_id INT,
    IN p_user_id INT,
    IN p_session_id VARCHAR(255)
)
BEGIN
    SELECT 
        p.product_id,
        p.name,
        p.stock as actual_stock,
        p.reserved_stock,
        (p.stock - p.reserved_stock) as calculated_available,
        GetAvailableStock(p.product_id) as function_available,
        COALESCE(user_reserved.quantity, 0) as user_reservation,
        COALESCE(total_reserved.total, 0) as total_reservations
    FROM products p
    LEFT JOIN (
        SELECT product_id, SUM(quantity) as quantity
        FROM stock_reservations 
        WHERE product_id = p_product_id
        AND (user_id = p_user_id OR session_id = p_session_id)
        AND status = 'active' 
        AND expires_at > NOW()
        GROUP BY product_id
    ) user_reserved ON p.product_id = user_reserved.product_id
    LEFT JOIN (
        SELECT product_id, SUM(quantity) as total
        FROM stock_reservations 
        WHERE product_id = p_product_id
        AND status = 'active' 
        AND expires_at > NOW()
        GROUP BY product_id
    ) total_reserved ON p.product_id = total_reserved.product_id
    WHERE p.product_id = p_product_id;
END//
DELIMITER ;
