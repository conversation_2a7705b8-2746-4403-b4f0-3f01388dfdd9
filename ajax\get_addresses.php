<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    
    // Get all addresses for the user
    $stmt = $conn->prepare("
        SELECT address_id as id, label, recipient_name, phone, address, 
               city, province, postal_code, is_default, created_at
        FROM user_addresses 
        WHERE user_id = ? 
        ORDER BY is_default DESC, created_at DESC
    ");
    $stmt->execute([$user_id]);
    $addresses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Convert is_default to boolean
    foreach ($addresses as &$address) {
        $address['is_default'] = (bool)$address['is_default'];
    }
    
    echo json_encode([
        'success' => true,
        'addresses' => $addresses,
        'count' => count($addresses)
    ]);
    
} catch (Exception $e) {
    error_log("Get addresses error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while loading addresses']);
}
?>
