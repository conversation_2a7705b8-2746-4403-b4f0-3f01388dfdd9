<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

try {
    // Get request data with enhanced validation
    $raw_input = file_get_contents('php://input');
    error_log("Cancel Order - Raw input: " . $raw_input);

    $input = json_decode($raw_input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON input: ' . json_last_error_msg());
    }

    if (!isset($input['order_id'])) {
        throw new Exception('Missing order_id parameter');
    }

    $order_id = (int)$input['order_id'];
    $user_id = (int)$_SESSION['user_id'];

    if ($order_id <= 0) {
        throw new Exception('Invalid order ID: ' . $order_id);
    }

    if ($user_id <= 0) {
        throw new Exception('Invalid user ID: ' . $user_id);
    }

    error_log("Cancel Order - Processing: Order ID: {$order_id}, User ID: {$user_id}");
    
    // Check if order belongs to user and can be cancelled
    $stmt = $conn->prepare("
        SELECT order_id, order_number, order_status, user_id
        FROM orders
        WHERE order_id = ? AND user_id = ?
    ");
    $stmt->execute([$order_id, $user_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        throw new Exception('Order not found or access denied');
    }

    // Handle empty or null status - set default to pending
    $current_status = $order['order_status'];
    if (empty($current_status) || is_null($current_status)) {
        $current_status = 'pending';
        // Update the order with default status
        $update_default_stmt = $conn->prepare("UPDATE orders SET order_status = 'pending' WHERE order_id = ?");
        $update_default_stmt->execute([$order_id]);
        error_log("Order {$order_id} had empty status, set to 'pending'");
    }

    $current_status = strtolower(trim($current_status));

    // Check if order can be cancelled - expand cancellable statuses
    $cancellable_statuses = ['pending', 'dibuat', 'diproses', 'processing'];

    // Debug logging
    error_log("Cancel Order Debug - Order ID: {$order_id}, Raw Status: '{$order['order_status']}', Processed Status: '{$current_status}', Cancellable: " . json_encode($cancellable_statuses));

    if (!in_array($current_status, $cancellable_statuses)) {
        throw new Exception("Order cannot be cancelled at this stage. Current status: '{$current_status}'. Cancellable statuses: " . implode(', ', $cancellable_statuses));
    }
    
    // Check if cancelled_at and cancelled_by columns exist
    $columns_check = $conn->query("SHOW COLUMNS FROM orders LIKE 'cancelled_at'");
    $has_cancelled_columns = $columns_check->rowCount() > 0;

    // Update order status to cancelled
    if ($has_cancelled_columns) {
        $update_stmt = $conn->prepare("
            UPDATE orders
            SET order_status = 'dibatalkan',
                updated_at = NOW(),
                cancelled_at = NOW(),
                cancelled_by = 'customer'
            WHERE order_id = ? AND user_id = ?
        ");
    } else {
        $update_stmt = $conn->prepare("
            UPDATE orders
            SET order_status = 'dibatalkan',
                updated_at = NOW()
            WHERE order_id = ? AND user_id = ?
        ");
    }

    $success = $update_stmt->execute([$order_id, $user_id]);
    $affected = $update_stmt->rowCount();

    // Verify the update was successful
    $verify_stmt = $conn->prepare("SELECT order_status FROM orders WHERE order_id = ?");
    $verify_stmt->execute([$order_id]);
    $current_status = $verify_stmt->fetchColumn();

    // Debug logging for cancel operation
    $success_text = $success ? 'true' : 'false';
    error_log("Cancel Order - Order ID: {$order_id}, Success: {$success_text}, Affected: {$affected}, Current Status: '{$current_status}'");
    
    if (!$success || $affected === 0) {
        throw new Exception('Failed to cancel order');
    }
    
    // Add to order status history
    try {
        $history_stmt = $conn->prepare("
            INSERT INTO order_status_history 
            (order_id, previous_status, new_status, notes, admin_id, created_at) 
            VALUES (?, ?, 'dibatalkan', 'Order cancelled by customer', ?, NOW())
        ");
        $history_stmt->execute([
            $order_id, 
            $order['order_status'], 
            $user_id
        ]);
    } catch (Exception $e) {
        // History insert failed, but order cancellation succeeded
        error_log("Failed to insert order status history: " . $e->getMessage());
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Order cancelled successfully',
        'order_id' => $order_id,
        'order_number' => $order['order_number'] ?? "#$order_id",
        'new_status' => 'dibatalkan'
    ]);
    
} catch (Exception $e) {
    // Enhanced error logging
    error_log("Cancel Order Error - " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    error_log("Cancel Order Error - Stack trace: " . $e->getTraceAsString());

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => $_SESSION['user_id'] ?? 'not_set',
            'post_data' => file_get_contents('php://input')
        ]
    ]);
}
?>
