<?php
session_start();
require_once '../config.php';
require_once '../includes/db_connect.php';

// Check if user is logged in, set up session ID for guest carts
if (!isset($_SESSION['user_id'])) {
    if (!isset($_SESSION['session_id'])) {
        $_SESSION['session_id'] = session_id();
    }
}

// Initialize response array
$response = ['success' => false, 'message' => '', 'count' => 0, 'total' => 0, 'items' => []];

// Get action from POST request
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Log the incoming request for debugging
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    file_put_contents('../debug_cart.txt', date('Y-m-d H:i:s') . " - Action: $action - Data: " . print_r($_POST, true) . "\n", FILE_APPEND);
}

try {
    // Handle different cart actions
    switch ($action) {
        case 'add':
            // Validate product id and quantity
            $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
            $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
            
            if ($product_id <= 0) {
                throw new Exception("Invalid product ID");
            }
            
            if ($quantity <= 0) {
                throw new Exception("Quantity must be greater than zero");
            }
            
            // Check if product exists and is in stock
            $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ? AND is_active = 1");
            $stmt->execute([$product_id]);
            $product = $stmt->fetch();
            
            if (!$product) {
                throw new Exception("Product not found or inactive");
            }
            
            if ($product['stock'] < $quantity) {
                throw new Exception("Not enough stock available. Only " . $product['stock'] . " left.");
            }
            
            // Check if product is already in cart
            if (isset($_SESSION['user_id'])) {
                $stmt = $conn->prepare("SELECT * FROM cart WHERE user_id = ? AND product_id = ?");
                $stmt->execute([$_SESSION['user_id'], $product_id]);
            } else {
                $stmt = $conn->prepare("SELECT * FROM cart WHERE session_id = ? AND product_id = ?");
                $stmt->execute([$_SESSION['session_id'], $product_id]);
            }
            
            $cartItem = $stmt->fetch();
            
            if ($cartItem) {
                // Update quantity if already in cart
                $newQuantity = $cartItem['quantity'] + $quantity;
                
                // Check if new quantity exceeds stock
                if ($newQuantity > $product['stock']) {
                    $newQuantity = $product['stock'];
                    $response['message'] = "Cart updated to maximum available stock (" . $product['stock'] . ").";
                }
                
                if (isset($_SESSION['user_id'])) {
                    $stmt = $conn->prepare("UPDATE cart SET quantity = ?, updated_at = NOW() WHERE cart_id = ?");
                    $stmt->execute([$newQuantity, $cartItem['cart_id']]);
                } else {
                    $stmt = $conn->prepare("UPDATE cart SET quantity = ?, updated_at = NOW() WHERE cart_id = ?");
                    $stmt->execute([$newQuantity, $cartItem['cart_id']]);
                }
                
                $response['success'] = true;
                $response['message'] = empty($response['message']) ? "Product quantity updated in cart" : $response['message'];
            } else {
                // Add new item to cart
                if (isset($_SESSION['user_id'])) {
                    $stmt = $conn->prepare("INSERT INTO cart (user_id, product_id, quantity, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
                    $stmt->execute([$_SESSION['user_id'], $product_id, $quantity]);
                } else {
                    $stmt = $conn->prepare("INSERT INTO cart (session_id, product_id, quantity, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
                    $stmt->execute([$_SESSION['session_id'], $product_id, $quantity]);
                }
                
                $response['success'] = true;
                $response['message'] = "Product added to cart successfully";
            }
            
            // Get updated cart count and total
            getCartSummary($conn, $response);
            break;
            
        case 'update':
            // Validate cart id and quantity
            $cart_id = isset($_POST['cart_id']) ? intval($_POST['cart_id']) : 0;
            $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 0;
            
            if ($cart_id <= 0) {
                throw new Exception("Invalid cart item");
            }
            
            // Check if cart item exists and belongs to user
            if (isset($_SESSION['user_id'])) {
                $stmt = $conn->prepare("SELECT c.*, p.stock, p.price 
                                        FROM cart c 
                                        JOIN products p ON c.product_id = p.product_id 
                                        WHERE c.cart_id = ? AND c.user_id = ?");
                $stmt->execute([$cart_id, $_SESSION['user_id']]);
            } else {
                $stmt = $conn->prepare("SELECT c.*, p.stock, p.price 
                                        FROM cart c 
                                        JOIN products p ON c.product_id = p.product_id 
                                        WHERE c.cart_id = ? AND c.session_id = ?");
                $stmt->execute([$cart_id, $_SESSION['session_id']]);
            }
            
            $cartItem = $stmt->fetch();
            
            if (!$cartItem) {
                throw new Exception("Cart item not found");
            }
            
            if ($quantity <= 0) {
                // Remove item from cart
                $stmt = $conn->prepare("DELETE FROM cart WHERE cart_id = ?");
                $stmt->execute([$cart_id]);
                
                $response['success'] = true;
                $response['message'] = "Item removed from cart";
            } else {
                // Check if quantity exceeds stock
                if ($quantity > $cartItem['stock']) {
                    $quantity = $cartItem['stock'];
                    $response['message'] = "Quantity adjusted to maximum available stock (" . $cartItem['stock'] . ").";
                }
                
                // Update quantity
                $stmt = $conn->prepare("UPDATE cart SET quantity = ?, updated_at = NOW() WHERE cart_id = ?");
                $stmt->execute([$quantity, $cart_id]);
                
                $response['success'] = true;
                $response['message'] = empty($response['message']) ? "Cart updated successfully" : $response['message'];
                $response['item_total'] = number_format($quantity * $cartItem['price'], 0);
            }
            
            // Get updated cart count and total
            getCartSummary($conn, $response);
            break;
            
        case 'remove':
            // Validate product_id or cart_id
            $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
            $cart_id = isset($_POST['cart_id']) ? intval($_POST['cart_id']) : 0;
            
            // Allow removal by either product_id or cart_id
            if ($product_id <= 0 && $cart_id <= 0) {
                throw new Exception("Invalid cart item: No product_id or cart_id provided");
            }
            
            // Check if cart item exists and belongs to user
            if ($cart_id > 0) {
                // Remove by cart_id
                if (isset($_SESSION['user_id'])) {
                    $stmt = $conn->prepare("SELECT * FROM cart WHERE cart_id = ? AND user_id = ?");
                    $stmt->execute([$cart_id, $_SESSION['user_id']]);
                } else {
                    $stmt = $conn->prepare("SELECT * FROM cart WHERE cart_id = ? AND session_id = ?");
                    $stmt->execute([$cart_id, $_SESSION['session_id']]);
                }
            } else {
                // Remove by product_id
                if (isset($_SESSION['user_id'])) {
                    $stmt = $conn->prepare("SELECT * FROM cart WHERE product_id = ? AND user_id = ?");
                    $stmt->execute([$product_id, $_SESSION['user_id']]);
                } else {
                    $stmt = $conn->prepare("SELECT * FROM cart WHERE product_id = ? AND session_id = ?");
                    $stmt->execute([$product_id, $_SESSION['session_id']]);
                }
            }
            
            $cartItem = $stmt->fetch();
            
            if (!$cartItem) {
                throw new Exception("Cart item not found");
            }
            
            // Remove item from cart
            if ($cart_id > 0) {
                $stmt = $conn->prepare("DELETE FROM cart WHERE cart_id = ?");
                $stmt->execute([$cart_id]);
            } else {
                if (isset($_SESSION['user_id'])) {
                    $stmt = $conn->prepare("DELETE FROM cart WHERE product_id = ? AND user_id = ?");
                    $stmt->execute([$product_id, $_SESSION['user_id']]);
                } else {
                    $stmt = $conn->prepare("DELETE FROM cart WHERE product_id = ? AND session_id = ?");
                    $stmt->execute([$product_id, $_SESSION['session_id']]);
                }
            }
            
            $response['success'] = true;
            $response['message'] = "Item removed from cart";
            
            // Get updated cart count and total
            getCartSummary($conn, $response);
            break;
            
        case 'clear':
            // Clear all items from cart
            if (isset($_SESSION['user_id'])) {
                $stmt = $conn->prepare("DELETE FROM cart WHERE user_id = ?");
                $stmt->execute([$_SESSION['user_id']]);
            } else {
                $stmt = $conn->prepare("DELETE FROM cart WHERE session_id = ?");
                $stmt->execute([$_SESSION['session_id']]);
            }
            
            // Also clear session cart data if exists
            if (isset($_SESSION['cart'])) {
                $_SESSION['cart'] = [];
            }
            
            $response['success'] = true;
            $response['message'] = "Cart cleared successfully";
            $response['count'] = 0;
            $response['total'] = 0;
            $response['items'] = [];
            break;
            
        case 'count':
            // Just get cart count and total
            getCartSummary($conn, $response);
            $response['success'] = true;
            break;
            
        case 'get':
            // Get all cart items with product details
            if (isset($_SESSION['user_id'])) {
                $stmt = $conn->prepare("
                    SELECT c.*, p.name, p.price, p.image, p.stock 
                    FROM cart c 
                    JOIN products p ON c.product_id = p.product_id 
                    WHERE c.user_id = ?
                    ORDER BY c.created_at DESC
                ");
                $stmt->execute([$_SESSION['user_id']]);
            } else {
                $stmt = $conn->prepare("
                    SELECT c.*, p.name, p.price, p.image, p.stock 
                    FROM cart c 
                    JOIN products p ON c.product_id = p.product_id 
                    WHERE c.session_id = ?
                    ORDER BY c.created_at DESC
                ");
                $stmt->execute([$_SESSION['session_id']]);
            }
            
            $items = $stmt->fetchAll();
            $cartItems = [];
            $total = 0;
            
            foreach ($items as $item) {
                $itemTotal = $item['quantity'] * $item['price'];
                $total += $itemTotal;
                
                $cartItems[] = [
                    'cart_id' => $item['cart_id'],
                    'product_id' => $item['product_id'],
                    'name' => $item['name'],
                    'price' => $item['price'],
                    'quantity' => $item['quantity'],
                    'image' => $item['image'],
                    'stock' => $item['stock'],
                    'item_total' => $itemTotal,
                    'formatted_price' => number_format($item['price'], 0),
                    'formatted_total' => number_format($itemTotal, 0)
                ];
            }
            
            $response['success'] = true;
            $response['count'] = count($items);
            $response['total'] = $total;
            $response['formatted_total'] = number_format($total, 0);
            $response['items'] = $cartItems;
            break;
            
        default:
            throw new Exception("Invalid action");
    }
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = $e->getMessage();
    
    // Log error
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("Cart Error: " . $e->getMessage());
    }
}

// Helper function to get cart count and total
function getCartSummary($conn, &$response) {
    try {
        if (isset($_SESSION['user_id'])) {
            // Get count
            $stmt = $conn->prepare("SELECT COUNT(*) FROM cart WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $response['count'] = $stmt->fetchColumn();
            
            // Get total
            $stmt = $conn->prepare("
                SELECT SUM(c.quantity * p.price) as total
                FROM cart c
                JOIN products p ON c.product_id = p.product_id
                WHERE c.user_id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
        } else {
            // Get count
            $stmt = $conn->prepare("SELECT COUNT(*) FROM cart WHERE session_id = ?");
            $stmt->execute([$_SESSION['session_id']]);
            $response['count'] = $stmt->fetchColumn();
            
            // Get total
            $stmt = $conn->prepare("
                SELECT SUM(c.quantity * p.price) as total
                FROM cart c
                JOIN products p ON c.product_id = p.product_id
                WHERE c.session_id = ?
            ");
            $stmt->execute([$_SESSION['session_id']]);
        }
        
        $total = $stmt->fetchColumn();
        $response['total'] = $total ?: 0;
        $response['formatted_total'] = number_format($response['total'], 0);
    } catch (Exception $e) {
        // Silently handle errors in this helper function
        error_log("Error in getCartSummary: " . $e->getMessage());
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);