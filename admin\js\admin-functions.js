/**
 * Admin Functions for TewuNeed
 * Enhanced admin functionality with better UX
 */

console.log('admin-functions.js loaded successfully');

// Print order function
function printOrder(orderId) {
    const printWindow = window.open(`print-order.php?id=${orderId}`, '_blank', 'width=800,height=600');
    if (printWindow) {
        printWindow.onload = function() {
            printWindow.print();
        };
    } else {
        alert('Please allow popups to print orders');
    }
}

// Send notification to customer
function sendNotification(orderId) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Send Notification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="notificationForm">
                        <div class="mb-3">
                            <label class="form-label">Notification Type</label>
                            <select class="form-select" name="type" required>
                                <option value="">Select type...</option>
                                <option value="status_update">Status Update</option>
                                <option value="payment_reminder">Payment Reminder</option>
                                <option value="shipping_info">Shipping Information</option>
                                <option value="custom">Custom Message</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Message</label>
                            <textarea class="form-control" name="message" rows="4" required 
                                placeholder="Enter your message to the customer..."></textarea>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="send_email" checked>
                            <label class="form-check-label">Send via Email</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitNotification(${orderId})">
                        <i class="fas fa-paper-plane me-2"></i>Send Notification
                    </button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    // Clean up modal when hidden
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
    });
}

// Submit notification
function submitNotification(orderId) {
    const form = document.getElementById('notificationForm');
    const formData = new FormData(form);
    formData.append('order_id', orderId);
    
    const submitBtn = event.target;
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    submitBtn.disabled = true;
    
    fetch('ajax/send-notification.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Notification sent successfully!', 'success');
            bootstrap.Modal.getInstance(document.querySelector('.modal')).hide();
        } else {
            showNotification('Failed to send notification: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error sending notification', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Delete order function
function deleteOrder(orderId) {
    if (confirm('Are you sure you want to delete this order? This action cannot be undone.')) {
        fetch('ajax/delete-order.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                order_id: orderId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Order deleted successfully', 'success');
                // Remove the row from table with animation
                const row = document.querySelector(`tr[data-order-id="${orderId}"]`);
                if (row) {
                    row.style.transition = 'all 0.5s';
                    row.style.opacity = '0';
                    row.style.transform = 'translateX(-100%)';
                    setTimeout(() => {
                        row.remove();
                    }, 500);
                }
            } else {
                showNotification('Failed to delete order: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting order', 'error');
        });
    }
}

// Refresh orders function
function refreshOrders() {
    const refreshBtn = document.getElementById('refreshOrders');
    if (refreshBtn) {
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Refreshing...';
        refreshBtn.disabled = true;
        
        // Reload the page after a short delay to show the loading state
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
}

// Enhanced status update with AJAX
function updateOrderStatusAjax(orderId, newStatus, note = '') {
    console.log('updateOrderStatusAjax called with:', { orderId, newStatus, note });

    const requestData = {
        order_id: orderId,
        order_status: newStatus,
        admin_note: note,
        notify_customer: true
    };

    console.log('Sending request data:', requestData);

    return fetch('ajax/update-order-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('Response received:', response);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            // Show success notification
            showNotification(`Order #${orderId} status updated to ${data.status_label}`, 'success');

            // Enhanced localStorage sync for user panel
            try {
                // Store for admin orders page sync
                const statusUpdates = JSON.parse(localStorage.getItem('orderStatusUpdates') || '{}');
                statusUpdates[orderId] = {
                    status: newStatus,
                    timestamp: Date.now()
                };
                localStorage.setItem('orderStatusUpdates', JSON.stringify(statusUpdates));

                // Store for user panel sync (my-orders.php)
                if (data.customer_notification) {
                    const userUpdates = JSON.parse(localStorage.getItem('tewuneed_order_status_sync') || '{}');
                    userUpdates[orderId] = data.customer_notification;
                    localStorage.setItem('tewuneed_order_status_sync', JSON.stringify(userUpdates));

                    console.log(`[AdminSync] Synced status to user panel for order ${orderId}: ${newStatus}`);

                    // Trigger storage event for cross-tab communication
                    window.dispatchEvent(new StorageEvent('storage', {
                        key: 'tewuneed_order_status_sync',
                        newValue: JSON.stringify(userUpdates),
                        oldValue: null
                    }));
                }
            } catch (e) {
                console.log('localStorage not available:', e);
            }
        } else {
            throw new Error(data.message || 'Failed to update status');
        }

        return data;
    });
}

// Initialize admin functions when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('admin-functions.js DOMContentLoaded fired');
    console.log('Looking for .status-action elements...');

    // Add refresh functionality
    const refreshBtn = document.getElementById('refreshOrders');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshOrders);
    }
    
    // Add status change listeners using event delegation
    const statusActionLinks = document.querySelectorAll('.status-action');
    console.log('Found', statusActionLinks.length, 'status action links');

    // Use event delegation on the document body to catch all clicks
    document.body.addEventListener('click', function(e) {
        // Check if the clicked element has the status-action class
        if (e.target.classList.contains('status-action') || e.target.closest('.status-action')) {
            const link = e.target.classList.contains('status-action') ? e.target : e.target.closest('.status-action');
            e.preventDefault();

            // Log the click for debugging
            console.log('Status action clicked via event delegation!');

            const orderId = link.dataset.orderId;
            const newStatus = link.dataset.status;
            const confirmRequired = link.dataset.confirm;

            console.log('Status action clicked:', { orderId, newStatus, confirmRequired });
            console.log('Element attributes:', link.attributes);
            console.log('Dataset:', link.dataset);

            // Check if we have valid data
            if (!orderId || !newStatus) {
                console.error('Missing required data:', { orderId, newStatus });
                alert('Error: Missing order ID or status data');
                return;
            }

            if (confirmRequired && !confirm('Are you sure you want to change this order status?')) {
                return;
            }

            const dropdownButton = link.closest('.dropdown').querySelector('.dropdown-toggle');
            const originalContent = dropdownButton.innerHTML;
            dropdownButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
            dropdownButton.disabled = true;

            console.log('Calling updateOrderStatusAjax...');
            updateOrderStatusAjax(orderId, newStatus)
                .then(data => {
                    console.log('Status update successful:', data);
                    // Update the button with new status
                    if (data.badge_html) {
                        dropdownButton.innerHTML = data.badge_html;
                    } else {
                        dropdownButton.innerHTML = originalContent;
                    }
                    dropdownButton.disabled = false;

                    // Optionally refresh the page after a short delay
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                })
                .catch(error => {
                    console.error('Error updating status:', error);
                    showNotification('Error updating order status: ' + error.message, 'error');
                    dropdownButton.innerHTML = originalContent;
                    dropdownButton.disabled = false;
                });
        }
    });
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
