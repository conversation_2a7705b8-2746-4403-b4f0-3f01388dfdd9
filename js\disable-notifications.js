/**
 * Global notification disabler
 * This script disables annoying popup notifications across the site
 */

// Disable all popup notifications globally
window.DISABLE_POPUP_NOTIFICATIONS = true;

// Override common notification functions
(function() {
    'use strict';
    
    // Store original functions
    const originalAlert = window.alert;
    const originalConfirm = window.confirm;
    
    // Override showNotification if it exists
    if (window.showNotification) {
        window.showNotification = function() {
            // Silently ignore notification calls
            console.log('Notification blocked by disable-notifications.js');
        };
    }
    
    // Override real-time sync notifications
    document.addEventListener('DOMContentLoaded', function() {
        // Disable real-time sync notifications
        if (window.realTimeSync) {
            const originalShowNotification = window.realTimeSync.showNotification;
            window.realTimeSync.showNotification = function() {
                // Silently ignore
                console.log('Real-time notification blocked');
            };
            
            const originalShowUserNotification = window.realTimeSync.showUserNotification;
            window.realTimeSync.showUserNotification = function() {
                // Silently ignore
                console.log('User notification blocked');
            };
        }
        
        // Disable active notification manager popups
        if (window.activeNotificationManager) {
            const originalShowPopup = window.activeNotificationManager.showNotificationPopup;
            window.activeNotificationManager.showNotificationPopup = function() {
                // Silently ignore
                console.log('Active notification popup blocked');
            };
        }
        
        // Remove existing notification containers
        const notificationContainers = document.querySelectorAll('#notificationContainer, .notification-popup');
        notificationContainers.forEach(container => {
            if (container) {
                container.style.display = 'none';
            }
        });
        
        // Prevent new notification containers from showing
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && (
                            node.classList.contains('notification-popup') ||
                            node.classList.contains('notification-toast') ||
                            node.classList.contains('alert') ||
                            node.id === 'notificationContainer'
                        )) {
                            node.style.display = 'none';
                            console.log('Blocked notification element:', node);
                        }
                        
                        // Also check child elements
                        const notificationElements = node.querySelectorAll && node.querySelectorAll('.notification-popup, .notification-toast, .alert');
                        if (notificationElements) {
                            notificationElements.forEach(el => {
                                el.style.display = 'none';
                                console.log('Blocked child notification element:', el);
                            });
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Hide notification badges that might be annoying
        const notificationBadges = document.querySelectorAll('.badge, .notification-badge, #notificationBadge');
        notificationBadges.forEach(badge => {
            if (badge && badge.textContent && parseInt(badge.textContent) > 0) {
                // Don't completely hide, but make less prominent
                badge.style.opacity = '0.5';
                badge.style.animation = 'none';
            }
        });
    });
    
    // Override setTimeout and setInterval for notification-related functions
    const originalSetTimeout = window.setTimeout;
    const originalSetInterval = window.setInterval;
    
    window.setTimeout = function(callback, delay, ...args) {
        // Check if this might be a notification timeout
        const callbackStr = callback.toString();
        if (callbackStr.includes('notification') || 
            callbackStr.includes('popup') || 
            callbackStr.includes('toast') ||
            callbackStr.includes('alert')) {
            console.log('Blocked notification timeout');
            return null;
        }
        return originalSetTimeout.call(this, callback, delay, ...args);
    };
    
    window.setInterval = function(callback, delay, ...args) {
        // Check if this might be a notification interval
        const callbackStr = callback.toString();
        if (callbackStr.includes('notification') || 
            callbackStr.includes('popup') || 
            callbackStr.includes('checkForNew')) {
            console.log('Blocked notification interval');
            return null;
        }
        return originalSetInterval.call(this, callback, delay, ...args);
    };
    
    // Disable notification sounds
    const AudioContext = window.AudioContext || window.webkitAudioContext;
    if (AudioContext) {
        const originalCreateOscillator = AudioContext.prototype.createOscillator;
        AudioContext.prototype.createOscillator = function() {
            console.log('Blocked notification sound');
            return {
                connect: function() {},
                start: function() {},
                stop: function() {},
                frequency: { setValueAtTime: function() {} }
            };
        };
    }
    
    console.log('Notification disabler loaded - popup notifications are now blocked');
})();

// CSS to hide notification elements
const style = document.createElement('style');
style.textContent = `
    .notification-popup,
    .notification-toast,
    .toast,
    .alert-dismissible:not(.alert-permanent),
    #notificationContainer .notification-popup {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }
    
    /* Reduce prominence of notification badges */
    .badge.bg-danger,
    .notification-badge,
    #notificationBadge {
        animation: none !important;
        opacity: 0.6 !important;
    }
    
    /* Hide notification animations */
    @keyframes pulse,
    @keyframes slideInRight,
    @keyframes newNotificationGlow {
        from, to { 
            transform: none !important;
            opacity: 1 !important;
        }
    }
`;
document.head.appendChild(style);
