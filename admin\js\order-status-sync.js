/**
 * Enhanced Order Status Synchronization System
 * Handles real-time synchronization of order status changes between admin and customer views
 * Version 2.0 - Enhanced with better error handling and performance
 */

class OrderStatusSync {
    constructor() {
        this.storageKey = 'tewuneed_order_status_sync';
        this.sessionKey = 'tewuneed_session_sync';
        this.debug = true;
        this.maxCacheAge = 30 * 60 * 1000; // 30 minutes
        this.syncInterval = null;
        this.isInitialized = false;

        this.init();
    }

    init() {
        if (this.isInitialized) return;

        try {
            // Apply any pending status updates when page loads
            this.applyPendingUpdates();

            // Set up event listeners for status changes
            this.setupEventListeners();

            // Clean up old entries periodically
            this.cleanupOldEntries();

            // Start periodic sync check
            this.startPeriodicSync();

            this.isInitialized = true;

            if (this.debug) {
                console.log('[OrderStatusSync] Initialized successfully');
            }
        } catch (error) {
            console.error('[OrderStatusSync] Initialization failed:', error);
        }
    }

    /**
     * Start periodic synchronization check
     */
    startPeriodicSync() {
        // Check for updates every 10 seconds
        this.syncInterval = setInterval(() => {
            this.checkForUpdates();
        }, 10000);

        if (this.debug) {
            console.log('[OrderStatusSync] Periodic sync started');
        }
    }

    /**
     * Stop periodic synchronization
     */
    stopPeriodicSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;

            if (this.debug) {
                console.log('[OrderStatusSync] Periodic sync stopped');
            }
        }
    }

    /**
     * Check for status updates from server
     */
    async checkForUpdates() {
        try {
            const response = await fetch('/tewuneed2/ajax/check-order-updates.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    last_check: this.getLastCheckTime()
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success && data.updates && data.updates.length > 0) {
                this.processServerUpdates(data.updates);
                this.updateLastCheckTime();
            }
        } catch (error) {
            if (this.debug) {
                console.error('[OrderStatusSync] Error checking for updates:', error);
            }
        }
    }

    /**
     * Process updates received from server
     */
    processServerUpdates(updates) {
        updates.forEach(update => {
            this.saveOrderStatus(update.order_id, update.new_status, {
                note: update.admin_note,
                timestamp: new Date(update.created_at).getTime(),
                source: 'server'
            });

            this.updateOrderStatusUI(update.order_id, update.new_status);

            if (this.debug) {
                console.log(`[OrderStatusSync] Processed server update for order #${update.order_id}: ${update.new_status}`);
            }
        });
    }

    /**
     * Get last check time from storage
     */
    getLastCheckTime() {
        return localStorage.getItem('tewuneed_last_check') || new Date().getTime();
    }

    /**
     * Update last check time
     */
    updateLastCheckTime() {
        localStorage.setItem('tewuneed_last_check', new Date().getTime());
    }

    /**
     * Save order status change to localStorage for cross-page sync
     */
    saveStatusChange(orderId, status, note = '') {
        try {
            const updates = this.getStoredUpdates();

            updates[orderId] = {
                status: status,
                note: note,
                timestamp: Date.now(),
                synced: false
            };

            localStorage.setItem(this.storageKey, JSON.stringify(updates));

            if (this.debug) {
                console.log(`[OrderStatusSync] Saved status change for order #${orderId}: ${status}`);
            }

            return true;
        } catch (error) {
            console.error('[OrderStatusSync] Error saving status change:', error);
            return false;
        }
    }

    /**
     * Get stored status updates from localStorage
     */
    getStoredUpdates() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : {};
        } catch (error) {
            console.error('[OrderStatusSync] Error reading stored updates:', error);
            return {};
        }
    }

    /**
     * Apply pending status updates to current page
     */
    applyPendingUpdates() {
        const updates = this.getStoredUpdates();
        const orderIds = Object.keys(updates);

        if (orderIds.length === 0) {
            return;
        }

        if (this.debug) {
            console.log(`[OrderStatusSync] Applying ${orderIds.length} pending updates`);
        }

        orderIds.forEach(orderId => {
            const update = updates[orderId];

            // Skip if too old (older than 1 hour)
            if (Date.now() - update.timestamp > 3600000) {
                return;
            }

            // Apply update to UI
            this.updateOrderStatusInUI(orderId, update.status, update.note);

            // Mark as synced
            update.synced = true;
        });

        // Save updated data
        localStorage.setItem(this.storageKey, JSON.stringify(updates));
    }

    /**
     * Update order status in the UI
     */
    updateOrderStatusInUI(orderId, status, note = '') {
        // Find order rows by various methods
        const selectors = [
            `tr[data-order-id="${orderId}"]`,
            `tr:has(input[value="${orderId}"])`,
            `tr:has(a[href*="id=${orderId}"])`
        ];

        let orderRow = null;

        for (const selector of selectors) {
            try {
                orderRow = document.querySelector(selector);
                if (orderRow) break;
            } catch (e) {
                // Selector might not be supported in all browsers
            }
        }

        // Fallback: search by text content
        if (!orderRow) {
            const rows = document.querySelectorAll('tbody tr');
            for (const row of rows) {
                if (row.textContent.includes(orderId) || row.textContent.includes(`#${orderId}`)) {
                    orderRow = row;
                    break;
                }
            }
        }

        if (!orderRow) {
            if (this.debug) {
                console.log(`[OrderStatusSync] Order row not found for #${orderId}`);
            }
            return false;
        }

        // Update status badge/dropdown
        const statusElement = orderRow.querySelector('.dropdown-toggle, .badge, .status-badge');
        if (statusElement) {
            const badgeHtml = this.generateStatusBadgeHtml(status);

            // Animate the change
            statusElement.style.transition = 'opacity 0.3s';
            statusElement.style.opacity = '0.5';

            setTimeout(() => {
                statusElement.innerHTML = badgeHtml;
                statusElement.style.opacity = '1';

                // Highlight the row briefly
                orderRow.style.transition = 'background-color 0.5s';
                orderRow.style.backgroundColor = 'rgba(255, 243, 205, 0.7)';

                setTimeout(() => {
                    orderRow.style.backgroundColor = '';
                }, 2000);
            }, 300);
        }

        // Show notification
        this.showNotification(`Order #${orderId} status updated to ${this.getStatusLabel(status)}`, 'info');

        return true;
    }

    /**
     * Generate status badge HTML
     */
    generateStatusBadgeHtml(status) {
        const statusMap = {
            'dibuat': { class: 'warning', icon: 'fa-clock', label: 'Pending' },
            'pending': { class: 'warning', icon: 'fa-clock', label: 'Pending' },
            'diproses': { class: 'info', icon: 'fa-spinner fa-spin', label: 'Processing' },
            'processing': { class: 'info', icon: 'fa-spinner fa-spin', label: 'Processing' },
            'dikirim': { class: 'primary', icon: 'fa-truck', label: 'Shipped' },
            'shipped': { class: 'primary', icon: 'fa-truck', label: 'Shipped' },
            'terkirim': { class: 'success', icon: 'fa-check-circle', label: 'Delivered' },
            'delivered': { class: 'success', icon: 'fa-check-circle', label: 'Delivered' },
            'dibatalkan': { class: 'danger', icon: 'fa-times-circle', label: 'Cancelled' },
            'cancelled': { class: 'danger', icon: 'fa-times-circle', label: 'Cancelled' }
        };

        const config = statusMap[status.toLowerCase()] || { class: 'secondary', icon: 'fa-circle', label: status };

        return `<span class="badge bg-${config.class}"><i class="fas ${config.icon} me-1"></i> ${config.label}</span>`;
    }

    /**
     * Get user-friendly status label
     */
    getStatusLabel(status) {
        const labels = {
            'dibuat': 'Pending',
            'pending': 'Pending',
            'diproses': 'Processing',
            'processing': 'Processing',
            'dikirim': 'Shipped',
            'shipped': 'Shipped',
            'terkirim': 'Delivered',
            'delivered': 'Delivered',
            'dibatalkan': 'Cancelled',
            'cancelled': 'Cancelled'
        };

        return labels[status.toLowerCase()] || status;
    }

    /**
     * Set up event listeners for status changes
     */
    setupEventListeners() {
        // Listen for status form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('status-form') || e.target.querySelector('input[name="order_status"]')) {
                const formData = new FormData(e.target);
                const orderId = formData.get('order_id');
                const status = formData.get('order_status');
                const note = formData.get('admin_note') || formData.get('note') || '';

                if (orderId && status) {
                    this.saveStatusChange(orderId, status, note);
                }
            }
        });

        // Listen for AJAX status updates
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            return originalFetch(...args).then(response => {
                // Check if this is a status update request
                if (args[0].includes('order') && args[1] && args[1].method === 'POST') {
                    try {
                        const body = args[1].body;
                        if (body instanceof FormData) {
                            const orderId = body.get('order_id');
                            const status = body.get('order_status');

                            if (orderId && status) {
                                this.saveStatusChange(orderId, status);
                            }
                        }
                    } catch (e) {
                        // Ignore errors in parsing
                    }
                }
                return response;
            });
        };
    }

    /**
     * Clean up old entries from localStorage
     */
    cleanupOldEntries() {
        try {
            const updates = this.getStoredUpdates();
            const now = Date.now();
            const maxAge = 24 * 60 * 60 * 1000; // 24 hours

            let cleaned = false;

            Object.keys(updates).forEach(orderId => {
                if (now - updates[orderId].timestamp > maxAge) {
                    delete updates[orderId];
                    cleaned = true;
                }
            });

            if (cleaned) {
                localStorage.setItem(this.storageKey, JSON.stringify(updates));
                if (this.debug) {
                    console.log('[OrderStatusSync] Cleaned up old entries');
                }
            }
        } catch (error) {
            console.error('[OrderStatusSync] Error cleaning up old entries:', error);
        }
    }

    /**
     * Show notification to user
     */
    showNotification(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        // Add to toast container or create one
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }

        container.appendChild(toast);

        // Show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Remove after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.orderStatusSync = new OrderStatusSync();

        // Add compatibility function for legacy calls
        window.orderStatusSyncLiveOrderStatus = function(orderId, status) {
            if (window.orderStatusSync) {
                return window.orderStatusSync.saveStatusChange(orderId, status);
            }
            return false;
        };

        console.log('[OrderStatusSync] Initialized successfully with compatibility layer');
    } catch (error) {
        console.error('[OrderStatusSync] Failed to initialize:', error);
    }
});
