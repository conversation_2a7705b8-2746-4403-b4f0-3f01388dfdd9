<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/StockReservation.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login to remove items from cart']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    $cart_item_id = $_POST['cart_item_id'] ?? 0;
    
    // Validate input
    if (!$cart_item_id) {
        echo json_encode(['success' => false, 'message' => 'Cart item ID is required']);
        exit;
    }
    
    // Check if cart item belongs to user and get product info
    $stmt = $conn->prepare("
        SELECT ci.*, p.NAME as product_name
        FROM cart_items ci
        JOIN carts c ON ci.cart_id = c.cart_id
        JOIN products p ON ci.product_id = p.product_id
        WHERE ci.cart_item_id = ? AND c.user_id = ?
    ");
    $stmt->execute([$cart_item_id, $user_id]);
    $cart_item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$cart_item) {
        echo json_encode(['success' => false, 'message' => 'Cart item not found']);
        exit;
    }
    
    // Initialize stock reservation system
    $stockReservation = new StockReservation($conn);

    // Cancel stock reservation for this product
    $cancelResult = $stockReservation->cancelReservation($cart_item['product_id'], $user_id);
    if (!$cancelResult['success']) {
        error_log("Warning: Could not cancel reservation for product {$cart_item['product_id']}: " . $cancelResult['message']);
        // Continue anyway, as the cart item should still be removed
    }

    // Remove cart item
    $stmt = $conn->prepare("DELETE FROM cart_items WHERE cart_item_id = ?");
    $result = $stmt->execute([$cart_item_id]);
    
    if ($result) {
        // Get updated cart totals
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total_items,
                SUM(ci.quantity) as total_quantity,
                SUM(ci.quantity * p.price) as total_amount
            FROM cart_items ci
            JOIN carts c ON ci.cart_id = c.cart_id
            JOIN products p ON ci.product_id = p.product_id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$user_id]);
        $totals = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Log activity
        $activity_stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, description, created_at) 
            VALUES (?, 'cart_remove', ?, NOW())
        ");
        $activity_stmt->execute([
            $user_id,
            "Removed from cart: {$cart_item['product_name']}"
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Item removed from cart successfully',
            'data' => [
                'removed_item_id' => $cart_item_id,
                'cart_total_items' => $totals['total_items'] ?? 0,
                'cart_total_quantity' => $totals['total_quantity'] ?? 0,
                'cart_total_amount' => $totals['total_amount'] ?? 0,
                'cart_is_empty' => ($totals['total_items'] ?? 0) == 0
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to remove item from cart']);
    }
    
} catch (Exception $e) {
    error_log("Remove from cart error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while removing item from cart']);
}
?>
