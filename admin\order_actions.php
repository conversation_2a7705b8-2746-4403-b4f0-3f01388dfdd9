<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: admin_login.php');
    exit;
}

// Database connection
$conn = getConnection();

// Process order status update
if (isset($_GET['action']) && $_GET['action'] === 'update_status') {
    try {
        $order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;
        $order_status = isset($_POST['order_status']) ? $_POST['order_status'] : '';
        $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';
        
        if ($order_id <= 0) {
            throw new Exception("ID pesanan tidak valid");
        }
        
        // Validate order status
        $valid_statuses = ['dibuat', 'diproses', 'dikirim', 'terkirim', 'dibatalkan', 'refunded'];
        if (!in_array($order_status, $valid_statuses)) {
            throw new Exception("Status pesanan tidak valid");
        }
        
        // Get current order data
        $stmt = $conn->prepare("SELECT * FROM orders WHERE order_id = ?");
        $stmt->execute([$order_id]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$order) {
            throw new Exception("Pesanan tidak ditemukan");
        }
        
        $previous_status = $order['order_status'];
        
        // Begin transaction
        $conn->beginTransaction();
        
        // Update order status
        $stmt = $conn->prepare("UPDATE orders SET order_status = ?, updated_at = NOW() WHERE order_id = ?");
        $stmt->execute([$order_status, $order_id]);
        
        // Log the status change
        $stmt = $conn->prepare("
            INSERT INTO order_status_history (
                order_id, previous_status, new_status, 
                notes, admin_id, created_at
            ) VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $order_id,
            $previous_status,
            $order_status,
            $notes,
            $_SESSION['user_id'] ?? null
        ]);
        
        // Handle inventory if status is cancelled (return stock) or delivered (finalize order)
        if ($order_status === 'dibatalkan' && $previous_status !== 'dibatalkan') {
            // Return stock to inventory
            $stmt = $conn->prepare("
                SELECT product_id, quantity FROM order_items WHERE order_id = ?
            ");
            $stmt->execute([$order_id]);
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($items as $item) {
                // Get current stock
                $stmt = $conn->prepare("SELECT stock FROM products WHERE product_id = ?");
                $stmt->execute([$item['product_id']]);
                $current_stock = $stmt->fetchColumn();
                
                // Update stock
                $new_stock = $current_stock + $item['quantity'];
                $stmt = $conn->prepare("UPDATE products SET stock = ? WHERE product_id = ?");
                $stmt->execute([$new_stock, $item['product_id']]);
                
                // Log inventory change
                $stmt = $conn->prepare("
                    INSERT INTO inventory_logs (
                        product_id, admin_id, old_stock, new_stock, 
                        adjustment, notes, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $item['product_id'],
                    $_SESSION['user_id'] ?? null,
                    $current_stock,
                    $new_stock,
                    $item['quantity'],
                    "Stock returned from cancelled order #" . $order_id
                ]);
            }
        }
        
        // If order was cancelled but now it's being processed again, deduct stock again
        if ($previous_status === 'dibatalkan' && $order_status !== 'dibatalkan') {
            // Deduct stock from inventory
            $stmt = $conn->prepare("
                SELECT product_id, quantity FROM order_items WHERE order_id = ?
            ");
            $stmt->execute([$order_id]);
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($items as $item) {
                // Get current stock
                $stmt = $conn->prepare("SELECT stock FROM products WHERE product_id = ?");
                $stmt->execute([$item['product_id']]);
                $current_stock = $stmt->fetchColumn();
                
                // Update stock
                $new_stock = max(0, $current_stock - $item['quantity']);
                $stmt = $conn->prepare("UPDATE products SET stock = ? WHERE product_id = ?");
                $stmt->execute([$new_stock, $item['product_id']]);
                
                // Log inventory change
                $stmt = $conn->prepare("
                    INSERT INTO inventory_logs (
                        product_id, admin_id, old_stock, new_stock, 
                        adjustment, notes, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $item['product_id'],
                    $_SESSION['user_id'] ?? null,
                    $current_stock,
                    $new_stock,
                    -$item['quantity'],
                    "Stock deducted for reactivated order #" . $order_id
                ]);
            }
        }
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['alert_message'] = "Status pesanan berhasil diperbarui menjadi \"" . ucfirst($order_status) . "\"";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to orders page or detail page
    if (isset($_GET['return']) && $_GET['return'] === 'detail') {
        header('Location: order-detail.php?id=' . $order_id);
    } else {
        header('Location: order_management.php');
    }
    exit;
}

// Process payment status update
if (isset($_GET['action']) && $_GET['action'] === 'update_payment') {
    try {
        $order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;
        $payment_status = isset($_POST['payment_status']) ? $_POST['payment_status'] : '';
        $payment_notes = isset($_POST['payment_notes']) ? trim($_POST['payment_notes']) : '';
        
        if ($order_id <= 0) {
            throw new Exception("ID pesanan tidak valid");
        }
        
        // Validate payment status
        $valid_statuses = ['pending', 'paid', 'failed', 'refunded'];
        if (!in_array($payment_status, $valid_statuses)) {
            throw new Exception("Status pembayaran tidak valid");
        }
        
        // Get current order data
        $stmt = $conn->prepare("SELECT * FROM orders WHERE order_id = ?");
        $stmt->execute([$order_id]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$order) {
            throw new Exception("Pesanan tidak ditemukan");
        }
        
        $previous_status = $order['payment_status'] ?? 'pending';
        
        // Begin transaction
        $conn->beginTransaction();
        
        // Update payment status
        $stmt = $conn->prepare("UPDATE orders SET payment_status = ?, payment_notes = ?, updated_at = NOW() WHERE order_id = ?");
        $stmt->execute([$payment_status, $payment_notes, $order_id]);
        
        // Log the payment status change
        $stmt = $conn->prepare("
            INSERT INTO payment_status_history (
                order_id, previous_status, new_status, 
                notes, admin_id, created_at
            ) VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $order_id,
            $previous_status,
            $payment_status,
            $payment_notes,
            $_SESSION['user_id'] ?? null
        ]);
        
        // If payment was previously pending and now paid, update order status to processing if still pending
        if ($previous_status === 'pending' && $payment_status === 'paid' && $order['order_status'] === 'pending') {
            $stmt = $conn->prepare("UPDATE orders SET order_status = 'processing', updated_at = NOW() WHERE order_id = ?");
            $stmt->execute([$order_id]);
            
            // Log the order status change
            $stmt = $conn->prepare("
                INSERT INTO order_status_history (
                    order_id, previous_status, new_status, 
                    notes, admin_id, created_at
                ) VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $order_id,
                'pending',
                'processing',
                'Automatically updated to processing after payment confirmation',
                $_SESSION['user_id'] ?? null
            ]);
        }
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['alert_message'] = "Status pembayaran berhasil diperbarui menjadi \"" . ucfirst($payment_status) . "\"";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to orders page or detail page
    if (isset($_GET['return']) && $_GET['return'] === 'detail') {
        header('Location: order-detail.php?id=' . $order_id);
    } else {
        header('Location: order_management.php');
    }
    exit;
}

// Process tracking number addition
if (isset($_GET['action']) && $_GET['action'] === 'add_tracking') {
    try {
        $order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;
        $tracking_number = isset($_POST['tracking_number']) ? trim($_POST['tracking_number']) : '';
        $shipping_carrier = isset($_POST['shipping_carrier']) ? $_POST['shipping_carrier'] : '';
        $tracking_url = isset($_POST['tracking_url']) ? trim($_POST['tracking_url']) : '';
        $update_status = isset($_POST['update_status']) && $_POST['update_status'] == '1';
        $notify_customer = isset($_POST['notify_customer']) && $_POST['notify_customer'] == '1';
        
        if ($order_id <= 0) {
            throw new Exception("ID pesanan tidak valid");
        }
        
        if (empty($tracking_number)) {
            throw new Exception("Nomor pelacakan tidak boleh kosong");
        }
        
        // Get current order data
        $stmt = $conn->prepare("SELECT * FROM orders WHERE order_id = ?");
        $stmt->execute([$order_id]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$order) {
            throw new Exception("Pesanan tidak ditemukan");
        }
        
        // Begin transaction
        $conn->beginTransaction();
        
        // Update tracking information
        $stmt = $conn->prepare("
            UPDATE orders 
            SET tracking_number = ?, 
                shipping_carrier = ?, 
                tracking_url = ?, 
                updated_at = NOW() 
            WHERE order_id = ?
        ");
        $stmt->execute([$tracking_number, $shipping_carrier, $tracking_url, $order_id]);
        
        // Update order status to shipped if requested
        if ($update_status && $order['order_status'] !== 'shipped') {
            $previous_status = $order['order_status'];
            
            $stmt = $conn->prepare("UPDATE orders SET order_status = 'shipped', updated_at = NOW() WHERE order_id = ?");
            $stmt->execute([$order_id]);
            
            // Log the status change
            $stmt = $conn->prepare("
                INSERT INTO order_status_history (
                    order_id, previous_status, new_status, 
                    notes, admin_id, created_at
                ) VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $order_id,
                $previous_status,
                'shipped',
                'Status updated to shipped when tracking number was added',
                $_SESSION['user_id'] ?? null
            ]);
        }
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['alert_message'] = "Informasi pelacakan berhasil diperbarui";
        $_SESSION['alert_type'] = 'success';
        
        // Send notification email to customer if requested
        if ($notify_customer) {
            $customer_email = $order['shipping_email'] ?? $order['user_email'] ?? null;
            $customer_name = $order['shipping_name'] ?? $order['user_name'] ?? 'Customer';
            
            if ($customer_email) {
                // Prepare email content
                $subject = "Your order #" . ($order['order_number'] ?? $order_id) . " has been shipped";
                $message = "Dear " . htmlspecialchars($customer_name) . ",\n\n";
                $message .= "Great news! Your order #" . ($order['order_number'] ?? $order_id) . " has been shipped.\n\n";
                $message .= "Tracking Number: " . htmlspecialchars($tracking_number) . "\n";
                $message .= "Carrier: " . ucfirst(htmlspecialchars($shipping_carrier)) . "\n";
                
                if (!empty($tracking_url)) {
                    $message .= "You can track your package at: " . htmlspecialchars($tracking_url) . "\n\n";
                }
                
                $message .= "Thank you for shopping with us!\n\n";
                $message .= "Best regards,\nThe Team";
                
                // Send email (simple mail function for demonstration - consider using a proper email library)
                $headers = "From: <EMAIL>\r\n";
                $headers .= "Reply-To: <EMAIL>\r\n";
                
                if (@mail($customer_email, $subject, $message, $headers)) {
                    $_SESSION['alert_message'] .= " dan email notifikasi telah dikirim";
                } else {
                    $_SESSION['alert_message'] .= " tetapi email notifikasi gagal dikirim";
                }
            }
        }
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to orders page or detail page
    if (isset($_GET['return']) && $_GET['return'] === 'detail') {
        header('Location: order-detail.php?id=' . $order_id);
    } else {
        header('Location: order_management.php');
    }
    exit;
}

// Process send email to customer
if (isset($_GET['action']) && $_GET['action'] === 'send_email') {
    try {
        $order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;
        $email_to = isset($_POST['email_to']) ? trim($_POST['email_to']) : '';
        $email_subject = isset($_POST['email_subject']) ? trim($_POST['email_subject']) : '';
        $email_message = isset($_POST['email_message']) ? trim($_POST['email_message']) : '';
        
        if ($order_id <= 0) {
            throw new Exception("ID pesanan tidak valid");
        }
        
        if (empty($email_to) || empty($email_subject) || empty($email_message)) {
            throw new Exception("Semua field email harus diisi");
        }
        
        if (!filter_var($email_to, FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Format email tidak valid");
        }
        
        // Send email (simple mail function for demonstration - consider using a proper email library)
        $headers = "From: <EMAIL>\r\n";
        $headers .= "Reply-To: <EMAIL>\r\n";
        
        if (@mail($email_to, $email_subject, $email_message, $headers)) {
            // Log the email
            $stmt = $conn->prepare("
                INSERT INTO communication_logs (
                    order_id, type, recipient, subject, 
                    message, admin_id, created_at
                ) VALUES (?, 'email', ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $order_id,
                $email_to,
                $email_subject,
                $email_message,
                $_SESSION['user_id'] ?? null
            ]);
            
            $_SESSION['alert_message'] = "Email berhasil dikirim ke " . $email_to;
            $_SESSION['alert_type'] = 'success';
        } else {
            throw new Exception("Gagal mengirim email");
        }
        
    } catch (Exception $e) {
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to orders page or detail page
    if (isset($_GET['return']) && $_GET['return'] === 'detail') {
        header('Location: order-detail.php?id=' . $order_id);
    } else {
        header('Location: order_management.php');
    }
    exit;
}

// Process order deletion (soft delete)
if (isset($_GET['action']) && $_GET['action'] === 'delete') {
    try {
        $order_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if ($order_id <= 0) {
            throw new Exception("ID pesanan tidak valid");
        }
        
        // Get order data
        $stmt = $conn->prepare("SELECT * FROM orders WHERE order_id = ?");
        $stmt->execute([$order_id]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$order) {
            throw new Exception("Pesanan tidak ditemukan");
        }
        
        // Soft delete order (update is_deleted flag)
        $stmt = $conn->prepare("UPDATE orders SET is_deleted = 1, updated_at = NOW() WHERE order_id = ?");
        $stmt->execute([$order_id]);
        
        $_SESSION['alert_message'] = "Pesanan berhasil dihapus";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to orders page
    header('Location: order_management.php');
    exit;
}

// Process order note update
if (isset($_GET['action']) && $_GET['action'] === 'update_note') {
    try {
        $order_id = isset($_POST['order_id']) ? (int)$_POST['order_id'] : 0;
        $admin_note = isset($_POST['admin_note']) ? trim($_POST['admin_note']) : '';
        
        if ($order_id <= 0) {
            throw new Exception("ID pesanan tidak valid");
        }
        
        // Update order note
        $stmt = $conn->prepare("UPDATE orders SET admin_note = ?, updated_at = NOW() WHERE order_id = ?");
        $stmt->execute([$admin_note, $order_id]);
        
        $_SESSION['alert_message'] = "Catatan admin berhasil diperbarui";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to order detail page
    header('Location: order-detail.php?id=' . $order_id);
    exit;
}

// Process bulk actions
if (isset($_POST['bulk_action']) && !empty($_POST['selected_orders'])) {
    try {
        $bulk_action = $_POST['bulk_action'];
        $selected_orders = $_POST['selected_orders'];
        
        if (!is_array($selected_orders) || empty($selected_orders)) {
            throw new Exception("Tidak ada pesanan yang dipilih");
        }
        
        // Validate order IDs
        $order_ids = array_map('intval', $selected_orders);
        $order_ids = array_filter($order_ids, function($id) { return $id > 0; });
        
        if (empty($order_ids)) {
            throw new Exception("Tidak ada pesanan valid yang dipilih");
        }
        
        // Begin transaction
        $conn->beginTransaction();
        
        $count = count($order_ids);
        $placeholders = implode(',', array_fill(0, $count, '?'));
        
        switch ($bulk_action) {
            case 'update_status':
                if (!isset($_POST['bulk_status']) || empty($_POST['bulk_status'])) {
                    throw new Exception("Status tidak dipilih");
                }
                
                $new_status = $_POST['bulk_status'];
                $valid_statuses = ['dibuat', 'diproses', 'dikirim', 'terkirim', 'dibatalkan', 'refunded'];
                if (!in_array($new_status, $valid_statuses)) {
                    throw new Exception("Status pesanan tidak valid");
                }
                
                // Get current statuses
                $stmt = $conn->prepare("
                    SELECT order_id, order_status 
                    FROM orders 
                    WHERE order_id IN ($placeholders)
                ");
                $stmt->execute($order_ids);
                $current_statuses = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
                
                // Update all selected orders
                $stmt = $conn->prepare("
                    UPDATE orders 
                    SET order_status = ?, updated_at = NOW() 
                    WHERE order_id IN ($placeholders)
                ");
                $params = array_merge([$new_status], $order_ids);
                $stmt->execute($params);
                
                // Log status changes
                foreach ($order_ids as $order_id) {
                    if (isset($current_statuses[$order_id])) {
                        $previous_status = $current_statuses[$order_id];
                        
                        if ($previous_status !== $new_status) {
                            $stmt = $conn->prepare("
                                INSERT INTO order_status_history (
                                    order_id, previous_status, new_status, 
                                    notes, admin_id, created_at
                                ) VALUES (?, ?, ?, ?, ?, NOW())
                            ");
                            
                            $stmt->execute([
                                $order_id,
                                $previous_status,
                                $new_status,
                                "Bulk status update",
                                $_SESSION['user_id'] ?? null
                            ]);
                        }
                    }
                }
                
                $_SESSION['alert_message'] = "Berhasil memperbarui status $count pesanan menjadi \"" . ucfirst($new_status) . "\"";
                break;
                
            case 'delete':
                // Soft delete orders
                $stmt = $conn->prepare("
                    UPDATE orders 
                    SET is_deleted = 1, updated_at = NOW() 
                    WHERE order_id IN ($placeholders)
                ");
                $stmt->execute($order_ids);
                
                $_SESSION['alert_message'] = "Berhasil menghapus $count pesanan";
                break;
                
            case 'export':
                // Not implemented here - redirects to export page with selected IDs
                $conn->commit(); // Commit any changes
                
                $ids_string = implode(',', $order_ids);
                header("Location: order-export.php?ids=" . urlencode($ids_string));
                exit;
                
            default:
                throw new Exception("Aksi tidak dikenal");
        }
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to orders page
    header('Location: order_management.php');
    exit;
}

// Redirect to orders page if no action was taken
header('Location: order_management.php');
exit;
