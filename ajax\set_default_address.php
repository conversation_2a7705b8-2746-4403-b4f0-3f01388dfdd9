<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    $address_id = $_POST['address_id'] ?? 0;
    
    if (!$address_id) {
        echo json_encode(['success' => false, 'message' => 'Address ID is required']);
        exit;
    }
    
    // Check if address belongs to user
    $stmt = $conn->prepare("SELECT * FROM user_addresses WHERE address_id = ? AND user_id = ?");
    $stmt->execute([$address_id, $user_id]);
    $address = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$address) {
        echo json_encode(['success' => false, 'message' => 'Address not found']);
        exit;
    }
    
    // Start transaction
    $conn->beginTransaction();
    
    // Remove default from all addresses
    $stmt = $conn->prepare("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?");
    $stmt->execute([$user_id]);
    
    // Set new default address
    $stmt = $conn->prepare("UPDATE user_addresses SET is_default = 1 WHERE address_id = ? AND user_id = ?");
    $result = $stmt->execute([$address_id, $user_id]);
    
    if ($result) {
        $conn->commit();
        
        // Log activity
        $activity_stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, description, created_at) 
            VALUES (?, 'address_default', ?, NOW())
        ");
        $activity_stmt->execute([
            $user_id,
            "Set default address: {$address['label']}"
        ]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Default address updated successfully'
        ]);
    } else {
        $conn->rollBack();
        echo json_encode(['success' => false, 'message' => 'Failed to update default address']);
    }
    
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    error_log("Set default address error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while updating default address']);
}
?>
