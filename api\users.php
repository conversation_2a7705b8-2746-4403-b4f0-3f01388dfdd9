<?php
require_once '../includes/autoload.php';

header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

// Check authentication for non-public endpoints
if ($method !== 'POST' && !isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Handle different request methods
switch ($method) {
    case 'GET':
        try {
            $pdo = getConnection();
            
            if (isset($_GET['id'])) {
                // Get single user (admin or self)
                if (!is_admin() && $_GET['id'] != $_SESSION['user_id']) {
                    http_response_code(403);
                    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
                    break;
                }

                $stmt = $pdo->prepare("
                    SELECT id, name, email, role, status, created_at, last_login
                    FROM users
                    WHERE id = ?
                ");
                $stmt->execute([$_GET['id']]);
                $user = $stmt->fetch();

                if ($user) {
                    echo json_encode(['success' => true, 'data' => $user]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'User not found']);
                }
            } else {
                // Get all users (admin only)
                if (!is_admin()) {
                    http_response_code(403);
                    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
                    break;
                }

                $page = $_GET['page'] ?? 1;
                $limit = $_GET['limit'] ?? 10;

                $stmt = $pdo->prepare("
                    SELECT id, name, email, role, status, created_at, last_login
                    FROM users
                    ORDER BY created_at DESC
                    LIMIT ? OFFSET ?
                ");
                $stmt->execute([$limit, ($page - 1) * $limit]);
                $users = $stmt->fetchAll();

                // Get total count
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM users");
                $stmt->execute();
                $total = $stmt->fetchColumn();

                echo json_encode([
                    'success' => true,
                    'data' => [
                        'users' => $users,
                        'pagination' => [
                            'total' => $total,
                            'per_page' => $limit,
                            'current_page' => $page,
                            'last_page' => ceil($total / $limit)
                        ]
                    ]
                ]);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Database error']);
        }
        break;

    case 'POST':
        if (!isset($data['action'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Action is required']);
            break;
        }

        switch ($data['action']) {
            case 'register':
                if (!isset($data['name']) || !isset($data['email']) || !isset($data['password'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Name, email and password are required']);
                    break;
                }

                if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Invalid email format']);
                    break;
                }

                if (strlen($data['password']) < 8) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Password must be at least 8 characters']);
                    break;
                }

                try {
                    $pdo = getConnection();
                    
                    // Check if email already exists
                    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                    $stmt->execute([$data['email']]);
                    if ($stmt->fetch()) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Email already registered']);
                        break;
                    }

                    // Create new user
                    $stmt = $pdo->prepare("
                        INSERT INTO users (name, email, password, role, status)
                        VALUES (?, ?, ?, 'user', 'active')
                    ");
                    $result = $stmt->execute([
                        $data['name'],
                        $data['email'],
                        password_hash($data['password'], PASSWORD_DEFAULT)
                    ]);

                    if ($result) {
                        $user_id = $pdo->lastInsertId();
                        echo json_encode([
                            'success' => true,
                            'message' => 'Registration successful',
                            'data' => ['user_id' => $user_id]
                        ]);
                    } else {
                        throw new PDOException('Failed to create user');
                    }
                } catch (PDOException $e) {
                    http_response_code(500);
                    echo json_encode(['success' => false, 'message' => 'Failed to register user']);
                }
                break;

            case 'change_password':
                if (!isset($data['current_password']) || !isset($data['new_password'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Current password and new password are required']);
                    break;
                }

                if (strlen($data['new_password']) < 8) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'New password must be at least 8 characters']);
                    break;
                }

                try {
                    $pdo = getConnection();
                    
                    // Verify current password
                    $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
                    $stmt->execute([$_SESSION['user_id']]);
                    $user = $stmt->fetch();

                    if (!$user || !password_verify($data['current_password'], $user['password'])) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Current password is incorrect']);
                        break;
                    }

                    // Update password
                    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                    $result = $stmt->execute([
                        password_hash($data['new_password'], PASSWORD_DEFAULT),
                        $_SESSION['user_id']
                    ]);

                    if ($result) {
                        echo json_encode(['success' => true, 'message' => 'Password changed successfully']);
                    } else {
                        throw new PDOException('Failed to change password');
                    }
                } catch (PDOException $e) {
                    http_response_code(500);
                    echo json_encode(['success' => false, 'message' => 'Failed to change password']);
                }
                break;

            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
                break;
        }
        break;

    case 'PUT':
        // Update user profile
        if (!isset($data['id']) || (!is_admin() && $data['id'] != $_SESSION['user_id'])) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        try {
            $pdo = getConnection();
            $updates = [];
            $params = [];

            $fields = ['name', 'email', 'role', 'status'];
            foreach ($fields as $field) {
                if (isset($data[$field])) {
                    // Only admin can update role and status
                    if (($field === 'role' || $field === 'status') && !is_admin()) {
                        continue;
                    }

                    // Validate email format
                    if ($field === 'email' && !filter_var($data[$field], FILTER_VALIDATE_EMAIL)) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
                        break 2;
                    }

                    $updates[] = "$field = ?";
                    $params[] = $data[$field];
                }
            }

            if (empty($updates)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'No fields to update']);
                break;
            }

            $params[] = $data['id'];
            $query = "UPDATE users SET " . implode(', ', $updates) . " WHERE id = ?";
            
            $stmt = $pdo->prepare($query);
            $result = $stmt->execute($params);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Profile updated successfully']);
            } else {
                throw new PDOException('Failed to update profile');
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update profile']);
        }
        break;

    case 'DELETE':
        // Delete user (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'User ID is required']);
            break;
        }

        try {
            $pdo = getConnection();
            $stmt = $pdo->prepare("UPDATE users SET status = 'deleted' WHERE id = ?");
            $result = $stmt->execute([$data['id']]);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'User deleted successfully']);
            } else {
                throw new PDOException('Failed to delete user');
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete user']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
} 