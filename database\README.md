# Database Tewuneed E-Commerce

## 📋 Overview

Database yang telah direstrukturisasi dan dioptimasi untuk website e-commerce Tewuneed. Database ini dirancang dengan prinsip normalisasi yang baik, performa tinggi, dan skalabilitas untuk mendukung operasi e-commerce yang kompleks.

## 🚀 Quick Start

### 1. Import Database

**Menggunakan Script PHP:**
```bash
# Akses melalui browser
http://localhost/tewuneed2/database/import_database.php
```

**Menggunakan MySQL Command Line:**
```bash
mysql -u root -p < database/db_tewuneed.sql
```

**Menggunakan phpMyAdmin:**
1. Buka phpMyAdmin
2. Klik "Import"
3. Pilih file `db_tewuneed.sql`
4. Klik "Go"

### 2. Test Database

```bash
# Akses melalui browser
http://localhost/tewuneed2/database/test_database.php
```

### 3. Default Login

**Admin Access:**
- Username: `admin`
- Email: `<EMAIL>`
- Password: `password` (⚠️ **Segera ganti!**)

## 📊 Database Structure

### Core Tables (22 Tables)

| Category | Tables | Description |
|----------|--------|-------------|
| **User Management** | `users`, `user_addresses`, `admin_accounts` | Manajemen pengguna dan admin |
| **Product Catalog** | `categories`, `products`, `product_attributes`, `product_attribute_values`, `product_reviews` | Katalog produk lengkap |
| **Shopping** | `cart`, `wishlist` | Keranjang belanja dan wishlist |
| **Order Management** | `orders`, `order_items`, `order_status_history` | Manajemen pesanan |
| **Payment** | `payments` | Sistem pembayaran |
| **Shipping** | `shipping_methods`, `order_shipping` | Manajemen pengiriman |
| **Inventory** | `stock_movements` | Tracking stok |
| **Promotions** | `coupons`, `coupon_usage` | Sistem kupon dan diskon |
| **System** | `system_settings` | Konfigurasi sistem |
| **Analytics** | `activity_logs`, `product_views` | Logging dan analitik |

### Views (4 Views)

- `product_summary` - Ringkasan produk dengan kategori
- `order_summary` - Ringkasan pesanan dengan customer
- `sales_analytics` - Analitik penjualan harian
- `low_stock_products` - Produk dengan stok rendah

### Triggers (4 Triggers)

- `update_product_rating_after_review_insert` - Auto-update rating produk
- `update_product_rating_after_review_update` - Update rating saat review berubah
- `log_order_status_change` - Log perubahan status pesanan
- `update_stock_after_order` - Update stok otomatis

### Stored Procedures (2 Procedures)

- `GetProductDetails(product_id)` - Detail lengkap produk
- `CalculateOrderTotal(order_id)` - Kalkulasi total pesanan

## 🔧 Configuration

### Database Settings

```php
// config/database.php
$config = [
    'host' => 'localhost',
    'dbname' => 'db_tewuneed',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'engine' => 'InnoDB'
];
```

### System Settings

Konfigurasi sistem tersimpan di tabel `system_settings`:

| Setting Key | Default Value | Description |
|-------------|---------------|-------------|
| `store_name` | Tewuneed | Nama toko |
| `store_email` | <EMAIL> | Email toko |
| `currency` | IDR | Mata uang |
| `tax_rate` | 11 | Tarif pajak (%) |
| `maintenance_mode` | 0 | Mode maintenance |

## 📈 Features

### ✅ User Management
- Multi-role system (admin/customer)
- Multiple addresses per user
- Email verification
- Password reset
- Profile management

### ✅ Product Catalog
- Hierarchical categories
- Product attributes & variations
- Image gallery (JSON)
- SEO-friendly URLs
- Stock tracking
- Rating & review system

### ✅ Shopping Experience
- Shopping cart (guest & registered)
- Wishlist functionality
- Product search (full-text)
- Product filtering

### ✅ Order Management
- Comprehensive order tracking
- Multiple payment methods
- Order status history
- Shipping integration
- Order cancellation

### ✅ Payment System
- Multiple payment gateways
- Payment status tracking
- Transaction logging
- Refund support

### ✅ Inventory Management
- Stock movement tracking
- Low stock alerts
- Automatic stock updates
- Inventory reports

### ✅ Promotions
- Coupon system
- Multiple discount types
- Usage tracking
- Date-based validity

### ✅ Analytics & Reporting
- Sales analytics
- Product view tracking
- Activity logging
- Performance metrics

## 🔒 Security Features

- **Password Hashing**: bcrypt/argon2 support
- **SQL Injection Protection**: Prepared statements
- **XSS Protection**: Input sanitization
- **CSRF Protection**: Token validation
- **Role-based Access**: Permission system
- **Activity Logging**: Audit trail

## ⚡ Performance Optimizations

- **Strategic Indexing**: Optimized for common queries
- **Query Optimization**: Efficient joins and subqueries
- **Data Types**: Appropriate storage types
- **Triggers**: Automated data consistency
- **Views**: Pre-computed complex queries
- **Full-text Search**: Product search optimization

## 📝 Sample Data

Database sudah dilengkapi dengan:

- ✅ 1 Admin user
- ✅ 8 Product categories
- ✅ 8 Sample products
- ✅ 4 Shipping methods
- ✅ 10 System settings
- ✅ 3 Sample coupons
- ✅ 5 Product attributes

## 🔄 Migration Guide

### From Old Database

1. **Backup existing data**
2. **Map old structure to new**
3. **Run migration scripts**
4. **Verify data integrity**
5. **Update application code**

### Data Mapping

| Old Table | New Table | Notes |
|-----------|-----------|-------|
| `users` | `users` | Enhanced with new fields |
| `products` | `products` | Added SEO and rating fields |
| `orders` | `orders` | JSON address storage |
| `categories` | `categories` | Added hierarchy support |

## 🛠️ Maintenance

### Regular Tasks

1. **Daily Backup**
   ```bash
   mysqldump -u root -p db_tewuneed > backup_$(date +%Y%m%d).sql
   ```

2. **Weekly Optimization**
   ```sql
   OPTIMIZE TABLE products, orders, order_items;
   ```

3. **Monthly Cleanup**
   ```sql
   DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 3 MONTH);
   ```

### Monitoring

- Monitor slow queries
- Check index usage
- Review storage usage
- Analyze query patterns

## 🐛 Troubleshooting

### Common Issues

**Connection Error:**
- Check MySQL service
- Verify credentials
- Check firewall settings

**Import Fails:**
- Check file permissions
- Verify SQL syntax
- Check MySQL version compatibility

**Performance Issues:**
- Review query execution plans
- Check index usage
- Monitor server resources

## 📞 Support

Untuk bantuan teknis:
1. Check documentation
2. Review error logs
3. Test with sample queries
4. Contact development team

## 📄 License

Database structure untuk Tewuneed E-Commerce Platform.
Developed for internal use.

---

**Last Updated:** 2024
**Version:** 2.0
**Compatibility:** MySQL 5.7+, MariaDB 10.2+
