<?php
// <PERSON><PERSON><PERSON> apakah sesi sudah dimulai sebelum mengatur konfigurasi
if (session_status() == PHP_SESSION_NONE) {
    // Session settings - hanya diatur jika sesi belum dimulai
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    // Matikan secure cookie karena kita menggunakan HTTP di localhost
    ini_set('session.cookie_secure', 0);
    
    // Start session
    session_start();
} else {
    // Jika sesi sudah aktif, tidak perlu mengubah pengaturan
    // Ini akan mencegah warning
}
?>