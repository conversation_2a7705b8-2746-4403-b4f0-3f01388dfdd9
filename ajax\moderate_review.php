<?php
session_start();
require_once "../includes/db_connect.php";

header("Content-Type: application/json");

if (!isset($_SESSION["user_id"]) || $_SESSION["role"] !== "admin") {
    echo json_encode(["success" => false, "message" => "Unauthorized access"]);
    exit;
}

$review_id = $_POST["review_id"] ?? 0;
$action = $_POST["action"] ?? ""; // approve, reject, flag

$valid_actions = ["approve", "reject", "flag"];
if (!in_array($action, $valid_actions)) {
    echo json_encode(["success" => false, "message" => "Invalid action"]);
    exit;
}

try {
    $status_map = [
        "approve" => "approved",
        "reject" => "rejected", 
        "flag" => "flagged"
    ];
    
    $stmt = $conn->prepare("
        UPDATE product_reviews 
        SET status = ?, moderated_by = ?, moderated_at = NOW()
        WHERE review_id = ?
    ");
    $stmt->execute([$status_map[$action], $_SESSION["user_id"], $review_id]);
    
    if ($stmt->rowCount() > 0) {
        // If approved, update product rating summary
        if ($action === "approve") {
            $stmt = $conn->prepare("
                SELECT product_id, rating FROM product_reviews WHERE review_id = ?
            ");
            $stmt->execute([$review_id]);
            $review = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($review) {
                // Recalculate product rating
                $stmt = $conn->prepare("
                    SELECT 
                        COUNT(*) as total_reviews,
                        AVG(rating) as average_rating,
                        SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as rating_1,
                        SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as rating_2,
                        SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as rating_3,
                        SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as rating_4,
                        SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as rating_5
                    FROM product_reviews 
                    WHERE product_id = ? AND status = \"approved\"
                ");
                $stmt->execute([$review["product_id"]]);
                $rating_data = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Update rating summary
                $stmt = $conn->prepare("
                    INSERT INTO product_rating_summary 
                    (product_id, total_reviews, average_rating, rating_1, rating_2, rating_3, rating_4, rating_5, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE
                    total_reviews = VALUES(total_reviews),
                    average_rating = VALUES(average_rating),
                    rating_1 = VALUES(rating_1),
                    rating_2 = VALUES(rating_2),
                    rating_3 = VALUES(rating_3),
                    rating_4 = VALUES(rating_4),
                    rating_5 = VALUES(rating_5),
                    updated_at = NOW()
                ");
                $stmt->execute([
                    $review["product_id"],
                    $rating_data["total_reviews"],
                    $rating_data["average_rating"],
                    $rating_data["rating_1"],
                    $rating_data["rating_2"],
                    $rating_data["rating_3"],
                    $rating_data["rating_4"],
                    $rating_data["rating_5"]
                ]);
            }
        }
        
        echo json_encode([
            "success" => true,
            "message" => "Review " . $action . "d successfully",
            "new_status" => $status_map[$action]
        ]);
    } else {
        echo json_encode(["success" => false, "message" => "Review not found"]);
    }
} catch (Exception $e) {
    echo json_encode(["success" => false, "message" => $e->getMessage()]);
}
?>