<?php
/**
 * Payment Gateway Configuration
 * Konfigurasi untuk berbagai payment gateway
 */

// Midtrans Configuration
define('MIDTRANS_SERVER_KEY', 'SB-Mid-server-YOUR_SERVER_KEY'); // Sandbox key
define('MIDTRANS_CLIENT_KEY', 'SB-Mid-client-YOUR_CLIENT_KEY'); // Sandbox key
define('MIDTRANS_IS_PRODUCTION', false); // Set to true for production
define('MIDTRANS_IS_SANITIZED', true);
define('MIDTRANS_IS_3DS', true);

// Xendit Configuration (Alternative)
define('XENDIT_SECRET_KEY', 'xnd_development_YOUR_SECRET_KEY'); // Development key
define('XENDIT_PUBLIC_KEY', 'xnd_public_development_YOUR_PUBLIC_KEY'); // Development key
define('XENDIT_IS_PRODUCTION', false);

// Payment Configuration
$payment_config = [
    'default_currency' => 'IDR',
    'transaction_timeout' => 24, // hours
    'auto_cancel_timeout' => 1, // hours for pending payments
    
    // Fee Configuration
    'admin_fee' => [
        'ewallet' => 0, // No fee for e-wallet
        'va' => 4000, // Rp 4.000 for Virtual Account
        'card' => [
            'percentage' => 2.9, // 2.9%
            'fixed' => 2000 // + Rp 2.000
        ],
        'bank_transfer' => 0,
        'cod' => 5000, // Rp 5.000 for COD
        'installment' => [
            '0' => 0, // 0% installment
            '3' => 1.5, // 1.5% for 3 months
            '6' => 2.5, // 2.5% for 6 months
            '12' => 3.5 // 3.5% for 12 months
        ]
    ],
    
    // Minimum amounts
    'min_amount' => [
        'ewallet' => 1000,
        'va' => 10000,
        'card' => 10000,
        'bank_transfer' => 10000,
        'cod' => 10000,
        'installment' => 500000,
        'paylater' => 50000
    ],
    
    // Maximum amounts
    'max_amount' => [
        'ewallet' => ********,
        'va' => ********,
        'card' => ********,
        'bank_transfer' => ********,
        'cod' => 5000000,
        'installment' => ********,
        'paylater' => ********
    ],
    
    // Payment method availability by region
    'regional_availability' => [
        'jakarta' => ['all'],
        'bandung' => ['all'],
        'surabaya' => ['all'],
        'medan' => ['ewallet', 'va', 'card', 'bank_transfer'],
        'makassar' => ['ewallet', 'va', 'card', 'bank_transfer'],
        'other' => ['va', 'card', 'bank_transfer', 'cod']
    ],
    
    // Notification URLs
    'notification_urls' => [
        'midtrans' => [
            'notification' => 'https://yourdomain.com/api/payment/midtrans/notification',
            'finish' => 'https://yourdomain.com/payment/finish',
            'unfinish' => 'https://yourdomain.com/payment/unfinish',
            'error' => 'https://yourdomain.com/payment/error'
        ],
        'xendit' => [
            'callback' => 'https://yourdomain.com/api/payment/xendit/callback'
        ]
    ]
];

// Bank Account Information for Manual Transfer
$bank_accounts = [
    'bca' => [
        'bank_name' => 'Bank Central Asia (BCA)',
        'account_number' => '**********',
        'account_name' => 'PT Tewuneed Indonesia',
        'swift_code' => 'CENAIDJA'
    ],
    'mandiri' => [
        'bank_name' => 'Bank Mandiri',
        'account_number' => '*************',
        'account_name' => 'PT Tewuneed Indonesia',
        'swift_code' => 'BMRIIDJA'
    ],
    'bni' => [
        'bank_name' => 'Bank Negara Indonesia (BNI)',
        'account_number' => '**********',
        'account_name' => 'PT Tewuneed Indonesia',
        'swift_code' => 'BNINIDJA'
    ],
    'bri' => [
        'bank_name' => 'Bank Rakyat Indonesia (BRI)',
        'account_number' => '***************',
        'account_name' => 'PT Tewuneed Indonesia',
        'swift_code' => 'BRINIDJA'
    ]
];

// Payment Status Mapping
$payment_status_mapping = [
    'midtrans' => [
        'pending' => 'pending',
        'settlement' => 'paid',
        'capture' => 'paid',
        'deny' => 'failed',
        'cancel' => 'cancelled',
        'expire' => 'expired',
        'failure' => 'failed',
        'refund' => 'refunded',
        'partial_refund' => 'paid'
    ],
    'xendit' => [
        'PENDING' => 'pending',
        'PAID' => 'paid',
        'SETTLED' => 'paid',
        'FAILED' => 'failed',
        'EXPIRED' => 'expired'
    ]
];

// Error Messages
$payment_error_messages = [
    'insufficient_balance' => 'Saldo tidak mencukupi',
    'card_declined' => 'Kartu ditolak oleh bank',
    'expired_card' => 'Kartu sudah kadaluarsa',
    'invalid_card' => 'Nomor kartu tidak valid',
    'transaction_timeout' => 'Transaksi timeout',
    'network_error' => 'Gangguan jaringan',
    'system_error' => 'Terjadi kesalahan sistem',
    'amount_too_small' => 'Jumlah pembayaran terlalu kecil',
    'amount_too_large' => 'Jumlah pembayaran terlalu besar',
    'payment_method_unavailable' => 'Metode pembayaran tidak tersedia',
    'duplicate_transaction' => 'Transaksi duplikat'
];

// Success Messages
$payment_success_messages = [
    'payment_created' => 'Pembayaran berhasil dibuat',
    'payment_completed' => 'Pembayaran berhasil diselesaikan',
    'refund_processed' => 'Refund berhasil diproses',
    'installment_created' => 'Cicilan berhasil dibuat'
];

// Email Templates for Payment
$payment_email_templates = [
    'payment_pending' => [
        'subject' => 'Menunggu Pembayaran - Order #{order_number}',
        'template' => 'emails/payment_pending.php'
    ],
    'payment_success' => [
        'subject' => 'Pembayaran Berhasil - Order #{order_number}',
        'template' => 'emails/payment_success.php'
    ],
    'payment_failed' => [
        'subject' => 'Pembayaran Gagal - Order #{order_number}',
        'template' => 'emails/payment_failed.php'
    ],
    'payment_expired' => [
        'subject' => 'Pembayaran Kadaluarsa - Order #{order_number}',
        'template' => 'emails/payment_expired.php'
    ],
    'refund_processed' => [
        'subject' => 'Refund Diproses - Order #{order_number}',
        'template' => 'emails/refund_processed.php'
    ]
];

// WhatsApp Templates (if using WhatsApp API)
$whatsapp_templates = [
    'payment_pending' => 'Halo {customer_name}, pembayaran untuk order #{order_number} sebesar Rp {amount} menunggu konfirmasi. Silakan selesaikan pembayaran sebelum {expired_time}.',
    'payment_success' => 'Halo {customer_name}, pembayaran untuk order #{order_number} sebesar Rp {amount} telah berhasil. Pesanan Anda sedang diproses.',
    'payment_reminder' => 'Halo {customer_name}, pembayaran untuk order #{order_number} akan berakhir dalam 2 jam. Silakan selesaikan pembayaran Anda.'
];

// Export configurations
return [
    'midtrans' => [
        'server_key' => MIDTRANS_SERVER_KEY,
        'client_key' => MIDTRANS_CLIENT_KEY,
        'is_production' => MIDTRANS_IS_PRODUCTION,
        'is_sanitized' => MIDTRANS_IS_SANITIZED,
        'is_3ds' => MIDTRANS_IS_3DS
    ],
    'xendit' => [
        'secret_key' => XENDIT_SECRET_KEY,
        'public_key' => XENDIT_PUBLIC_KEY,
        'is_production' => XENDIT_IS_PRODUCTION
    ],
    'payment_config' => $payment_config,
    'bank_accounts' => $bank_accounts,
    'status_mapping' => $payment_status_mapping,
    'error_messages' => $payment_error_messages,
    'success_messages' => $payment_success_messages,
    'email_templates' => $payment_email_templates,
    'whatsapp_templates' => $whatsapp_templates
];
?>
