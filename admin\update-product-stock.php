<?php
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';
require_once 'auth.php';

// Get JSON data
$data = json_decode(file_get_contents('php://input'), true);
if (!$data) $data = $_POST;

if (!isset($data['action'])) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
    exit();
}

if (!isset($data['product_id']) || !isset($data['stock'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid data']);
    exit();
}

try {
    // Get current stock
    $stmt = $conn->prepare("SELECT stock FROM products WHERE product_id = ?");
    $stmt->execute([$data['product_id']]);
    $current_stock = $stmt->fetch()['stock'];

    // Update stock
    $stmt = $conn->prepare("UPDATE products SET stock = ? WHERE product_id = ?");
    $stmt->execute([$data['stock'], $data['product_id']]);

    // Log stock mutation
    $stmt = $conn->prepare("INSERT INTO stock_mutations (product_id, quantity, mutation_type, reference_type, notes) 
                           VALUES (?, ?, 'adjustment', 'manual', 'Manual stock adjustment')");
    $stmt->execute([$data['product_id'], $data['stock'] - $current_stock]);
    
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
