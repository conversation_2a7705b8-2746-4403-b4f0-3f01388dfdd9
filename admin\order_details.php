<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/order_status_functions.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    echo '<div class="alert alert-danger">Access denied</div>';
    exit;
}

$order_id = (int)($_GET['id'] ?? 0);

if (!$order_id) {
    echo '<div class="alert alert-danger">Invalid order ID</div>';
    exit;
}

// Get order with status info
$order = getOrderWithStatus($conn, $order_id);

if (!$order) {
    echo '<div class="alert alert-danger">Order not found</div>';
    exit;
}

// Get order items
$stmt = $conn->prepare("
    SELECT oi.*, COALESCE(p.name, p.NAME) as product_name, p.image 
    FROM order_items oi 
    LEFT JOIN products p ON oi.product_id = p.product_id 
    WHERE oi.order_id = ?
");
$stmt->execute([$order_id]);
$order_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

$statuses = getOrderStatuses();
?>

<div class="row">
    <!-- Order Information -->
    <div class="col-md-6">
        <h6 class="fw-bold mb-3">Order Information</h6>
        <table class="table table-borderless table-sm">
            <tr>
                <td class="fw-bold">Order ID:</td>
                <td>#<?php echo $order['order_id']; ?></td>
            </tr>
            <?php if (!empty($order['order_number'])): ?>
            <tr>
                <td class="fw-bold">Order Number:</td>
                <td><?php echo htmlspecialchars($order['order_number']); ?></td>
            </tr>
            <?php endif; ?>
            <tr>
                <td class="fw-bold">Customer:</td>
                <td><?php echo htmlspecialchars($order['customer_name'] ?? 'N/A'); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">Email:</td>
                <td><?php echo htmlspecialchars($order['customer_email'] ?? 'N/A'); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">Order Date:</td>
                <td><?php echo date('d M Y H:i', strtotime($order['order_date'])); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">Payment Method:</td>
                <td><?php echo ucfirst(str_replace('_', ' ', $order['payment_method'])); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">Total Amount:</td>
                <td class="fw-bold text-success">Rp <?php echo number_format($order['total_amount'], 0, ',', '.'); ?></td>
            </tr>
        </table>
    </div>
    
    <!-- Shipping Information -->
    <div class="col-md-6">
        <h6 class="fw-bold mb-3">Shipping Information</h6>
        <table class="table table-borderless table-sm">
            <tr>
                <td class="fw-bold">Name:</td>
                <td><?php echo htmlspecialchars($order['shipping_name']); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">Phone:</td>
                <td><?php echo htmlspecialchars($order['shipping_phone']); ?></td>
            </tr>
            <tr>
                <td class="fw-bold">Address:</td>
                <td><?php echo htmlspecialchars($order['shipping_address']); ?></td>
            </tr>
            <?php if (!empty($order['shipping_method'])): ?>
            <tr>
                <td class="fw-bold">Shipping Method:</td>
                <td><?php echo ucfirst(str_replace('_', ' ', $order['shipping_method'])); ?></td>
            </tr>
            <?php endif; ?>
            <?php if (!empty($order['tracking_number'])): ?>
            <tr>
                <td class="fw-bold">Tracking Number:</td>
                <td><code><?php echo $order['tracking_number']; ?></code></td>
            </tr>
            <?php endif; ?>
            <?php if (!empty($order['estimated_delivery'])): ?>
            <tr>
                <td class="fw-bold">Estimated Delivery:</td>
                <td><?php echo date('d M Y', strtotime($order['estimated_delivery'])); ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>
</div>

<!-- Current Status -->
<div class="row mt-4">
    <div class="col-12">
        <h6 class="fw-bold mb-3">Current Status</h6>
        <div class="alert alert-<?php echo $order['status_info']['color']; ?> d-flex align-items-center">
            <i class="<?php echo $order['status_info']['icon']; ?> fa-2x me-3"></i>
            <div>
                <h6 class="mb-1"><?php echo $order['status_info']['label']; ?></h6>
                <p class="mb-0"><?php echo $order['status_info']['description']; ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Order Items -->
<?php if (count($order_items) > 0): ?>
<div class="row mt-4">
    <div class="col-12">
        <h6 class="fw-bold mb-3">Order Items</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th class="text-center">Quantity</th>
                        <th class="text-end">Price</th>
                        <th class="text-end">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $items_subtotal = 0;
                    foreach ($order_items as $item): 
                        $item_total = $item['price'] * $item['quantity'];
                        $items_subtotal += $item_total;
                    ?>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <?php if (!empty($item['image']) && file_exists('../' . $item['image'])): ?>
                                <img src="../<?php echo htmlspecialchars($item['image']); ?>" alt="Product" class="img-thumbnail me-2" width="40">
                                <?php else: ?>
                                <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                <?php endif; ?>
                                <div>
                                    <div class="fw-bold"><?php echo htmlspecialchars($item['product_name'] ?? 'Product #' . $item['product_id']); ?></div>
                                    <small class="text-muted">ID: <?php echo $item['product_id']; ?></small>
                                </div>
                            </div>
                        </td>
                        <td class="text-center"><?php echo $item['quantity']; ?></td>
                        <td class="text-end">Rp <?php echo number_format($item['price'], 0, ',', '.'); ?></td>
                        <td class="text-end">Rp <?php echo number_format($item_total, 0, ',', '.'); ?></td>
                    </tr>
                    <?php endforeach; ?>
                    <tr class="border-top">
                        <td colspan="3" class="text-end fw-bold">Subtotal:</td>
                        <td class="text-end fw-bold">Rp <?php echo number_format($items_subtotal, 0, ',', '.'); ?></td>
                    </tr>
                    <?php if (!empty($order['shipping_cost']) && $order['shipping_cost'] > 0): ?>
                    <tr>
                        <td colspan="3" class="text-end">Shipping:</td>
                        <td class="text-end">Rp <?php echo number_format($order['shipping_cost'], 0, ',', '.'); ?></td>
                    </tr>
                    <?php endif; ?>
                    <tr class="border-top">
                        <td colspan="3" class="text-end fw-bold fs-6">Total:</td>
                        <td class="text-end fw-bold fs-6 text-success">Rp <?php echo number_format($order['total_amount'], 0, ',', '.'); ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Status History -->
<?php if (count($order['status_history']) > 0): ?>
<div class="row mt-4">
    <div class="col-12">
        <h6 class="fw-bold mb-3">Status History</h6>
        <div class="timeline">
            <?php foreach ($order['status_history'] as $index => $history): ?>
            <div class="timeline-item <?php echo $index === 0 ? 'active' : ''; ?>">
                <div class="timeline-marker">
                    <i class="fas fa-circle"></i>
                </div>
                <div class="timeline-content">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1"><?php echo htmlspecialchars($history['status_label']); ?></h6>
                            <?php if (!empty($history['notes'])): ?>
                            <p class="mb-1 text-muted small"><?php echo htmlspecialchars($history['notes']); ?></p>
                            <?php endif; ?>
                            <small class="text-muted">
                                <?php echo date('d M Y H:i', strtotime($history['created_at'])); ?>
                                <?php if ($history['updated_by_name']): ?>
                                by <?php echo htmlspecialchars($history['updated_by_name']); ?>
                                <?php endif; ?>
                                (<?php echo ucfirst($history['updated_by_type']); ?>)
                            </small>
                        </div>
                        <span class="badge bg-<?php echo $statuses[$history['status']]['color'] ?? 'secondary'; ?>">
                            <i class="<?php echo $statuses[$history['status']]['icon'] ?? 'fas fa-circle'; ?> me-1"></i>
                            <?php echo $history['status']; ?>
                        </span>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timeline-item.active .timeline-marker {
    background: #0d6efd;
}

.timeline-marker i {
    font-size: 8px;
    color: white;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
}

.timeline-item.active .timeline-content {
    border-left-color: #0d6efd;
    background: #e7f3ff;
}
</style>
