/**
 * Notification Manager
 * Manages notifications with staggered timing and queue system
 */

class NotificationManager {
    constructor() {
        this.queue = [];
        this.activeCount = 0;
        this.maxActive = 3;
        this.minDelay = 1500; // Minimum delay between notifications
        this.lastNotificationTime = 0;
        this.isDisabled = false; // Flag to check if notifications are disabled
        
        // Check if notifications are disabled globally
        if (window.DISABLE_POPUP_NOTIFICATIONS === true) {
            this.isDisabled = true;
            console.log('Notifications are disabled globally');
        }
    }
    
    /**
     * Add a notification to the queue
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, danger, warning, info)
     * @param {Object} options - Additional options
     */
    addNotification(message, type = 'success', options = {}) {
        // Skip if notifications are disabled
        if (this.isDisabled) {
            console.log('Notification skipped (disabled):', message);
            return;
        }
        
        // Skip error notifications if they contain blocked messages
        if (type === 'danger' || type === 'error') {
            const blockedMessages = [
                'Error adding to cart',
                'Error adding product to cart',
                'Gagal menambahkan produk ke keranjang',
                'Please try again',
                'Database error occurred',
                'Gagal mereservasi stok'
            ];
            
            const shouldBlock = blockedMessages.some(blocked => 
                message && message.toLowerCase().includes(blocked.toLowerCase())
            );
            
            if (shouldBlock) {
                console.log('Blocked error notification:', message);
                return;
            }
        }
        
        // Add to queue
        this.queue.push({ message, type, options });
        
        // Process queue
        this.processQueue();
    }
    
    /**
     * Process the notification queue
     */
    processQueue() {
        // If notifications are disabled, clear queue and return
        if (this.isDisabled) {
            this.queue = [];
            return;
        }
        
        // If we're at max active notifications, wait
        if (this.activeCount >= this.maxActive) {
            return;
        }
        
        // If queue is empty, nothing to do
        if (this.queue.length === 0) {
            return;
        }
        
        // Calculate delay based on last notification time
        const now = Date.now();
        const timeSinceLast = now - this.lastNotificationTime;
        const delay = Math.max(0, this.minDelay - timeSinceLast);
        
        // Show next notification after delay
        setTimeout(() => {
            if (this.queue.length > 0) {
                const notification = this.queue.shift();
                this.showNotification(notification.message, notification.type, notification.options);
                this.lastNotificationTime = Date.now();
                
                // Process next in queue
                this.processQueue();
            }
        }, delay);
    }
    
    /**
     * Show a notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type
     * @param {Object} options - Additional options
     */
    showNotification(message, type, options) {
        // Skip if notifications are disabled
        if (this.isDisabled) {
            return;
        }
        
        this.activeCount++;
        
        // Use Toastify if available
        if (typeof Toastify !== 'undefined') {
            const background = this.getBackgroundColor(type);
            
            try {
                Toastify({
                    text: message,
                    duration: options.duration || 3000,
                    close: true,
                    gravity: "top",
                    position: "right",
                    backgroundColor: background,
                    stopOnFocus: true,
                    onClick: options.onClick || null,
                    callback: () => {
                        this.activeCount--;
                        this.processQueue();
                        if (options.callback) options.callback();
                    }
                }).showToast();
            } catch (error) {
                console.error('Error showing Toastify notification:', error);
                // Fallback to custom notification
                this.showCustomNotification(message, type, options);
            }
        } else {
            // Fallback to custom notification
            this.showCustomNotification(message, type, options);
        }
    }
    
    /**
     * Show a custom notification when Toastify is not available
     * @param {string} message - Notification message
     * @param {string} type - Notification type
     * @param {Object} options - Additional options
     */
    showCustomNotification(message, type, options) {
        // Skip if notifications are disabled
        if (this.isDisabled) {
            return;
        }
        
        const container = this.getOrCreateContainer();
        
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        // Add cart-notification class if it's a cart notification
        if (options.isCartNotification) {
            notification.classList.add('cart-notification');
        }
        
        // Create content
        const content = document.createElement('div');
        content.className = 'notification-content';
        
        // Create message
        const messageEl = document.createElement('div');
        messageEl.className = 'notification-message';
        messageEl.textContent = message;
        
        // Create close button
        const closeBtn = document.createElement('button');
        closeBtn.className = 'notification-close';
        closeBtn.innerHTML = '&times;';
        closeBtn.setAttribute('aria-label', 'Close');
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification);
        });
        
        // Assemble notification
        content.appendChild(messageEl);
        content.appendChild(closeBtn);
        notification.appendChild(content);
        
        // Add click handler if provided
        if (options.onClick) {
            notification.style.cursor = 'pointer';
            notification.addEventListener('click', (e) => {
                if (e.target !== closeBtn) {
                    options.onClick(e);
                }
            });
        }
        
        // Add to container
        container.appendChild(notification);
        
        // Auto remove after duration
        const duration = options.duration || 3000;
        setTimeout(() => {
            this.removeNotification(notification);
            if (options.callback) options.callback();
        }, duration);
    }
    
    /**
     * Remove a notification element
     * @param {HTMLElement} notification - The notification element
     */
    removeNotification(notification) {
        notification.classList.add('removing');
        
        // Wait for animation to complete
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.activeCount--;
            this.processQueue();
        }, 300);
    }
    
    /**
     * Get or create the notification container
     * @returns {HTMLElement} The notification container
     */
    getOrCreateContainer() {
        let container = document.getElementById('notification-container');
        
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            document.body.appendChild(container);
        }
        
        return container;
    }
    
    /**
     * Get background color based on notification type
     * @param {string} type - Notification type
     * @returns {string} Background color
     */
    getBackgroundColor(type) {
        switch(type) {
            case 'success':
                return 'linear-gradient(to right, #00b09b, #96c93d)';
            case 'danger':
            case 'error':
                return 'linear-gradient(to right, #ff416c, #ff4b2b)';
            case 'warning':
                return 'linear-gradient(to right, #f46b45, #eea849)';
            case 'info':
                return 'linear-gradient(to right, #2193b0, #6dd5ed)';
            default:
                return 'linear-gradient(to right, #00b09b, #96c93d)';
        }
    }
    
    /**
     * Clear all notifications
     */
    clearAll() {
        // Clear queue
        this.queue = [];
        
        // Remove all notifications
        const container = document.getElementById('notification-container');
        if (container) {
            container.innerHTML = '';
        }
        
        // Reset active count
        this.activeCount = 0;
    }
    
    /**
     * Enable notifications
     */
    enable() {
        this.isDisabled = false;
        console.log('Notifications enabled');
    }
    
    /**
     * Disable notifications
     */
    disable() {
        this.isDisabled = true;
        this.clearAll();
        console.log('Notifications disabled');
    }
    
    /**
     * Add a cart notification
     * @param {string} productName - Product name
     * @param {number} quantity - Quantity added
     * @param {Object} options - Additional options
     */
    addCartNotification(productName, quantity, options = {}) {
        const message = `${quantity} ${productName} telah ditambahkan ke keranjang`;
        this.addNotification(message, 'success', {
            duration: 3000,
            isCartNotification: true,
            ...options
        });
    }
}

// Create global instance
window.notificationManager = new NotificationManager();

// Enhanced showToast function that uses the notification manager
function showToast(message, type = 'success', options = {}) {
    if (window.notificationManager) {
        window.notificationManager.addNotification(message, type, options);
    } else {
        // Fallback to direct Toastify if available
        if (typeof Toastify !== 'undefined') {
            let background = '';
            switch(type) {
                case 'success':
                    background = 'linear-gradient(to right, #00b09b, #96c93d)';
                    break;
                case 'danger':
                case 'error':
                    background = 'linear-gradient(to right, #ff416c, #ff4b2b)';
                    break;
                case 'warning':
                    background = 'linear-gradient(to right, #f46b45, #eea849)';
                    break;
                default:
                    background = 'linear-gradient(to right, #00b09b, #96c93d)';
            }
            
            Toastify({
                text: message,
                duration: options.duration || 3000,
                close: true,
                gravity: "top",
                position: "right",
                backgroundColor: background,
                stopOnFocus: true
            }).showToast();
        }
    }
}

// Enhanced addToCartNotification function
function addToCartNotification(productName, quantity, options = {}) {
    if (window.notificationManager) {
        window.notificationManager.addCartNotification(productName, quantity, options);
    } else {
        showToast(`${quantity} ${productName} telah ditambahkan ke keranjang`, 'success', options);
    }
}
