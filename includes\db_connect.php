<?php
// Database configuration
$host = 'localhost';
$dbname = 'db_tewuneed';
$username = 'root';
$password = '';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Set autocommit to true to prevent transaction issues
    $conn->setAttribute(PDO::ATTR_AUTOCOMMIT, true);

    // Set charset to utf8mb4 for better compatibility
    $conn->exec("SET NAMES utf8mb4");

    // Set SQL mode to be more permissive and avoid reserved word conflicts
    $conn->exec("SET sql_mode = 'ONLY_FULL_GROUP_BY,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'");

    // Ensure no hanging transactions
    try {
        $conn->rollback();
    } catch (Exception $e) {
        // No active transaction to rollback, which is good
    }

} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?>