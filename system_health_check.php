<?php
/**
 * TEWUNEED - System Health Check
 * Comprehensive system health monitoring and diagnostics
 */

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

$health_checks = [];
$overall_status = 'healthy';

/**
 * Health check helper function
 */
function addHealthCheck($category, $check_name, $status, $message, $details = null, $recommendation = null) {
    global $health_checks, $overall_status;
    
    if (!isset($health_checks[$category])) {
        $health_checks[$category] = [];
    }
    
    $health_checks[$category][] = [
        'name' => $check_name,
        'status' => $status, // healthy, warning, critical
        'message' => $message,
        'details' => $details,
        'recommendation' => $recommendation,
        'timestamp' => date('H:i:s')
    ];
    
    // Update overall status
    if ($status === 'critical') {
        $overall_status = 'critical';
    } elseif ($status === 'warning' && $overall_status !== 'critical') {
        $overall_status = 'warning';
    }
}

// 1. System Requirements Check
$php_version = phpversion();
$required_php = '7.4.0';

if (version_compare($php_version, $required_php, '>=')) {
    addHealthCheck(
        'System Requirements',
        'PHP Version',
        'healthy',
        "PHP $php_version is supported",
        "Current: $php_version, Required: $required_php+"
    );
} else {
    addHealthCheck(
        'System Requirements',
        'PHP Version',
        'critical',
        "PHP version is outdated",
        "Current: $php_version, Required: $required_php+",
        "Upgrade PHP to version $required_php or higher"
    );
}

// 2. Database Connection Health
try {
    $start_time = microtime(true);
    $stmt = $conn->query("SELECT 1");
    $connection_time = round((microtime(true) - $start_time) * 1000, 2);
    
    if ($connection_time < 100) {
        addHealthCheck(
            'Database',
            'Connection Speed',
            'healthy',
            'Database connection is fast',
            "Connection time: {$connection_time}ms"
        );
    } elseif ($connection_time < 500) {
        addHealthCheck(
            'Database',
            'Connection Speed',
            'warning',
            'Database connection is slow',
            "Connection time: {$connection_time}ms",
            'Consider optimizing database configuration'
        );
    } else {
        addHealthCheck(
            'Database',
            'Connection Speed',
            'critical',
            'Database connection is very slow',
            "Connection time: {$connection_time}ms",
            'Check database server performance and network connectivity'
        );
    }
} catch (Exception $e) {
    addHealthCheck(
        'Database',
        'Connection',
        'critical',
        'Database connection failed',
        $e->getMessage(),
        'Check database server status and configuration'
    );
}

// 3. File System Health
$upload_dir = 'uploads/';
$cache_dir = 'cache/';

// Check upload directory
if (is_dir($upload_dir)) {
    if (is_writable($upload_dir)) {
        addHealthCheck(
            'File System',
            'Upload Directory',
            'healthy',
            'Upload directory is writable',
            "Directory: $upload_dir"
        );
    } else {
        addHealthCheck(
            'File System',
            'Upload Directory',
            'warning',
            'Upload directory is not writable',
            "Directory: $upload_dir",
            'Set proper permissions (755 or 777) for upload directory'
        );
    }
} else {
    addHealthCheck(
        'File System',
        'Upload Directory',
        'critical',
        'Upload directory does not exist',
        "Directory: $upload_dir",
        'Create upload directory and set proper permissions'
    );
}

// 4. Memory Usage
$memory_limit = ini_get('memory_limit');
$memory_usage = memory_get_usage(true);
$memory_peak = memory_get_peak_usage(true);

function formatBytes($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}

$memory_usage_formatted = formatBytes($memory_usage);
$memory_peak_formatted = formatBytes($memory_peak);

if ($memory_usage < (128 * 1024 * 1024)) { // Less than 128MB
    addHealthCheck(
        'Performance',
        'Memory Usage',
        'healthy',
        'Memory usage is optimal',
        "Current: $memory_usage_formatted, Peak: $memory_peak_formatted, Limit: $memory_limit"
    );
} else {
    addHealthCheck(
        'Performance',
        'Memory Usage',
        'warning',
        'Memory usage is high',
        "Current: $memory_usage_formatted, Peak: $memory_peak_formatted, Limit: $memory_limit",
        'Consider optimizing code or increasing memory limit'
    );
}

// 5. Security Checks
$security_headers = [
    'X-Content-Type-Options' => 'nosniff',
    'X-Frame-Options' => 'DENY',
    'X-XSS-Protection' => '1; mode=block'
];

$missing_headers = [];
foreach ($security_headers as $header => $value) {
    if (!isset($_SERVER['HTTP_' . str_replace('-', '_', strtoupper($header))])) {
        $missing_headers[] = $header;
    }
}

if (empty($missing_headers)) {
    addHealthCheck(
        'Security',
        'Security Headers',
        'healthy',
        'Security headers are properly configured',
        'All recommended headers present'
    );
} else {
    addHealthCheck(
        'Security',
        'Security Headers',
        'warning',
        'Some security headers are missing',
        'Missing: ' . implode(', ', $missing_headers),
        'Configure web server to send security headers'
    );
}

// 6. Session Health
$session_status = session_status();
$session_save_path = session_save_path();

if ($session_status === PHP_SESSION_ACTIVE) {
    if (is_writable($session_save_path)) {
        addHealthCheck(
            'Session',
            'Session Management',
            'healthy',
            'Session system is working correctly',
            "Save path: $session_save_path"
        );
    } else {
        addHealthCheck(
            'Session',
            'Session Management',
            'warning',
            'Session save path is not writable',
            "Save path: $session_save_path",
            'Check session directory permissions'
        );
    }
} else {
    addHealthCheck(
        'Session',
        'Session Management',
        'critical',
        'Session system is not active',
        "Status: $session_status",
        'Check session configuration'
    );
}

// 7. Extension Availability
$required_extensions = [
    'pdo' => 'PDO Database Extension',
    'pdo_mysql' => 'PDO MySQL Driver',
    'json' => 'JSON Extension',
    'mbstring' => 'Multibyte String Extension',
    'openssl' => 'OpenSSL Extension'
];

$missing_extensions = [];
foreach ($required_extensions as $ext => $description) {
    if (!extension_loaded($ext)) {
        $missing_extensions[] = "$description ($ext)";
    }
}

if (empty($missing_extensions)) {
    addHealthCheck(
        'Extensions',
        'Required Extensions',
        'healthy',
        'All required PHP extensions are loaded',
        'Extensions: ' . implode(', ', array_keys($required_extensions))
    );
} else {
    addHealthCheck(
        'Extensions',
        'Required Extensions',
        'critical',
        'Some required extensions are missing',
        'Missing: ' . implode(', ', $missing_extensions),
        'Install missing PHP extensions'
    );
}

// 8. Disk Space Check
$disk_free = disk_free_space('.');
$disk_total = disk_total_space('.');
$disk_usage_percent = round((($disk_total - $disk_free) / $disk_total) * 100, 2);

if ($disk_usage_percent < 80) {
    addHealthCheck(
        'Storage',
        'Disk Space',
        'healthy',
        'Sufficient disk space available',
        "Usage: $disk_usage_percent%, Free: " . formatBytes($disk_free)
    );
} elseif ($disk_usage_percent < 90) {
    addHealthCheck(
        'Storage',
        'Disk Space',
        'warning',
        'Disk space is getting low',
        "Usage: $disk_usage_percent%, Free: " . formatBytes($disk_free),
        'Consider cleaning up old files or expanding storage'
    );
} else {
    addHealthCheck(
        'Storage',
        'Disk Space',
        'critical',
        'Disk space is critically low',
        "Usage: $disk_usage_percent%, Free: " . formatBytes($disk_free),
        'Immediately free up disk space or expand storage'
    );
}

// Calculate summary statistics
$total_checks = 0;
$status_counts = ['healthy' => 0, 'warning' => 0, 'critical' => 0];

foreach ($health_checks as $category => $checks) {
    foreach ($checks as $check) {
        $total_checks++;
        $status_counts[$check['status']]++;
    }
}

$health_score = round(($status_counts['healthy'] / $total_checks) * 100, 1);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEWUNEED - System Health Check</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .health-card {
            margin-bottom: 1rem;
            border-left: 4px solid #dee2e6;
        }
        .health-card.healthy {
            border-left-color: #28a745;
        }
        .health-card.warning {
            border-left-color: #ffc107;
        }
        .health-card.critical {
            border-left-color: #dc3545;
        }
        .status-icon.healthy { color: #28a745; }
        .status-icon.warning { color: #ffc107; }
        .status-icon.critical { color: #dc3545; }
        .category-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        .health-score {
            font-size: 3rem;
            font-weight: bold;
        }
        .health-score.healthy { color: #28a745; }
        .health-score.warning { color: #ffc107; }
        .health-score.critical { color: #dc3545; }
        .recommendation {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 0.75rem;
            margin-top: 0.5rem;
            border-radius: 0.25rem;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4">
                        <i class="fas fa-heartbeat me-3"></i>
                        System Health Check
                    </h1>
                    <p class="lead">Comprehensive system monitoring and diagnostics</p>
                </div>

                <!-- Overall Health Score -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-md-3">
                                <h5>Health Score</h5>
                                <div class="health-score <?php echo $overall_status; ?>">
                                    <?php echo $health_score; ?>%
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h5>Total Checks</h5>
                                <span class="display-6"><?php echo $total_checks; ?></span>
                            </div>
                            <div class="col-md-2">
                                <h5>Healthy</h5>
                                <span class="display-6 text-success"><?php echo $status_counts['healthy']; ?></span>
                            </div>
                            <div class="col-md-2">
                                <h5>Warnings</h5>
                                <span class="display-6 text-warning"><?php echo $status_counts['warning']; ?></span>
                            </div>
                            <div class="col-md-2">
                                <h5>Critical</h5>
                                <span class="display-6 text-danger"><?php echo $status_counts['critical']; ?></span>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="alert <?php 
                                echo $overall_status === 'healthy' ? 'alert-success' : 
                                     ($overall_status === 'warning' ? 'alert-warning' : 'alert-danger'); 
                            ?>">
                                <strong>System Status: </strong>
                                <?php 
                                echo $overall_status === 'healthy' ? 'All systems operational' : 
                                     ($overall_status === 'warning' ? 'Some issues detected - monitoring recommended' : 
                                      'Critical issues detected - immediate attention required'); 
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Health Check Results by Category -->
                <?php foreach ($health_checks as $category => $checks): ?>
                <div class="mb-4">
                    <div class="category-header">
                        <h4 class="mb-0">
                            <i class="fas <?php 
                                echo $category === 'System Requirements' ? 'fa-cogs' : 
                                     ($category === 'Database' ? 'fa-database' : 
                                      ($category === 'File System' ? 'fa-folder' : 
                                       ($category === 'Performance' ? 'fa-tachometer-alt' : 
                                        ($category === 'Security' ? 'fa-shield-alt' : 
                                         ($category === 'Session' ? 'fa-user-clock' : 
                                          ($category === 'Extensions' ? 'fa-puzzle-piece' : 'fa-hdd'))))));
                            ?> me-2"></i>
                            <?php echo htmlspecialchars($category); ?>
                        </h4>
                    </div>
                    
                    <?php foreach ($checks as $check): ?>
                    <div class="card health-card <?php echo $check['status']; ?>">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-2">
                                        <i class="fas <?php 
                                            echo $check['status'] === 'healthy' ? 'fa-check-circle' : 
                                                 ($check['status'] === 'warning' ? 'fa-exclamation-triangle' : 'fa-times-circle'); 
                                        ?> status-icon <?php echo $check['status']; ?> me-2"></i>
                                        <?php echo htmlspecialchars($check['name']); ?>
                                    </h6>
                                    <p class="mb-2"><?php echo htmlspecialchars($check['message']); ?></p>
                                    
                                    <?php if ($check['details']): ?>
                                    <small class="text-muted">
                                        <strong>Details:</strong> <?php echo htmlspecialchars($check['details']); ?>
                                    </small>
                                    <?php endif; ?>
                                    
                                    <?php if ($check['recommendation']): ?>
                                    <div class="recommendation">
                                        <small>
                                            <i class="fas fa-lightbulb me-1"></i>
                                            <strong>Recommendation:</strong> <?php echo htmlspecialchars($check['recommendation']); ?>
                                        </small>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo $check['timestamp']; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endforeach; ?>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <button onclick="window.location.reload()" class="btn btn-primary me-2">
                        <i class="fas fa-redo me-2"></i>Refresh Health Check
                    </button>
                    <a href="run_all_tests.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-vial me-2"></i>Run System Tests
                    </a>
                    <a href="check_database.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-database me-2"></i>Database Check
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
