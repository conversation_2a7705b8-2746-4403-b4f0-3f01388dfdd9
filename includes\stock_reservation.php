<?php
/**
 * Stock Reservation Management
 * Handles stock reservations for cart items
 */

class StockReservation {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Get available stock for a product (actual stock - reserved stock)
     */
    public function getAvailableStock($product_id) {
        try {
            $stmt = $this->conn->prepare("SELECT GetAvailableStock(?) as available_stock");
            $stmt->execute([$product_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return (int)$result['available_stock'];
        } catch (Exception $e) {
            error_log("Error getting available stock: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Reserve stock for cart item
     */
    public function reserveStock($product_id, $quantity, $user_id = null, $session_id = null) {
        try {
            $stmt = $this->conn->prepare("CALL CreateStockReservation(?, ?, ?, ?, @success, @message)");
            $stmt->execute([$product_id, $user_id, $session_id, $quantity]);
            
            // Get the output parameters
            $result = $this->conn->query("SELECT @success as success, @message as message")->fetch(PDO::FETCH_ASSOC);
            
            return [
                'success' => (bool)$result['success'],
                'message' => $result['message']
            ];
        } catch (Exception $e) {
            error_log("Error reserving stock: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to reserve stock: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Remove stock reservation
     */
    public function removeReservation($product_id, $quantity, $user_id = null, $session_id = null) {
        try {
            $stmt = $this->conn->prepare("CALL RemoveStockReservation(?, ?, ?, ?, @success, @message)");
            $stmt->execute([$product_id, $user_id, $session_id, $quantity]);
            
            // Get the output parameters
            $result = $this->conn->query("SELECT @success as success, @message as message")->fetch(PDO::FETCH_ASSOC);
            
            return [
                'success' => (bool)$result['success'],
                'message' => $result['message']
            ];
        } catch (Exception $e) {
            error_log("Error removing stock reservation: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to remove stock reservation: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get user's reserved stock for a product
     */
    public function getUserReservedStock($product_id, $user_id = null, $session_id = null) {
        try {
            $sql = "SELECT COALESCE(SUM(quantity), 0) as reserved_quantity 
                    FROM cart_stock_reservations 
                    WHERE product_id = ? 
                    AND expires_at > NOW()";
            
            $params = [$product_id];
            
            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params[] = $user_id;
            } else {
                $sql .= " AND session_id = ?";
                $params[] = $session_id;
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return (int)$result['reserved_quantity'];
        } catch (Exception $e) {
            error_log("Error getting user reserved stock: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Clean up expired reservations
     */
    public function cleanupExpiredReservations() {
        try {
            $stmt = $this->conn->prepare("DELETE FROM cart_stock_reservations WHERE expires_at < NOW()");
            $stmt->execute();
            return true;
        } catch (Exception $e) {
            error_log("Error cleaning up expired reservations: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Convert cart reservations to actual stock reduction (when order is placed)
     */
    public function convertReservationsToSales($user_id = null, $session_id = null) {
        try {
            $this->conn->beginTransaction();
            
            // Get all reservations for this user/session
            $sql = "SELECT product_id, SUM(quantity) as total_quantity 
                    FROM cart_stock_reservations 
                    WHERE expires_at > NOW()";
            
            $params = [];
            
            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params[] = $user_id;
            } else {
                $sql .= " AND session_id = ?";
                $params[] = $session_id;
            }
            
            $sql .= " GROUP BY product_id";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            $reservations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Reduce actual stock for each product
            foreach ($reservations as $reservation) {
                $updateStmt = $this->conn->prepare("UPDATE products SET stock = stock - ? WHERE product_id = ?");
                $updateStmt->execute([$reservation['total_quantity'], $reservation['product_id']]);
            }
            
            // Remove all reservations for this user/session
            $deleteSQL = "DELETE FROM cart_stock_reservations WHERE ";
            if ($user_id) {
                $deleteSQL .= "user_id = ?";
                $deleteParams = [$user_id];
            } else {
                $deleteSQL .= "session_id = ?";
                $deleteParams = [$session_id];
            }
            
            $deleteStmt = $this->conn->prepare($deleteSQL);
            $deleteStmt->execute($deleteParams);
            
            $this->conn->commit();
            return true;
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            error_log("Error converting reservations to sales: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Release all reservations for a user/session (when cart is abandoned)
     */
    public function releaseAllReservations($user_id = null, $session_id = null) {
        try {
            $sql = "DELETE FROM cart_stock_reservations WHERE ";
            
            if ($user_id) {
                $sql .= "user_id = ?";
                $params = [$user_id];
            } else {
                $sql .= "session_id = ?";
                $params = [$session_id];
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return true;
        } catch (Exception $e) {
            error_log("Error releasing all reservations: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * Helper function to get stock reservation instance
 */
function getStockReservation() {
    global $conn;
    return new StockReservation($conn);
}
?>
