<?php
$page = 'users';
$page_title = 'Manajemen Pengguna';

// Pastikan session dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    $_SESSION['alert_type'] = 'danger';
    $_SESSION['alert_message'] = 'Anda harus login sebagai admin untuk mengakses halaman ini';
    header('Location: ../login.php');
    exit;
}

// Include header setelah cek autentikasi
require_once 'includes/header.php';

// CSS khusus untuk halaman users
?>
<style>
    .avatar-circle {
        width: 100px;
        height: 100px;
        background-color: #0d6efd;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .avatar-text {
        color: white;
        font-size: 40px;
        font-weight: bold;
    }
    .detail-item {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    .detail-label {
        color: #6c757d;
        font-weight: 500;
        display: block;
        margin-bottom: 5px;
    }
    .detail-value {
        font-weight: 600;
    }
    .btn-group .btn {
        margin-right: 2px;
    }
    .user-status {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
    .user-status.active {
        background-color: #28a745;
    }
    .user-status.inactive {
        background-color: #dc3545;
    }
    .table-hover tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }
</style>
<?php

// Get filter parameters
$role_filter = isset($_GET['role']) ? $_GET['role'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build query
$params = [];
$whereClause = "WHERE 1=1";

if (!empty($role_filter)) {
    $whereClause .= " AND role = ?";
    $params[] = $role_filter;
}

if (!empty($status_filter)) {
    $whereClause .= " AND status = ?";
    $params[] = $status_filter;
}

if (!empty($search)) {
    $whereClause .= " AND (username LIKE ? OR email LIKE ? OR full_name LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $params[] = $searchParam;
}

try {
    // Get total count for pagination
    $countQuery = "SELECT COUNT(*) FROM users $whereClause";
    $stmt = $conn->prepare($countQuery);
    if (!empty($params)) {
        $stmt->execute($params);
    } else {
        $stmt->execute();
    }
    $totalUsers = $stmt->fetchColumn();
    
    // Pagination
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $usersPerPage = 10;
    $totalPages = ceil($totalUsers / $usersPerPage);
    $page = max(1, min($page, $totalPages));
    $offset = ($page - 1) * $usersPerPage;
    
    // Get users for current page
    $query = "
        SELECT *
        FROM users
        $whereClause
        ORDER BY created_at DESC
        LIMIT $usersPerPage OFFSET $offset
    ";
    
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $stmt->execute($params);
    } else {
        $stmt->execute();
    }
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $_SESSION['alert_message'] = 'Database error: ' . $e->getMessage();
    $_SESSION['alert_type'] = 'danger';
    $users = [];
    $totalPages = 1;
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">User Management</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Users</li>
    </ol>

    <?php if (isset($_SESSION['alert_message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['alert_type']; ?> alert-dismissible fade show" role="alert">
            <?php 
                echo $_SESSION['alert_message']; 
                unset($_SESSION['alert_message']);
                unset($_SESSION['alert_type']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-users me-1"></i>
            Users
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <form class="d-flex" method="GET">
                        <input type="text" name="search" class="form-control me-2" placeholder="Search users..." value="<?php echo htmlspecialchars($search); ?>">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo $user['user_id']; ?></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['full_name'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <?php if ($user['role'] === 'admin'): ?>
                                        <span class="badge bg-primary">Admin</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">User</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (isset($user['is_active']) && $user['is_active']): ?>
                                        <span class="badge bg-success">Aktif</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Nonaktif</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('d M Y H:i', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="#" class="btn btn-sm btn-info view-user" data-bs-toggle="modal" data-bs-target="#viewUserModal" 
                                           data-id="<?php echo $user['user_id']; ?>" 
                                           data-username="<?php echo htmlspecialchars($user['username']); ?>" 
                                           data-fullname="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" 
                                           data-email="<?php echo htmlspecialchars($user['email']); ?>" 
                                           data-role="<?php echo $user['role']; ?>" 
                                           data-status="<?php echo isset($user['is_active']) && $user['is_active'] ? 'active' : 'inactive'; ?>" 
                                           data-created="<?php echo date('d M Y H:i', strtotime($user['created_at'])); ?>">
                                            <i class="fas fa-eye" title="Lihat Detail"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-primary edit-user" data-bs-toggle="modal" data-bs-target="#editUserModal"
                                           data-id="<?php echo $user['user_id']; ?>">
                                            <i class="fas fa-edit" title="Edit"></i>
                                        </a>
                                        <?php if (isset($user['is_active']) && $user['is_active']): ?>
                                            <a href="#" class="btn btn-sm btn-danger toggle-status" data-id="<?php echo $user['user_id']; ?>" data-action="deactivate" data-username="<?php echo htmlspecialchars($user['username']); ?>">
                                                <i class="fas fa-user-times" title="Nonaktifkan"></i>
                                            </a>
                                        <?php else: ?>
                                            <a href="#" class="btn btn-sm btn-success toggle-status" data-id="<?php echo $user['user_id']; ?>" data-action="activate" data-username="<?php echo htmlspecialchars($user['username']); ?>">
                                                <i class="fas fa-user-check" title="Aktifkan"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<!-- View User Modal -->
<div class="modal fade" id="viewUserModal" tabindex="-1" aria-labelledby="viewUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="viewUserModalLabel">Detail Pengguna</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <div class="avatar-circle mx-auto mb-3">
                        <span id="viewUserInitials" class="avatar-text">JD</span>
                    </div>
                    <h4 id="viewUserFullName">John Doe</h4>
                    <p id="viewUserUsername" class="text-muted mb-0">@johndoe</p>
                </div>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-envelope me-2"></i>Email:</span>
                            <span id="viewUserEmail" class="detail-value"><EMAIL></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-user-tag me-2"></i>Role:</span>
                            <span id="viewUserRole" class="detail-value">User</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-check-circle me-2"></i>Status:</span>
                            <span id="viewUserStatus" class="detail-value">Aktif</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="detail-item">
                            <span class="detail-label"><i class="fas fa-calendar-alt me-2"></i>Terdaftar:</span>
                            <span id="viewUserCreated" class="detail-value">01 Jan 2023</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" id="editUserFromView">Edit Pengguna</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editUserModalLabel">Edit Pengguna</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm" method="POST" action="process-user.php">
                    <input type="hidden" name="action" value="update">
                    <input type="hidden" name="user_id" id="editUserId">
                    
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">Username <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editUsername" name="username" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editFullName" class="form-label">Nama Lengkap</label>
                        <input type="text" class="form-control" id="editFullName" name="full_name">
                    </div>
                    
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="editEmail" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editPassword" class="form-label">Password (Biarkan kosong jika tidak diubah)</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="editPassword" name="password">
                            <button class="btn btn-outline-secondary toggle-password" type="button" data-target="editPassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="form-text">Kosongkan jika tidak ingin mengubah password.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editRole" class="form-label">Role</label>
                        <select class="form-select" id="editRole" name="role">
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="editIsActive" name="is_active" value="1">
                        <label class="form-check-label" for="editIsActive">Akun Aktif</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="saveUserChanges">Simpan Perubahan</button>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addUserModalLabel">Tambah Pengguna Baru</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm" method="POST" action="process-user.php">
                    <input type="hidden" name="action" value="add">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="fullname" class="form-label">Nama Lengkap</label>
                        <input type="text" class="form-control" id="fullname" name="full_name">
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" required>
                            <button class="btn btn-outline-secondary toggle-password" type="button" data-target="password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role">
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" checked>
                        <label class="form-check-label" for="is_active">Akun Aktif</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-success" id="saveNewUser">Tambah Pengguna</button>
            </div>
        </div>
    </div>
</div>

<!-- Status Toggle Confirmation Modal -->
<div class="modal fade" id="toggleStatusModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="toggleStatusTitle">Konfirmasi Perubahan Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="toggleStatusBody">
                Apakah Anda yakin ingin mengubah status pengguna ini?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="confirmToggleStatus">Konfirmasi</button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Animation styles */
    .table-responsive {
        overflow-x: hidden;
    }
    
    .table tbody tr {
        transition: all 0.3s ease;
        transform: translateY(0);
    }
    
    .table tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        z-index: 1;
        position: relative;
    }
    
    .btn-group .btn {
        transition: all 0.2s ease;
    }
    
    .btn-group .btn:hover {
        transform: scale(1.1);
        z-index: 5;
    }
    
    .badge {
        transition: all 0.3s ease;
    }
    
    .badge:hover {
        transform: scale(1.2);
    }
    
    .form-control:focus {
        box-shadow: 0 0 8px rgba(13, 110, 253, 0.5);
        border-color: #0d6efd;
        animation: pulse 1s;
    }
    
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.7); }
        70% { box-shadow: 0 0 0 5px rgba(13, 110, 253, 0); }
        100% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0); }
    }
    
    /* Fade in animation for new rows */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .new-row {
        animation: fadeIn 0.5s ease forwards;
    }
    
    /* Buttons animation */
    .btn-add-user {
        transition: all 0.3s ease;
    }
    
    .btn-add-user:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Loading spinner */
    .spinner-grow-sm {
        width: 1rem;
        height: 1rem;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation to search form
        const searchForm = document.querySelector('.d-flex');
        if (searchForm) {
            const searchInput = searchForm.querySelector('input[name="search"]');
            searchInput.addEventListener('focus', function() {
                this.classList.add('shadow-pulse');
                searchForm.classList.add('active-search-form');
            });
            
            searchInput.addEventListener('blur', function() {
                if (this.value === '') {
                    this.classList.remove('shadow-pulse');
                    searchForm.classList.remove('active-search-form');
                }
            });
        }
        
        // Add animation to table rows
        const tableRows = document.querySelectorAll('.table tbody tr');
        tableRows.forEach((row, index) => {
            row.style.opacity = 0;
            setTimeout(() => {
                row.style.opacity = 1;
                row.classList.add('new-row');
            }, index * 50);
        });
        
        // Add animation to status badges
        document.querySelectorAll('.badge').forEach(badge => {
            badge.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.2)';
            });
            
            badge.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
        
        // Add user button animation
        const addButton = document.createElement('button');
        addButton.type = 'button';
        addButton.className = 'btn btn-success btn-add-user ms-2';
        addButton.innerHTML = '<i class="fas fa-user-plus me-2"></i>Tambah Pengguna';
        addButton.setAttribute('data-bs-toggle', 'modal');
        addButton.setAttribute('data-bs-target', '#addUserModal');
        
        // Add the button to the search form container
        document.querySelector('.col-md-6').appendChild(addButton);
        
        // Toggle password visibility
        document.querySelectorAll('.toggle-password').forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const passwordInput = document.getElementById(targetId);
                const icon = this.querySelector('i');
                
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    passwordInput.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
                
                // Add animation
                this.classList.add('btn-pulse');
                setTimeout(() => {
                    this.classList.remove('btn-pulse');
                }, 300);
            });
        });
        
        // Handle user form submission with animation
        // Handle form submissions for add, edit users
        document.getElementById('saveNewUser').addEventListener('click', function() {
            document.getElementById('addUserForm').submit();
        });
        
        document.getElementById('saveUserChanges').addEventListener('click', function() {
            document.getElementById('editUserForm').submit();
        });
        
        // View user detail handler
        document.querySelectorAll('.view-user').forEach(button => {
            button.addEventListener('click', function() {
                // Get user data from data attributes
                const id = this.getAttribute('data-id');
                const username = this.getAttribute('data-username');
                const fullname = this.getAttribute('data-fullname') || username;
                const email = this.getAttribute('data-email');
                const role = this.getAttribute('data-role');
                const status = this.getAttribute('data-status');
                const created = this.getAttribute('data-created');
                
                // Set user data in modal
                document.getElementById('viewUserInitials').textContent = getInitials(fullname);
                document.getElementById('viewUserFullName').textContent = fullname;
                document.getElementById('viewUserUsername').textContent = '@' + username;
                document.getElementById('viewUserEmail').textContent = email;
                document.getElementById('viewUserRole').textContent = role;
                document.getElementById('viewUserStatus').textContent = status === 'active' ? 'Aktif' : 'Nonaktif';
                document.getElementById('viewUserCreated').textContent = created;
                
                // Set edit button link
                document.getElementById('editUserFromView').onclick = function() {
                    const viewModal = bootstrap.Modal.getInstance(document.getElementById('viewUserModal'));
                    viewModal.hide();
                    
                    // Trigger click on the edit button for this user
                    document.querySelector(`.edit-user[data-id="${id}"]`).click();
                };
            });
        });
        
        // Edit user handler
        document.querySelectorAll('.edit-user').forEach(button => {
            button.addEventListener('click', function() {
                const userId = this.getAttribute('data-id');
                
                // AJAX call to get user data
                fetch(`get-user.php?id=${userId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const user = data.user;
                            
                            // Fill form with user data
                            document.getElementById('editUserId').value = user.user_id;
                            document.getElementById('editUsername').value = user.username;
                            document.getElementById('editFullName').value = user.full_name || '';
                            document.getElementById('editEmail').value = user.email;
                            document.getElementById('editRole').value = user.role;
                            document.getElementById('editIsActive').checked = user.status === 'active';
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Gagal mengambil data pengguna');
                    });
            });
        });
        
        // Toggle status handler
        document.querySelectorAll('.toggle-status').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const userId = this.getAttribute('data-id');
                const username = this.getAttribute('data-username');
                const action = this.getAttribute('data-action');
                
                // Set modal content
                const title = document.getElementById('toggleStatusTitle');
                const body = document.getElementById('toggleStatusBody');
                const confirmBtn = document.getElementById('confirmToggleStatus');
                
                if (action === 'activate') {
                    title.textContent = 'Aktifkan Pengguna';
                    body.textContent = `Apakah Anda yakin ingin mengaktifkan pengguna "${username}"?`;
                    confirmBtn.classList.remove('btn-danger');
                    confirmBtn.classList.add('btn-success');
                } else {
                    title.textContent = 'Nonaktifkan Pengguna';
                    body.textContent = `Apakah Anda yakin ingin menonaktifkan pengguna "${username}"?`;
                    confirmBtn.classList.remove('btn-success');
                    confirmBtn.classList.add('btn-danger');
                }
                
                // Set confirm button action
                confirmBtn.onclick = function() {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = 'process-user.php';
                    
                    const inputAction = document.createElement('input');
                    inputAction.type = 'hidden';
                    inputAction.name = 'action';
                    inputAction.value = action;
                    
                    const inputId = document.createElement('input');
                    inputId.type = 'hidden';
                    inputId.name = 'user_id';
                    inputId.value = userId;
                    
                    form.appendChild(inputAction);
                    form.appendChild(inputId);
                    document.body.appendChild(form);
                    form.submit();
                };
            });
        });
        
        // Helper function to get initials from name
        function getInitials(name) {
            if (!name) return 'U';
            
            const parts = name.split(' ');
            if (parts.length === 1) {
                return parts[0].charAt(0).toUpperCase();
            } else {
                return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
            }
        }
            
            // Basic validation
            const requiredFields = form.querySelectorAll('[required]');
            let valid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    valid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!valid) {
                alertBox.textContent = 'Harap isi semua kolom yang wajib diisi';
                alertBox.classList.remove('d-none', 'alert-success');
                alertBox.classList.add('alert-danger');
                
                // Shake the modal
                const modalContent = document.querySelector('.modal-content');
                modalContent.classList.add('shake-animation');
                setTimeout(() => {
                    modalContent.classList.remove('shake-animation');
                }, 500);
                
                return;
            }
            
            // Show loading spinner
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Menyimpan...';
            
            // Submit the form with AJAX
            const formData = new FormData(form);
            
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alertBox.textContent = data.message || 'Pengguna berhasil ditambahkan';
                    alertBox.classList.remove('d-none', 'alert-danger');
                    alertBox.classList.add('alert-success');
                    
                    // Reset form
                    form.reset();
                    
                    // Reload the page after 1.5 seconds
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    // Show error message
                    alertBox.textContent = data.message || 'Terjadi kesalahan';
                    alertBox.classList.remove('d-none', 'alert-success');
                    alertBox.classList.add('alert-danger');
                    
                    // Reset button
                    this.disabled = false;
                    this.innerHTML = '<i class="fas fa-save me-1"></i> Simpan';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alertBox.textContent = 'Terjadi kesalahan jaringan';
                alertBox.classList.remove('d-none', 'alert-success');
                alertBox.classList.add('alert-danger');
                
                // Reset button
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-save me-1"></i> Simpan';
            });
        });
    });
</script>

<?php require_once 'includes/footer.php'; ?>
