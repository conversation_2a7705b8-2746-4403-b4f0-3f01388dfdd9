<?php
/**
 * Admin Authentication Helper
 * File ini menyediakan fungsi untuk autentikasi admin secara ketat
 * dan harus disertakan di semua halaman admin
 */

// Pastikan session dimulai dan hapus cache agar selalu fresh
if (session_status() === PHP_SESSION_NONE) {
    // Set parameter cookie session yang lebih ketat
    session_start([
        'cookie_httponly' => true,     // Mencegah akses via JavaScript
        'cookie_secure' => false,      // Setel ke true jika menggunakan HTTPS
        'use_strict_mode' => true      // Gunakan mode ketat untuk session
    ]);
}

// Pastikan header tidak di-cache
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

// Force cookie regeneration untuk mencegah session fixation
if (!isset($_SESSION['last_regeneration']) || time() - $_SESSION['last_regeneration'] > 1800) {
    session_regenerate_id(true); // Regenerate session ID
    $_SESSION['last_regeneration'] = time();
}

/**
 * Memeriksa apakah pengguna sudah login sebagai admin
 * Jika tidak, redirect ke halaman login
 * @return bool true jika autentikasi berhasil, false jika gagal
 */
function ensureAdminAuthenticated() {
    // Cek session untuk admin role secara ketat
    $isAdmin = false;
    
    if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id']) && 
        isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        $isAdmin = true;
    }
    
    if (!$isAdmin) {
        // Hapus semua session untuk memastikan tidak ada session yang tersisa
        $_SESSION = array();
        
        // Hapus cookie session jika ada
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Destroy session
        session_destroy();
        
        // Mulai session baru untuk pesan error
        session_start();
        
        // Simpan pesan error di session
        $_SESSION['alert_type'] = 'danger';
        $_SESSION['alert_message'] = 'Anda harus login sebagai admin untuk mengakses halaman ini';
        
        // Tentukan URL absolut untuk redirect
        $redirect_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . 
                "://" . $_SERVER['HTTP_HOST'] . "/tewuneed2/login.php";
        
        // Redirect ke halaman login dengan exit setelahnya
        header("Location: $redirect_url");
        exit();
    }
    
    return $isAdmin;
}

// Cek autentikasi admin secara otomatis saat file ini disertakan
// Eksekusi segera
ensureAdminAuthenticated();
