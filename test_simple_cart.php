<?php
// Test simple add to cart
session_start();
require_once 'includes/db_connect.php';

echo "<!DOCTYPE html>\n";
echo "<html><head><title>Test Simple Cart</title></head><body>\n";
echo "<h1>Test Simple Add to Cart</h1>\n";

// Auto-login test user if not logged in
if (!isset($_SESSION['user_id'])) {
    try {
        $stmt = $conn->query("SELECT user_id, username FROM users WHERE email = '<EMAIL>' LIMIT 1");
        $user = $stmt->fetch();
        if (!$user) {
            // Create test user
            $stmt = $conn->prepare("INSERT INTO users (username, password, email, full_name, role, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute(['testuser', password_hash('test123', PASSWORD_DEFAULT), '<EMAIL>', 'Test User', 'customer']);
            $user_id = $conn->lastInsertId();
            $_SESSION['user_id'] = $user_id;
            $_SESSION['username'] = 'testuser';
            echo "<p style='color: green;'>✓ Test user created and logged in (ID: $user_id)</p>\n";
        } else {
            $_SESSION['user_id'] = $user['user_id'];
            $_SESSION['username'] = $user['username'];
            echo "<p style='color: green;'>✓ Test user logged in (ID: {$user['user_id']})</p>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error setting up test user: " . $e->getMessage() . "</p>\n";
    }
}

// Get products for testing
try {
    $stmt = $conn->query("SELECT product_id, name, price, stock FROM products WHERE is_active = 1 LIMIT 5");
    $products = $stmt->fetchAll();
    
    if ($products) {
        echo "<h2>Test Products</h2>\n";
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>\n";
        
        foreach ($products as $product) {
            echo "<div style='border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: #f9f9f9;'>\n";
            echo "<h3>{$product['name']}</h3>\n";
            echo "<p><strong>Price:</strong> Rp " . number_format($product['price'], 0, ',', '.') . "</p>\n";
            echo "<p><strong>Stock:</strong> {$product['stock']}</p>\n";
            echo "<p><strong>Product ID:</strong> {$product['product_id']}</p>\n";
            echo "<button onclick='addToCartSimple({$product['product_id']}, \"{$product['name']}\")' ";
            echo "style='background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px;'>";
            echo "Add to Cart (Simple)</button>\n";
            echo "<button onclick='addToCartOriginal({$product['product_id']}, \"{$product['name']}\")' ";
            echo "style='background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;'>";
            echo "Add to Cart (Original)</button>\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
    } else {
        echo "<p style='color: red;'>✗ No products found</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error getting products: " . $e->getMessage() . "</p>\n";
}

// Show current cart
if (isset($_SESSION['user_id'])) {
    try {
        $stmt = $conn->prepare("
            SELECT ci.*, p.name, p.price, (ci.quantity * p.price) as subtotal
            FROM cart_items ci 
            JOIN carts c ON ci.cart_id = c.cart_id 
            JOIN products p ON ci.product_id = p.product_id 
            WHERE c.user_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $cartItems = $stmt->fetchAll();
        
        echo "<h2>Current Cart</h2>\n";
        if ($cartItems) {
            echo "<table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>\n";
            echo "<tr style='background: #f8f9fa;'>\n";
            echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Product</th>\n";
            echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Price</th>\n";
            echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Quantity</th>\n";
            echo "<th style='border: 1px solid #ddd; padding: 10px; text-align: left;'>Subtotal</th>\n";
            echo "</tr>\n";
            
            $total = 0;
            foreach ($cartItems as $item) {
                $total += $item['subtotal'];
                echo "<tr>\n";
                echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$item['name']}</td>\n";
                echo "<td style='border: 1px solid #ddd; padding: 10px;'>Rp " . number_format($item['price'], 0, ',', '.') . "</td>\n";
                echo "<td style='border: 1px solid #ddd; padding: 10px;'>{$item['quantity']}</td>\n";
                echo "<td style='border: 1px solid #ddd; padding: 10px;'>Rp " . number_format($item['subtotal'], 0, ',', '.') . "</td>\n";
                echo "</tr>\n";
            }
            
            echo "<tr style='background: #e9ecef; font-weight: bold;'>\n";
            echo "<td colspan='3' style='border: 1px solid #ddd; padding: 10px; text-align: right;'>Total:</td>\n";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>Rp " . number_format($total, 0, ',', '.') . "</td>\n";
            echo "</tr>\n";
            echo "</table>\n";
            
            echo "<button onclick='clearCart()' style='background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;'>Clear Cart</button>\n";
        } else {
            echo "<p>Cart is empty</p>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error getting cart: " . $e->getMessage() . "</p>\n";
    }
}

echo "<div style='margin: 30px 0;'>\n";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Back to Website</a>\n";
echo "<a href='debug_add_to_cart.php' style='background: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>Debug Tool</a>\n";
echo "</div>\n";

echo "<script>\n";
echo "function addToCartSimple(productId, productName) {\n";
echo "    console.log('Adding to cart (simple):', productId, productName);\n";
echo "    \n";
echo "    fetch('simple_add_to_cart.php', {\n";
echo "        method: 'POST',\n";
echo "        headers: {\n";
echo "            'Content-Type': 'application/x-www-form-urlencoded',\n";
echo "        },\n";
echo "        body: 'product_id=' + productId + '&quantity=1'\n";
echo "    })\n";
echo "    .then(response => response.text())\n";
echo "    .then(text => {\n";
echo "        console.log('Simple cart response:', text);\n";
echo "        try {\n";
echo "            const data = JSON.parse(text);\n";
echo "            if (data.success) {\n";
echo "                alert('✓ Simple: ' + data.message);\n";
echo "                location.reload();\n";
echo "            } else {\n";
echo "                alert('✗ Simple Error: ' + data.message);\n";
echo "                if (data.debug) console.error('Debug:', data.debug);\n";
echo "            }\n";
echo "        } catch (e) {\n";
echo "            console.error('JSON parse error:', e);\n";
echo "            alert('Simple: Server returned non-JSON response');\n";
echo "        }\n";
echo "    })\n";
echo "    .catch(error => {\n";
echo "        console.error('Simple cart error:', error);\n";
echo "        alert('Simple: Network error');\n";
echo "    });\n";
echo "}\n";
echo "\n";
echo "function addToCartOriginal(productId, productName) {\n";
echo "    console.log('Adding to cart (original):', productId, productName);\n";
echo "    \n";
echo "    fetch('ajax/add_to_cart.php', {\n";
echo "        method: 'POST',\n";
echo "        headers: {\n";
echo "            'Content-Type': 'application/x-www-form-urlencoded',\n";
echo "        },\n";
echo "        body: 'product_id=' + productId + '&quantity=1'\n";
echo "    })\n";
echo "    .then(response => response.text())\n";
echo "    .then(text => {\n";
echo "        console.log('Original cart response:', text);\n";
echo "        try {\n";
echo "            const data = JSON.parse(text);\n";
echo "            if (data.success) {\n";
echo "                alert('✓ Original: ' + data.message);\n";
echo "                location.reload();\n";
echo "            } else {\n";
echo "                alert('✗ Original Error: ' + data.message);\n";
echo "            }\n";
echo "        } catch (e) {\n";
echo "            console.error('JSON parse error:', e);\n";
echo "            alert('Original: Server returned non-JSON response');\n";
echo "        }\n";
echo "    })\n";
echo "    .catch(error => {\n";
echo "        console.error('Original cart error:', error);\n";
echo "        alert('Original: Network error');\n";
echo "    });\n";
echo "}\n";
echo "\n";
echo "function clearCart() {\n";
echo "    if (confirm('Are you sure you want to clear the cart?')) {\n";
echo "        fetch('clear_cart.php', { method: 'POST' })\n";
echo "        .then(response => response.json())\n";
echo "        .then(data => {\n";
echo "            if (data.success) {\n";
echo "                alert('Cart cleared successfully');\n";
echo "                location.reload();\n";
echo "            } else {\n";
echo "                alert('Error clearing cart: ' + data.message);\n";
echo "            }\n";
echo "        })\n";
echo "        .catch(error => {\n";
echo "            console.error('Clear cart error:', error);\n";
echo "            alert('Error clearing cart');\n";
echo "        });\n";
echo "    }\n";
echo "}\n";
echo "</script>\n";

echo "</body></html>\n";
?>
