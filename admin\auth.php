<?php
// session_start(); // Sudah aktif di file lain
require_once __DIR__ . '/../includes/auth_helper.php';
require_once __DIR__ . '/../includes/functions.php';

// Ensure session is properly initialized
ensureSession();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: admin_login.php');
    exit();
}

// Set admin_logged_in flag
$_SESSION['admin_logged_in'] = true;
$_SESSION['last_activity'] = time();

// Function to check admin status
function checkAdmin() {
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        header('Location: admin_login.php');
        exit();
    }
}

// Automatically check admin status for all direct accesses to this file
if (basename($_SERVER['PHP_SELF']) === 'auth.php') {
    // Generate CSRF token for security
    generateAdminCSRFToken();
    // Only process login POST requests at the login page
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['username']) && isset($_POST['password'])) {
        // This should not happen when accessing auth.php directly
        // Redirect to login page
        header('Location: admin_login.php');
        exit;
    }
    // If direct access, redirect to login
    checkAdmin();
    header('Location: admin_login.php');
    exit;
}
?>