<?php
header('Content-Type: application/json');
session_start();

try {
    // Clear all session data
    session_unset();
    session_destroy();
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Successfully logged out'
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 