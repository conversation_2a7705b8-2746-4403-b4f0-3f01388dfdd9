<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

try {
    // Get user orders
    $stmt = $conn->prepare("
        SELECT o.*,
               COUNT(oi.order_item_id) as total_items
        FROM orders o
        LEFT JOIN order_items oi ON o.order_id = oi.order_id
        WHERE o.user_id = ?
        GROUP BY o.order_id
        ORDER BY o.order_date DESC
        LIMIT 10
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format orders for display
    $formatted_orders = [];
    foreach ($orders as $order) {
        $formatted_orders[] = [
            'order_id' => $order['order_id'],
            'order_number' => $order['order_number'] ?? $order['order_id'],
            'created_at' => $order['order_date'],
            'status' => $order['status'] ?? $order['order_status'] ?? 'pending',
            'total_amount' => $order['total_amount'],
            'total_items' => $order['total_items']
        ];
    }

    echo json_encode([
        'success' => true,
        'orders' => $formatted_orders
    ]);

} catch (Exception $e) {
    error_log('Error getting user orders: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error loading orders: ' . $e->getMessage()
    ]);
}
?>
