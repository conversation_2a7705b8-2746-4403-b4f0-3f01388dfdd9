<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set response header to JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Parse JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Log request data for debugging
error_log('Update Cart Request: ' . $input);

// Check if data is valid
if (!isset($data['product_id']) || !isset($data['quantity'])) {
    $response['message'] = 'Missing required parameters';
    echo json_encode($response);
    exit;
}

$product_id = intval($data['product_id']);
$quantity = max(1, intval($data['quantity']));

try {
    // Check if cart exists
    if (!isset($_SESSION['cart']) || !is_array($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }

    // Find product in cart and update quantity
    $found = false;
    
    foreach ($_SESSION['cart'] as &$item) {
        if ($item['product_id'] == $product_id) {
            $item['quantity'] = $quantity;
            $found = true;
            break;
        }
    }
    
    if (!$found) {
        $response['message'] = 'Product not found in cart';
    } else {
        $response['success'] = true;
        $response['message'] = 'Cart updated successfully';
        
        // Get updated cart count
        $cart_count = 0;
        foreach ($_SESSION['cart'] as $item) {
            $cart_count += isset($item['quantity']) ? $item['quantity'] : 1;
        }
        
        $response['data'] = [
            'cart_count' => $cart_count
        ];
    }
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    error_log('Update cart error: ' . $e->getMessage());
}

// Return JSON response
echo json_encode($response);
