document.addEventListener('DOMContentLoaded', () => {
    // Get elements
    const quantityInput = document.getElementById('quantity');
    const decreaseBtn = document.getElementById('decrease-quantity');
    const increaseBtn = document.getElementById('increase-quantity');
    const addToCartBtn = document.querySelector('.add-to-cart');
    const alertContainer = document.getElementById('alert-container');
    const spinnerElement = document.getElementById('spinner');

    // Helper functions
    function showSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'block';
        }
    }

    function hideSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'none';
        }
    }

    function showAlert(message, type = 'success') {
        if (!alertContainer) return;
        
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        alertContainer.innerHTML = alertHtml;
        
        // Automatically dismiss alert after 5 seconds
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    function updateCartCount(count) {
        const cartCountElement = document.getElementById('cart-count');
        if (cartCountElement) {
            cartCountElement.textContent = count;
        }
    }

    if (quantityInput && decreaseBtn && increaseBtn) {
        // Update quantity
        decreaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            if (value > 1) {
                quantityInput.value = value - 1;
            }
        });

        increaseBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            if (value < max) {
                quantityInput.value = value + 1;
            }
        });

        // Validate quantity input
        quantityInput.addEventListener('change', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            if (isNaN(value) || value < 1) {
                quantityInput.value = 1;
            } else if (value > max) {
                quantityInput.value = max;
            }
        });
    }

    // Add to cart
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const quantity = parseInt(quantityInput?.value || 1);
            
            // Disable button while processing
            this.disabled = true;
            const oldText = this.textContent;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Adding...';
            
            showSpinner();
            
            // Send AJAX request to add to cart
            fetch('../ajax/cart.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=add&product_id=${productId}&quantity=${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                hideSpinner();
                
                // Show alert
                showAlert(data.message, data.success ? 'success' : 'danger');
                
                // Update cart count if successful
                if (data.success && data.cart_count !== undefined) {
                    updateCartCount(data.cart_count);
                    
                    // Change button text temporarily
                    this.textContent = 'Added!';
                    setTimeout(() => {
                        this.disabled = false;
                        this.textContent = oldText;
                    }, 2000);
                } else {
                    this.disabled = false;
                    this.textContent = oldText;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                hideSpinner();
                this.disabled = false;
                this.textContent = oldText;
                
                // Show error alert
                showAlert('Error adding product to cart. Please try again.', 'danger');
            });
        });
    }
});
