<?php
require_once '../includes/db_connect.php';
require_once '../includes/admin_auth.php';

$page_title = 'Category Images Management';

// Handle image upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_image'])) {
    $category_id = $_POST['category_id'];
    
    if (isset($_FILES['category_image']) && $_FILES['category_image']['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB
        
        if (!in_array($_FILES['category_image']['type'], $allowed_types)) {
            $error = "Only JPG, PNG, and GIF files are allowed.";
        } elseif ($_FILES['category_image']['size'] > $max_size) {
            $error = "File size must be less than 2MB.";
        } else {
            // Create upload directory
            $upload_dir = '../assets/img/categories/';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // Generate filename
            $extension = pathinfo($_FILES['category_image']['name'], PATHINFO_EXTENSION);
            $filename = 'category_' . $category_id . '_' . time() . '.' . $extension;
            $filepath = $upload_dir . $filename;
            
            if (move_uploaded_file($_FILES['category_image']['tmp_name'], $filepath)) {
                // Update database
                $stmt = $conn->prepare("UPDATE categories SET image = ? WHERE category_id = ?");
                if ($stmt->execute([$filename, $category_id])) {
                    $success = "Image uploaded successfully!";
                } else {
                    $error = "Failed to update database.";
                }
            } else {
                $error = "Failed to upload image.";
            }
        }
    } else {
        $error = "Please select an image file.";
    }
}

// Get all categories
$stmt = $conn->query("SELECT * FROM categories ORDER BY NAME");
$categories = $stmt->fetchAll();

include '../includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-images me-2"></i>Category Images Management</h2>
        <a href="categories.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Categories
        </a>
    </div>

    <?php if (isset($success)): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <?php echo $success; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <?php echo $error; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <?php foreach ($categories as $category): ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><?php echo htmlspecialchars($category['NAME']); ?></h5>
                </div>
                <div class="card-body text-center">
                    <?php 
                    $image_path = '../assets/img/categories/' . ($category['image'] ?: 'category-default.jpg');
                    $web_path = 'assets/img/categories/' . ($category['image'] ?: 'category-default.jpg');
                    ?>
                    
                    <div class="mb-3">
                        <img src="../<?php echo $web_path; ?>" 
                             class="img-fluid rounded" 
                             style="max-height: 200px; width: 100%; object-fit: cover;" 
                             alt="<?php echo htmlspecialchars($category['NAME']); ?>"
                             onerror="this.src='../assets/img/categories/category-default.jpg'">
                    </div>
                    
                    <p class="text-muted small">
                        Current: <?php echo $category['image'] ?: 'No image'; ?>
                    </p>
                    
                    <form method="POST" enctype="multipart/form-data" class="mt-3">
                        <input type="hidden" name="category_id" value="<?php echo $category['category_id']; ?>">
                        
                        <div class="mb-3">
                            <input type="file" 
                                   class="form-control" 
                                   name="category_image" 
                                   accept="image/*" 
                                   required>
                        </div>
                        
                        <button type="submit" name="upload_image" class="btn btn-primary btn-sm">
                            <i class="fas fa-upload me-1"></i>Upload Image
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    
    <div class="card mt-4">
        <div class="card-header">
            <h5><i class="fas fa-info-circle me-2"></i>Instructions</h5>
        </div>
        <div class="card-body">
            <ul>
                <li>Recommended image size: 400x300 pixels</li>
                <li>Supported formats: JPG, PNG, GIF</li>
                <li>Maximum file size: 2MB</li>
                <li>Images will be displayed with overlay text on the homepage</li>
            </ul>
        </div>
    </div>
</div>

<script>
// Preview image before upload
document.querySelectorAll('input[type="file"]').forEach(input => {
    input.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = input.closest('.card-body').querySelector('img');
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>

<?php include '../includes/admin_footer.php'; ?>
