<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    
    // Get form data
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validate required fields
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        echo json_encode(['success' => false, 'message' => 'All password fields are required']);
        exit;
    }
    
    // Check if new passwords match
    if ($new_password !== $confirm_password) {
        echo json_encode(['success' => false, 'message' => 'New passwords do not match']);
        exit;
    }
    
    // Validate new password strength
    if (strlen($new_password) < 8) {
        echo json_encode(['success' => false, 'message' => 'New password must be at least 8 characters long']);
        exit;
    }
    
    // Check if new password contains at least one letter and one number
    if (!preg_match('/^(?=.*[A-Za-z])(?=.*\d)/', $new_password)) {
        echo json_encode(['success' => false, 'message' => 'New password must contain at least one letter and one number']);
        exit;
    }
    
    // Check if new password is different from current password
    if ($current_password === $new_password) {
        echo json_encode(['success' => false, 'message' => 'New password must be different from current password']);
        exit;
    }
    
    // Get current password hash from database
    $stmt = $conn->prepare("SELECT password FROM users WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Verify current password
    if (!password_verify($current_password, $user['password'])) {
        echo json_encode(['success' => false, 'message' => 'Current password is incorrect']);
        exit;
    }
    
    // Hash new password
    $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Update password in database
    $stmt = $conn->prepare("
        UPDATE users 
        SET password = ?, password_changed_at = NOW(), updated_at = NOW()
        WHERE user_id = ?
    ");
    
    $result = $stmt->execute([$new_password_hash, $user_id]);
    
    if ($result) {
        // Log password change activity
        $activity_stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, description, ip_address, user_agent, created_at) 
            VALUES (?, 'password_change', 'Password changed successfully', ?, ?, NOW())
        ");
        $activity_stmt->execute([
            $user_id,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        
        // Optional: Invalidate all other sessions (force re-login on other devices)
        // This would require a sessions table to track active sessions
        
        echo json_encode([
            'success' => true, 
            'message' => 'Password changed successfully'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to change password']);
    }
    
} catch (Exception $e) {
    error_log("Password change error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while changing password']);
}
?>
