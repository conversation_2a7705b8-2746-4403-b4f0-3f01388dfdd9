<?php
// Start output buffering to prevent any unwanted output
ob_start();

session_start();
require_once '../../includes/db_connect.php';

// Clear any previous output
ob_clean();

// Set proper headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    ob_clean();
    echo json_encode(['success' => false, 'message' => 'Unauthorized access', 'debug' => 'Not admin user']);
    exit;
}

try {
    // Get JSON input
    $raw_input = file_get_contents('php://input');
    $input = json_decode($raw_input, true);

    // Log input for debugging
    error_log("AJAX Input - Raw: " . $raw_input);
    error_log("AJAX Input - Decoded: " . print_r($input, true));
    error_log("AJAX Input - POST: " . print_r($_POST, true));

    if (!$input) {
        // Fallback to POST data
        $input = $_POST;
    }

    if (empty($input)) {
        throw new Exception('No input data received');
    }

    $order_id = $input['order_id'] ?? '';
    $new_status = $input['order_status'] ?? '';
    $admin_note = $input['admin_note'] ?? '';
    $notify_customer = $input['notify_customer'] ?? true;

    if (empty($order_id) || empty($new_status)) {
        throw new Exception('Missing required fields: order_id and order_status. Received: ' . json_encode($input));
    }

    // Standardize status values for consistency
    $status_mapping = [
        // Indonesian to English mapping
        'dibuat' => 'pending',
        'diproses' => 'processing',
        'dikirim' => 'shipped',
        'terkirim' => 'delivered',
        'dibatalkan' => 'cancelled',
        // English values (keep as is)
        'pending' => 'pending',
        'processing' => 'processing',
        'shipped' => 'shipped',
        'delivered' => 'delivered',
        'cancelled' => 'cancelled',
        // Additional mappings
        'paid' => 'processing'
    ];

    $standardized_status = $status_mapping[$new_status] ?? $new_status;
    error_log("Status standardization: $new_status -> $standardized_status");
    
    // Get current order status - check multiple possible column names
    $stmt = $conn->prepare("SELECT
        COALESCE(order_status, STATUS, 'pending') as current_status,
        user_id,
        order_number
        FROM orders WHERE order_id = ?");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();

    if (!$order) {
        throw new Exception('Order not found');
    }

    $old_status = $order['current_status'];
    $user_id = $order['user_id'];
    $order_number = $order['order_number'] ?? $order_id;

    // Update order status - handle both order_status and STATUS columns
    $update_success = false;
    $update_errors = [];

    // Try to update both columns if they exist
    try {
        // Check which columns exist
        $stmt = $conn->prepare("SHOW COLUMNS FROM orders LIKE 'order_status'");
        $stmt->execute();
        $has_order_status = $stmt->rowCount() > 0;

        $stmt = $conn->prepare("SHOW COLUMNS FROM orders LIKE 'STATUS'");
        $stmt->execute();
        $has_status = $stmt->rowCount() > 0;

        error_log("Column check - order_status: " . ($has_order_status ? 'exists' : 'missing') . ", STATUS: " . ($has_status ? 'exists' : 'missing'));

        // Start transaction for atomic updates
        $conn->beginTransaction();

        // Update order_status column if it exists
        if ($has_order_status) {
            try {
                $stmt = $conn->prepare("UPDATE orders SET order_status = ?, updated_at = NOW() WHERE order_id = ?");
                $result1 = $stmt->execute([$standardized_status, $order_id]);
                if ($result1) {
                    $update_success = true;
                    error_log("Successfully updated order_status column to: $standardized_status");
                }
            } catch (Exception $e) {
                $update_errors[] = "order_status update failed: " . $e->getMessage();
            }
        }

        // Update STATUS column if it exists
        if ($has_status) {
            try {
                $stmt = $conn->prepare("UPDATE orders SET STATUS = ?, updated_at = NOW() WHERE order_id = ?");
                $result2 = $stmt->execute([$standardized_status, $order_id]);
                if ($result2) {
                    $update_success = true;
                    error_log("Successfully updated STATUS column to: $standardized_status");
                }
            } catch (Exception $e) {
                $update_errors[] = "STATUS update failed: " . $e->getMessage();
            }
        }

        // Commit transaction if successful
        if ($update_success) {
            $conn->commit();
        } else {
            $conn->rollback();
        }

        // If neither column exists, try a generic update
        if (!$has_order_status && !$has_status) {
            throw new Exception("Neither order_status nor STATUS column found in orders table");
        }

    } catch (Exception $e) {
        $update_errors[] = "Column check failed: " . $e->getMessage();
    }
    
    if (!$update_success) {
        $error_message = 'Failed to update order status';
        if (!empty($update_errors)) {
            $error_message .= ': ' . implode(', ', $update_errors);
        }
        throw new Exception($error_message);
    }
    
    // Add to order history if table exists
    try {
        // Try different history table structures
        $admin_id = $_SESSION['user_id'] ?? null;
        $admin_name = $_SESSION['full_name'] ?? $_SESSION['username'] ?? 'Admin';

        try {
            // Try with admin_name column
            $stmt = $conn->prepare("INSERT INTO order_status_history (order_id, old_status, new_status, notes, admin_name, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$order_id, $old_status, $standardized_status, $admin_note, $admin_name]);
        } catch (Exception $e1) {
            try {
                // Try with admin_id column
                $stmt = $conn->prepare("INSERT INTO order_status_history (order_id, old_status, new_status, notes, admin_id, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
                $stmt->execute([$order_id, $old_status, $standardized_status, $admin_note, $admin_id]);
            } catch (Exception $e2) {
                try {
                    // Try with previous_status and new_status columns
                    $stmt = $conn->prepare("INSERT INTO order_status_history (order_id, previous_status, new_status, notes, admin_id, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
                    $stmt->execute([$order_id, $old_status, $standardized_status, $admin_note, $admin_id]);
                } catch (Exception $e3) {
                    // Try simple structure
                    $stmt = $conn->prepare("INSERT INTO order_status_history (order_id, STATUS, notes, created_at) VALUES (?, ?, ?, NOW())");
                    $stmt->execute([$order_id, $standardized_status, $admin_note]);
                }
            }
        }
    } catch (Exception $historyError) {
        // History table might not exist, continue anyway
        error_log("Order history insert failed: " . $historyError->getMessage());
    }
    
    // Create status configuration for response
    $status_config = [
        'pending' => ['label' => 'Pending', 'class' => 'btn-warning', 'bg_class' => 'bg-warning', 'icon' => 'fa-clock'],
        'diproses' => ['label' => 'Processing', 'class' => 'btn-info', 'bg_class' => 'bg-info', 'icon' => 'fa-cog'],
        'processing' => ['label' => 'Processing', 'class' => 'btn-info', 'bg_class' => 'bg-info', 'icon' => 'fa-cog'],
        'dikirim' => ['label' => 'Shipped', 'class' => 'btn-primary', 'bg_class' => 'bg-primary', 'icon' => 'fa-truck'],
        'shipped' => ['label' => 'Shipped', 'class' => 'btn-primary', 'bg_class' => 'bg-primary', 'icon' => 'fa-truck'],
        'terkirim' => ['label' => 'Delivered', 'class' => 'btn-success', 'bg_class' => 'bg-success', 'icon' => 'fa-check-circle'],
        'delivered' => ['label' => 'Delivered', 'class' => 'btn-success', 'bg_class' => 'bg-success', 'icon' => 'fa-check-circle'],
        'dibatalkan' => ['label' => 'Cancelled', 'class' => 'btn-danger', 'bg_class' => 'bg-danger', 'icon' => 'fa-times-circle'],
        'cancelled' => ['label' => 'Cancelled', 'class' => 'btn-danger', 'bg_class' => 'bg-danger', 'icon' => 'fa-times-circle']
    ];

    $config = $status_config[$new_status] ?? [
        'label' => ucfirst($new_status),
        'class' => 'btn-secondary',
        'bg_class' => 'bg-secondary',
        'icon' => 'fa-question-circle'
    ];
    
    // Prepare customer notification data with standardized status
    $customer_notification = [
        'order_id' => $order_id,
        'order_number' => $order_number,
        'old_status' => $old_status,
        'new_status' => $standardized_status,
        'original_status' => $new_status, // Keep original for reference
        'admin_note' => $admin_note,
        'admin_name' => $_SESSION['full_name'] ?? $_SESSION['username'] ?? 'Admin',
        'timestamp' => time() * 1000, // JavaScript timestamp
        'source' => 'admin_update',
        'processed' => false
    ];
    
    // Store in localStorage sync format for immediate user panel updates
    if ($notify_customer) {
        // This will be picked up by the response handler to update localStorage
        $sync_data = [
            $order_id => $customer_notification
        ];
    }
    
    // Prepare response
    $response = [
        'success' => true,
        'message' => 'Order status updated successfully',
        'order_id' => $order_id,
        'old_status' => $old_status,
        'new_status' => $standardized_status,
        'original_status' => $new_status,
        'status_label' => $config['label'],
        'status_class' => $config['class'],
        'status_bg_class' => $config['bg_class'],
        'status_icon' => $config['icon'],
        'badge_html' => "<i class='fas {$config['icon']} me-1'></i>{$config['label']}",
        'button_html' => "<i class='fas {$config['icon']} me-1'></i>{$config['label']}",
        'customer_notification' => $customer_notification,
        'timestamp' => date('Y-m-d H:i:s'),
        'debug' => [
            'config_used' => $config,
            'status_input' => $new_status,
            'old_status' => $old_status,
            'update_success' => $update_success,
            'update_errors' => $update_errors,
            'columns_checked' => [
                'has_order_status' => $has_order_status ?? false,
                'has_status' => $has_status ?? false
            ]
        ]
    ];
    
    if (isset($sync_data)) {
        $response['sync_data'] = $sync_data;
    }
    
    // Clear any output and send JSON response
    ob_clean();
    echo json_encode($response, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // Clear any output and send error response
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error_code' => $e->getCode(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ], JSON_UNESCAPED_UNICODE);
} catch (Error $e) {
    // Handle fatal errors
    ob_clean();
    echo json_encode([
        'success' => false,
        'message' => 'Fatal error: ' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ], JSON_UNESCAPED_UNICODE);
}

// End output buffering and flush
ob_end_flush();
?>
