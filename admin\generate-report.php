<?php
/**
 * Order Report Generator
 * 
 * This file generates order reports based on the provided filters.
 * Supports PDF, Excel, and CSV formats.
 */

session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';
require_once '../includes/order_status_helper.php';

// Check if user is authorized
if (!isset($_SESSION['user_id']) || !isAdmin($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Set default values if not provided
$format = isset($_GET['format']) ? $_GET['format'] : 'pdf';
$status = isset($_POST['report_status']) ? $_POST['report_status'] : '';
$payment_status = isset($_POST['report_payment']) ? $_POST['report_payment'] : '';
$date_from = isset($_POST['report_date_from']) ? $_POST['report_date_from'] : '';
$date_to = isset($_POST['report_date_to']) ? $_POST['report_date_to'] : '';

// Includes options
$include_customer = isset($_POST['include_customer']) && $_POST['include_customer'] == '1';
$include_shipping = isset($_POST['include_shipping']) && $_POST['include_shipping'] == '1';
$include_payment = isset($_POST['include_payment']) && $_POST['include_payment'] == '1';
$include_items = isset($_POST['include_items']) && $_POST['include_items'] == '1';
$include_status = isset($_POST['include_status']) && $_POST['include_status'] == '1';
$include_summary = isset($_POST['include_summary']) && $_POST['include_summary'] == '1';

// Build the query to get order data
$query = "
    SELECT o.*, u.username as user_name, u.full_name, u.email, u.phone_number as phone
    FROM orders o
    LEFT JOIN users u ON o.user_id = u.user_id
    WHERE 1=1
";

$params = [];

if (!empty($status)) {
    $query .= " AND o.order_status = ?";
    $params[] = $status;
}

if (!empty($payment_status)) {
    $query .= " AND o.payment_status = ?";
    $params[] = $payment_status;
}

if (!empty($date_from)) {
    $query .= " AND DATE(o.order_date) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $query .= " AND DATE(o.order_date) <= ?";
    $params[] = $date_to;
}

$query .= " ORDER BY o.order_date DESC";

// Get orders
try {
    $conn = getDbConnection();
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($orders) == 0) {
        // No orders found, redirect back with error
        $_SESSION['alert_message'] = 'No orders found for the selected criteria';
        $_SESSION['alert_type'] = 'warning';
        header('Location: order_management.php');
        exit;
    }
    
    // Get order items if requested
    if ($include_items) {
        foreach ($orders as $key => $order) {
            $stmt = $conn->prepare("
                SELECT oi.*, p.NAME as product_name, p.slug as sku
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.product_id
                WHERE oi.order_id = ?
            ");
            $stmt->execute([$order['order_id']]);
            $orders[$key]['items'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
    
    // Get status history if requested
    if ($include_status) {
        foreach ($orders as $key => $order) {
            try {
                $stmt = $conn->prepare("
                    SELECT osh.*, a.username as admin_username 
                    FROM order_status_history osh
                    LEFT JOIN users a ON osh.admin_id = a.user_id
                    WHERE osh.order_id = ?
                    ORDER BY osh.created_at DESC
                ");
                $stmt->execute([$order['order_id']]);
                $orders[$key]['status_history'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                // Table might not exist, set empty array
                $orders[$key]['status_history'] = [];
            }
        }
    }
    
    // Calculate summary statistics
    $summary = [
        'total_orders' => count($orders),
        'total_revenue' => 0,
        'status_counts' => [
            'dibuat' => 0,
            'diproses' => 0,
            'dikirim' => 0,
            'terkirim' => 0,
            'dibatalkan' => 0
        ],
        'payment_counts' => [
            'pending' => 0,
            'paid' => 0,
            'failed' => 0,
            'refunded' => 0
        ]
    ];
    
    foreach ($orders as $order) {
        // Add to total revenue
        $summary['total_revenue'] += (float)($order['total_amount'] ?? 0);
        
        // Count by status
        $status = $order['order_status'] ?? $order['status'] ?? 'dibuat';
        if (isset($summary['status_counts'][$status])) {
            $summary['status_counts'][$status]++;
        }
        
        // Count by payment status
        $payment_status = $order['payment_status'] ?? 'pending';
        if (isset($summary['payment_counts'][$payment_status])) {
            $summary['payment_counts'][$payment_status]++;
        }
    }
    
    // Generate file based on format
    switch ($format) {
        case 'pdf':
            generatePdfReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary);
            break;
            
        case 'excel':
            generateExcelReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary);
            break;
            
        case 'csv':
            generateCsvReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary);
            break;
            
        default:
            $_SESSION['alert_message'] = 'Invalid report format';
            $_SESSION['alert_type'] = 'danger';
            header('Location: order_management.php');
            exit;
    }
    
} catch (PDOException $e) {
    $_SESSION['alert_message'] = 'Database error: ' . $e->getMessage();
    $_SESSION['alert_type'] = 'danger';
    header('Location: order_management.php');
    exit;
}

/**
 * Generate PDF Report
 */
function generatePdfReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary) {
    // Check if TCPDF library is available
    if (!file_exists('../vendor/tcpdf/tcpdf.php')) {
        // If not available, use a simple HTML to PDF approach
        header('Content-Type: text/html; charset=utf-8');
        echo generateHtmlReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary);
        echo '<script>window.print();</script>';
        exit;
    }
    
    // Use TCPDF if available
    require_once('../vendor/tcpdf/tcpdf.php');
    
    // Create new PDF document
    $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
    
    // Set document information
    $pdf->SetCreator('Tewuneed Order System');
    $pdf->SetAuthor('Admin');
    $pdf->SetTitle('Order Report');
    $pdf->SetSubject('Orders Report');
    
    // Set default header data
    $pdf->SetHeaderData('', 0, 'Order Report', 'Generated on ' . date('Y-m-d H:i:s'));
    
    // Set margins
    $pdf->SetMargins(15, 15, 15);
    $pdf->SetHeaderMargin(10);
    $pdf->SetFooterMargin(10);
    
    // Set auto page breaks
    $pdf->SetAutoPageBreak(TRUE, 15);
    
    // Add a page
    $pdf->AddPage();
    
    // Write the HTML content
    $html = generateHtmlReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary);
    $pdf->writeHTML($html, true, false, true, false, '');
    
    // Close and output PDF
    $pdf->Output('order_report_' . date('Y-m-d') . '.pdf', 'D');
    exit;
}

/**
 * Generate Excel Report
 */
function generateExcelReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary) {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="order_report_' . date('Y-m-d') . '.xls"');
    header('Cache-Control: max-age=0');
    
    // Output the HTML table
    echo '<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8" /></head><body>';
    echo generateHtmlReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary, true);
    echo '</body></html>';
    exit;
}

/**
 * Generate CSV Report
 */
function generateCsvReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary) {
    // Set headers for CSV download
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment;filename="order_report_' . date('Y-m-d') . '.csv"');
    
    // Create a file pointer connected to the output stream
    $output = fopen('php://output', 'w');
    
    // Add UTF-8 BOM
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Define the column headers
    $headers = ['Order ID', 'Order Number', 'Date', 'Status', 'Total Amount'];
    
    if ($include_customer) {
        $headers = array_merge($headers, ['Customer Name', 'Email', 'Phone']);
    }
    
    if ($include_shipping) {
        $headers = array_merge($headers, ['Shipping Name', 'Shipping Address', 'Shipping Method', 'Shipping Cost']);
    }
    
    if ($include_payment) {
        $headers = array_merge($headers, ['Payment Method', 'Payment Status']);
    }
    
    // Output the column headers
    fputcsv($output, $headers);
    
    // Output each row of data
    foreach ($orders as $order) {
        $row = [
            $order['order_id'],
            $order['order_number'] ?? $order['order_id'],
            isset($order['order_date']) ? date('Y-m-d H:i', strtotime($order['order_date'])) : 'N/A',
            getStatusLabel($order['order_status'] ?? $order['status'] ?? 'dibuat'),
            isset($order['total_amount']) ? number_format($order['total_amount'], 0, ',', '.') : '0'
        ];
        
        if ($include_customer) {
            $row = array_merge($row, [
                $order['full_name'] ?? $order['user_name'] ?? 'N/A',
                $order['email'] ?? 'N/A',
                $order['phone'] ?? 'N/A'
            ]);
        }
        
        if ($include_shipping) {
            $row = array_merge($row, [
                $order['shipping_name'] ?? 'N/A',
                $order['shipping_address'] ?? 'N/A',
                $order['shipping_method'] ?? 'N/A',
                isset($order['shipping_cost']) ? number_format($order['shipping_cost'], 0, ',', '.') : '0'
            ]);
        }
        
        if ($include_payment) {
            $row = array_merge($row, [
                $order['payment_method'] ?? 'N/A',
                $order['payment_status'] ?? 'N/A'
            ]);
        }
        
        fputcsv($output, $row);
        
        // If including items, add a row for each item
        if ($include_items && isset($order['items']) && count($order['items']) > 0) {
            // Add a header row for items
            fputcsv($output, ['', 'Product ID', 'Product Name', 'SKU', 'Quantity', 'Price', 'Subtotal']);
            
            foreach ($order['items'] as $item) {
                $itemRow = [
                    '',
                    $item['product_id'],
                    $item['product_name'] ?? 'N/A',
                    $item['sku'] ?? 'N/A',
                    $item['quantity'],
                    isset($item['price']) ? number_format($item['price'], 0, ',', '.') : '0',
                    isset($item['price']) && isset($item['quantity']) 
                        ? number_format($item['price'] * $item['quantity'], 0, ',', '.') 
                        : '0'
                ];
                fputcsv($output, $itemRow);
            }
            
            // Add an empty row for spacing
            fputcsv($output, ['']);
        }
    }
    
    // If including summary, add summary information
    if ($include_summary) {
        // Add a blank row
        fputcsv($output, ['']);
        fputcsv($output, ['Summary']);
        fputcsv($output, ['Total Orders', $summary['total_orders']]);
        fputcsv($output, ['Total Revenue', 'Rp ' . number_format($summary['total_revenue'], 0, ',', '.')]);
        
        // Status breakdown
        fputcsv($output, ['']);
        fputcsv($output, ['Status Breakdown']);
        foreach ($summary['status_counts'] as $status => $count) {
            fputcsv($output, [getStatusLabel($status), $count]);
        }
        
        // Payment breakdown
        fputcsv($output, ['']);
        fputcsv($output, ['Payment Status Breakdown']);
        foreach ($summary['payment_counts'] as $status => $count) {
            fputcsv($output, [ucfirst($status), $count]);
        }
    }
    
    fclose($output);
    exit;
}

/**
 * Generate HTML Report (used for PDF and Excel)
 */
function generateHtmlReport($orders, $summary, $include_customer, $include_shipping, $include_payment, $include_items, $include_status, $include_summary, $for_excel = false) {
    $html = '<h1>Order Report</h1>';
    $html .= '<p>Generated on ' . date('Y-m-d H:i:s') . '</p>';
    
    // Summary section if requested
    if ($include_summary) {
        $html .= '<h2>Summary</h2>';
        $html .= '<table border="1" cellpadding="5" cellspacing="0" width="100%">';
        $html .= '<tr><td width="30%"><strong>Total Orders</strong></td><td>' . $summary['total_orders'] . '</td></tr>';
        $html .= '<tr><td><strong>Total Revenue</strong></td><td>Rp ' . number_format($summary['total_revenue'], 0, ',', '.') . '</td></tr>';
        $html .= '</table>';
        
        // Status breakdown
        $html .= '<h3>Status Breakdown</h3>';
        $html .= '<table border="1" cellpadding="5" cellspacing="0" width="100%">';
        $html .= '<tr><th>Status</th><th>Count</th></tr>';
        foreach ($summary['status_counts'] as $status => $count) {
            $html .= '<tr><td>' . getStatusLabel($status) . '</td><td>' . $count . '</td></tr>';
        }
        $html .= '</table>';
        
        // Payment status breakdown
        $html .= '<h3>Payment Status Breakdown</h3>';
        $html .= '<table border="1" cellpadding="5" cellspacing="0" width="100%">';
        $html .= '<tr><th>Status</th><th>Count</th></tr>';
        foreach ($summary['payment_counts'] as $status => $count) {
            $html .= '<tr><td>' . ucfirst($status) . '</td><td>' . $count . '</td></tr>';
        }
        $html .= '</table>';
    }
    
    // Orders list
    $html .= '<h2>Orders List</h2>';
    $html .= '<table border="1" cellpadding="5" cellspacing="0" width="100%">';
    
    // Table header
    $html .= '<tr bgcolor="#f2f2f2">';
    $html .= '<th>Order ID</th>';
    $html .= '<th>Date</th>';
    $html .= '<th>Status</th>';
    $html .= '<th>Total</th>';
    
    if ($include_customer) {
        $html .= '<th>Customer</th>';
    }
    
    if ($include_payment) {
        $html .= '<th>Payment</th>';
    }
    
    $html .= '</tr>';
    
    // Table data
    foreach ($orders as $order) {
        $html .= '<tr>';
        $html .= '<td>' . ($order['order_number'] ?? $order['order_id']) . '</td>';
        $html .= '<td>' . (isset($order['order_date']) ? date('Y-m-d H:i', strtotime($order['order_date'])) : 'N/A') . '</td>';
        $html .= '<td>' . getStatusLabel($order['order_status'] ?? $order['status'] ?? 'dibuat') . '</td>';
        $html .= '<td>Rp ' . number_format($order['total_amount'] ?? 0, 0, ',', '.') . '</td>';
        
        if ($include_customer) {
            $html .= '<td>';
            $html .= 'Name: ' . ($order['full_name'] ?? $order['user_name'] ?? 'N/A') . '<br>';
            $html .= 'Email: ' . ($order['email'] ?? 'N/A') . '<br>';
            $html .= 'Phone: ' . ($order['phone'] ?? 'N/A');
            $html .= '</td>';
        }
        
        if ($include_payment) {
            $html .= '<td>';
            $html .= 'Method: ' . ucwords(str_replace('_', ' ', $order['payment_method'] ?? 'N/A')) . '<br>';
            $html .= 'Status: ' . ucfirst($order['payment_status'] ?? 'N/A');
            $html .= '</td>';
        }
        
        $html .= '</tr>';
        
        // Shipping details if requested
        if ($include_shipping) {
            $html .= '<tr>';
            $html .= '<td colspan="' . ($include_customer && $include_payment ? '6' : ($include_customer || $include_payment ? '5' : '4')) . '">';
            $html .= '<strong>Shipping Details:</strong><br>';
            $html .= 'Name: ' . ($order['shipping_name'] ?? 'N/A') . '<br>';
            $html .= 'Address: ' . nl2br($order['shipping_address'] ?? 'N/A') . '<br>';
            $html .= 'Method: ' . ucwords(str_replace('_', ' ', $order['shipping_method'] ?? 'N/A')) . '<br>';
            $html .= 'Cost: Rp ' . number_format($order['shipping_cost'] ?? 0, 0, ',', '.');
            $html .= '</td>';
            $html .= '</tr>';
        }
        
        // Order items if requested
        if ($include_items && isset($order['items']) && count($order['items']) > 0) {
            $html .= '<tr>';
            $html .= '<td colspan="' . ($include_customer && $include_payment ? '6' : ($include_customer || $include_payment ? '5' : '4')) . '">';
            $html .= '<strong>Order Items:</strong><br>';
            $html .= '<table border="1" cellpadding="3" cellspacing="0" width="100%">';
            $html .= '<tr bgcolor="#f2f2f2"><th>Product</th><th>SKU</th><th>Quantity</th><th>Price</th><th>Subtotal</th></tr>';
            
            foreach ($order['items'] as $item) {
                $html .= '<tr>';
                $html .= '<td>' . ($item['product_name'] ?? 'N/A') . '</td>';
                $html .= '<td>' . ($item['sku'] ?? 'N/A') . '</td>';
                $html .= '<td>' . $item['quantity'] . '</td>';
                $html .= '<td>Rp ' . number_format($item['price'] ?? 0, 0, ',', '.') . '</td>';
                $html .= '<td>Rp ' . number_format(($item['price'] ?? 0) * $item['quantity'], 0, ',', '.') . '</td>';
                $html .= '</tr>';
            }
            
            $html .= '</table>';
            $html .= '</td>';
            $html .= '</tr>';
        }
        
        // Status history if requested
        if ($include_status && isset($order['status_history']) && count($order['status_history']) > 0) {
            $html .= '<tr>';
            $html .= '<td colspan="' . ($include_customer && $include_payment ? '6' : ($include_customer || $include_payment ? '5' : '4')) . '">';
            $html .= '<strong>Status History:</strong><br>';
            $html .= '<table border="1" cellpadding="3" cellspacing="0" width="100%">';
            $html .= '<tr bgcolor="#f2f2f2"><th>Date</th><th>Status</th><th>Updated By</th><th>Note</th></tr>';
            
            foreach ($order['status_history'] as $history) {
                $html .= '<tr>';
                $html .= '<td>' . (isset($history['created_at']) ? date('Y-m-d H:i', strtotime($history['created_at'])) : 'N/A') . '</td>';
                $html .= '<td>' . getStatusLabel($history['status'] ?? 'dibuat') . '</td>';
                $html .= '<td>' . ($history['admin_username'] ?? 'System') . '</td>';
                $html .= '<td>' . ($history['note'] ?? $history['notes'] ?? '-') . '</td>';
                $html .= '</tr>';
            }
            
            $html .= '</table>';
            $html .= '</td>';
            $html .= '</tr>';
        }
        
        // Add a spacer row between orders
        $html .= '<tr><td colspan="' . ($include_customer && $include_payment ? '6' : ($include_customer || $include_payment ? '5' : '4')) . '" height="10" bgcolor="#f9f9f9"></td></tr>';
    }
    
    $html .= '</table>';
    
    return $html;
}

/**
 * Convert status code to display label
 */
function getStatusLabel($status) {
    switch ($status) {
        case 'dibuat':
            return 'Pending';
        case 'diproses':
            return 'Processing';
        case 'dikirim':
            return 'Shipped';
        case 'terkirim':
            return 'Delivered';
        case 'dibatalkan':
            return 'Cancelled';
        default:
            return ucfirst($status);
    }
}
