<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

session_start();

try {
    if (!isset($_SESSION['user'])) {
        throw new Exception('User not logged in');
    }
    
    $conn = getConnection();
    
    // Get user's wishlist
    $stmt = $conn->prepare("
        SELECT w.wishlist_id
        FROM wishlists w
        WHERE w.user_id = ?
        LIMIT 1
    ");
    $stmt->execute([$_SESSION['user']['id']]);
    $wishlist = $stmt->fetch();
    
    if (!$wishlist) {
        echo json_encode([
            'status' => 'success',
            'data' => ['count' => 0]
        ]);
        exit;
    }
    
    // Get total items in wishlist
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_items
        FROM wishlist_items
        WHERE wishlist_id = ?
    ");
    $stmt->execute([$wishlist['wishlist_id']]);
    $result = $stmt->fetch();
    
    echo json_encode([
        'status' => 'success',
        'data' => ['count' => (int)$result['total_items']]
    ]);
    
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 