## Brief overview
This set of guidelines is specific to the PHP e-commerce project "Tewuneed2", a grocery supermarket website. These rules outline the coding conventions, project structure, and development preferences for maintaining and extending this application.

## Project structure
- PHP files are organized in the root directory by functionality (e.g., product categories, authentication, cart)
- Admin functionality is separated in the `/admin` directory
- API endpoints are organized in the `/api` directory with subdirectories for specific functionality
- Shared code is placed in the `/includes` directory
- Database configuration is in the `/config` directory
- Models representing data entities are in the `/models` directory
- Assets (CSS, JS, images) are stored in the `/assets` directory with appropriate subdirectories

## Coding conventions
- Use PHP PDO for database operations with prepared statements for security
- Implement proper error handling with try/catch blocks for database operations
- Use meaningful function and variable names that describe their purpose
- Include docblocks for functions describing their purpose and parameters
- Sanitize user input using the `sanitizeInput()` function before using in queries or output
- Validate form inputs on both client and server sides
- Use consistent indentation (4 spaces) for all PHP code
- Separate PHP logic from HTML presentation when possible
- Use `htmlspecialchars()` when outputting user-provided data to prevent XSS

## Database practices
- Use prepared statements with named parameters for all database queries
- Implement proper error logging for database operations
- Use JOINs for related data rather than multiple separate queries
- Group related database operations in transactions when appropriate
- Use consistent naming conventions for database tables and columns

## Security practices
- Store passwords using PHP's `password_hash()` function
- Validate user authentication status before allowing access to protected pages
- Implement CSRF protection for forms using tokens
- Use session management for user authentication
- Implement proper input validation and sanitization
- Log user activities for audit purposes using the `logActivity()` function

## Frontend development
- Use Bootstrap 5 for responsive UI components
- Implement modals for product details to enhance user experience
- Use Font Awesome icons for visual elements
- Separate JavaScript functionality in dedicated .js files
- Implement proper error and success messaging for user actions
- Use AJAX for cart operations to avoid page reloads

## Cart functionality
- Implement add, remove, and update quantity operations
- Display cart summary with subtotal, shipping, and total
- Show appropriate messages for empty cart state
- Implement quantity validation against available stock
- Support promo code application

## Product management
- Include comprehensive product details (name, price, description, features)
- Organize products by categories
- Support product images with fallback to default image
- Include stock management functionality
- Display product features in a structured format

## User experience
- Implement clear navigation with breadcrumbs
- Show loading indicators for asynchronous operations
- Provide clear feedback for user actions (success/error messages)
- Implement responsive design for mobile compatibility
- Use consistent styling across all pages
