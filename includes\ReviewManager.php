<?php
/**
 * Review Manager Class
 * Mengelola sistem ulasan dan rating produk
 */

require_once __DIR__ . '/db_connect.php';

class ReviewManager {
    private $conn;

    public function __construct() {
        global $conn;
        $this->conn = $conn;
    }

    /**
     * Get product reviews with pagination
     */
    public function getProductReviews($product_id, $page = 1, $limit = 10, $rating_filter = null, $sort = 'newest') {
        try {
            $offset = ($page - 1) * $limit;

            // Build WHERE clause
            $where_conditions = ["pr.product_id = ? AND pr.status = 'approved'"];
            $params = [$product_id];

            if ($rating_filter && $rating_filter >= 1 && $rating_filter <= 5) {
                $where_conditions[] = "pr.rating = ?";
                $params[] = $rating_filter;
            }

            $where_clause = implode(' AND ', $where_conditions);

            // Build ORDER BY clause
            $order_by = match($sort) {
                'oldest' => 'pr.created_at ASC',
                'highest_rating' => 'pr.rating DESC, pr.created_at DESC',
                'lowest_rating' => 'pr.rating ASC, pr.created_at DESC',
                'most_helpful' => 'pr.helpful_count DESC, pr.created_at DESC',
                default => 'pr.created_at DESC' // newest
            };

            $sql = "
                SELECT
                    pr.review_id,
                    pr.rating,
                    pr.review_title,
                    pr.review_text,
                    pr.is_verified_purchase,
                    pr.is_anonymous,
                    pr.helpful_count,
                    pr.not_helpful_count,
                    pr.created_at,
                    CASE
                        WHEN pr.is_anonymous = 1 THEN 'Anonymous User'
                        ELSE u.full_name
                    END as user_name,
                    u.user_id
                FROM product_reviews pr
                JOIN users u ON pr.user_id = u.user_id
                WHERE $where_clause
                ORDER BY $order_by
                LIMIT ? OFFSET ?
            ";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get review images for each review
            foreach ($reviews as &$review) {
                $review['images'] = $this->getReviewImages($review['review_id']);
                $review['replies'] = $this->getReviewReplies($review['review_id']);
            }

            return $reviews;
        } catch (PDOException $e) {
            error_log("Error getting product reviews: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get product rating summary
     */
    public function getProductRatingSummary($product_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM product_rating_summary WHERE product_id = ?
            ");
            $stmt->execute([$product_id]);
            $summary = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$summary) {
                // Return default summary if no reviews yet
                return [
                    'product_id' => $product_id,
                    'total_reviews' => 0,
                    'average_rating' => 0.00,
                    'rating_1_count' => 0,
                    'rating_2_count' => 0,
                    'rating_3_count' => 0,
                    'rating_4_count' => 0,
                    'rating_5_count' => 0
                ];
            }

            return $summary;
        } catch (PDOException $e) {
            error_log("Error getting rating summary: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get review images
     */
    private function getReviewImages($review_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT image_url, image_caption
                FROM review_images
                WHERE review_id = ?
                ORDER BY sort_order ASC
            ");
            $stmt->execute([$review_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting review images: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get review replies
     */
    private function getReviewReplies($review_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT
                    rr.reply_text,
                    rr.is_official,
                    rr.created_at,
                    u.name as user_name
                FROM review_replies rr
                JOIN users u ON rr.user_id = u.user_id
                WHERE rr.review_id = ?
                ORDER BY rr.created_at ASC
            ");
            $stmt->execute([$review_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Error getting review replies: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Add new review
     */
    public function addReview($product_id, $user_id, $rating, $review_title, $review_text, $order_id = null, $is_anonymous = false) {
        try {
            // Validate input
            if (!is_numeric($product_id) || !is_numeric($user_id) || !is_numeric($rating)) {
                throw new Exception("Invalid input parameters");
            }

            if ($rating < 1 || $rating > 5) {
                throw new Exception("Rating must be between 1 and 5");
            }

            // Check if user already reviewed this product
            $stmt = $this->conn->prepare("
                SELECT review_id FROM product_reviews
                WHERE product_id = ? AND user_id = ?
            ");
            $stmt->execute([$product_id, $user_id]);

            if ($stmt->fetch()) {
                throw new Exception("You have already reviewed this product");
            }

            // Insert review
            $stmt = $this->conn->prepare("
                INSERT INTO product_reviews (
                    product_id, user_id, order_id, rating, review_title, review_text,
                    is_verified_purchase, is_anonymous, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'approved')
            ");

            $is_verified = $order_id ? true : false;

            $stmt->execute([
                $product_id,
                $user_id,
                $order_id,
                $rating,
                $review_title,
                $review_text,
                $is_verified,
                $is_anonymous
            ]);

            $review_id = $this->conn->lastInsertId();

            // Update rating summary
            $this->updateRatingSummary($product_id);

            return $review_id;
        } catch (Exception $e) {
            error_log("Error adding review: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update product rating summary
     */
    private function updateRatingSummary($product_id) {
        try {
            $stmt = $this->conn->prepare("
                INSERT INTO product_rating_summary (
                    product_id, total_reviews, average_rating,
                    rating_1_count, rating_2_count, rating_3_count, rating_4_count, rating_5_count
                )
                SELECT
                    product_id,
                    COUNT(*) as total_reviews,
                    ROUND(AVG(rating), 2) as average_rating,
                    SUM(CASE WHEN rating = 1 THEN 1 ELSE 0 END) as rating_1_count,
                    SUM(CASE WHEN rating = 2 THEN 1 ELSE 0 END) as rating_2_count,
                    SUM(CASE WHEN rating = 3 THEN 1 ELSE 0 END) as rating_3_count,
                    SUM(CASE WHEN rating = 4 THEN 1 ELSE 0 END) as rating_4_count,
                    SUM(CASE WHEN rating = 5 THEN 1 ELSE 0 END) as rating_5_count
                FROM product_reviews
                WHERE product_id = ? AND status = 'approved'
                GROUP BY product_id
                ON DUPLICATE KEY UPDATE
                    total_reviews = VALUES(total_reviews),
                    average_rating = VALUES(average_rating),
                    rating_1_count = VALUES(rating_1_count),
                    rating_2_count = VALUES(rating_2_count),
                    rating_3_count = VALUES(rating_3_count),
                    rating_4_count = VALUES(rating_4_count),
                    rating_5_count = VALUES(rating_5_count)
            ");

            $stmt->execute([$product_id]);
        } catch (PDOException $e) {
            error_log("Error updating rating summary: " . $e->getMessage());
        }
    }

    /**
     * Mark review as helpful/not helpful
     */
    public function markReviewHelpful($review_id, $user_id, $is_helpful) {
        try {
            // Check if user already voted
            $stmt = $this->conn->prepare("
                SELECT id FROM review_helpfulness
                WHERE review_id = ? AND user_id = ?
            ");
            $stmt->execute([$review_id, $user_id]);

            if ($stmt->fetch()) {
                // Update existing vote
                $stmt = $this->conn->prepare("
                    UPDATE review_helpfulness
                    SET is_helpful = ?
                    WHERE review_id = ? AND user_id = ?
                ");
                $stmt->execute([$is_helpful, $review_id, $user_id]);
            } else {
                // Insert new vote
                $stmt = $this->conn->prepare("
                    INSERT INTO review_helpfulness (review_id, user_id, is_helpful)
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$review_id, $user_id, $is_helpful]);
            }

            // Update helpful counts
            $this->updateHelpfulCounts($review_id);

            return true;
        } catch (PDOException $e) {
            error_log("Error marking review helpful: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update helpful counts for a review
     */
    private function updateHelpfulCounts($review_id) {
        try {
            $stmt = $this->conn->prepare("
                UPDATE product_reviews
                SET
                    helpful_count = (
                        SELECT COUNT(*) FROM review_helpfulness
                        WHERE review_id = ? AND is_helpful = 1
                    ),
                    not_helpful_count = (
                        SELECT COUNT(*) FROM review_helpfulness
                        WHERE review_id = ? AND is_helpful = 0
                    )
                WHERE review_id = ?
            ");
            $stmt->execute([$review_id, $review_id, $review_id]);
        } catch (PDOException $e) {
            error_log("Error updating helpful counts: " . $e->getMessage());
        }
    }

    /**
     * Get total review count for pagination
     */
    public function getTotalReviewCount($product_id, $rating_filter = null) {
        try {
            $where_conditions = ["product_id = ? AND status = 'approved'"];
            $params = [$product_id];

            if ($rating_filter && $rating_filter >= 1 && $rating_filter <= 5) {
                $where_conditions[] = "rating = ?";
                $params[] = $rating_filter;
            }

            $where_clause = implode(' AND ', $where_conditions);

            $stmt = $this->conn->prepare("
                SELECT COUNT(*) as total
                FROM product_reviews
                WHERE $where_clause
            ");
            $stmt->execute($params);

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['total'];
        } catch (PDOException $e) {
            error_log("Error getting total review count: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Generate star rating HTML
     */
    public static function generateStarRating($rating, $show_number = true) {
        $html = '<div class="star-rating">';

        for ($i = 1; $i <= 5; $i++) {
            if ($i <= $rating) {
                $html .= '<i class="fas fa-star text-warning"></i>';
            } elseif ($i - 0.5 <= $rating) {
                $html .= '<i class="fas fa-star-half-alt text-warning"></i>';
            } else {
                $html .= '<i class="far fa-star text-muted"></i>';
            }
        }

        if ($show_number) {
            $html .= ' <span class="rating-number">(' . number_format($rating, 1) . ')</span>';
        }

        $html .= '</div>';
        return $html;
    }
}
