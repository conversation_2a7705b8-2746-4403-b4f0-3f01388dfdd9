<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Toastify - Tewuneed</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
        }
        
        .btn-test {
            margin: 10px;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="test-card">
        <h1 class="mb-4">🧪 Test Notifikasi Toastify</h1>
        <p class="text-muted mb-4">Klik tombol di bawah untuk menguji notifikasi</p>
        
        <div class="d-grid gap-3">
            <button class="btn btn-success btn-test" onclick="testSuccess()">
                ✅ Test Success Notification
            </button>
            
            <button class="btn btn-danger btn-test" onclick="testError()">
                ❌ Test Error Notification
            </button>
            
            <button class="btn btn-warning btn-test" onclick="testWarning()">
                ⚠️ Test Warning Notification
            </button>
            
            <button class="btn btn-info btn-test" onclick="testInfo()">
                ℹ️ Test Info Notification
            </button>
            
            <button class="btn btn-primary btn-test" onclick="testCartNotification()">
                🛒 Test Cart Notification
            </button>
            
            <button class="btn btn-secondary btn-test" onclick="testMultiple()">
                🔄 Test Multiple Notifications
            </button>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                Status: <span id="status">Ready</span><br>
                Toastify: <span id="toastify-status">Checking...</span>
            </small>
        </div>
        
        <div class="mt-3">
            <a href="Products.php" class="btn btn-outline-primary">
                ← Kembali ke Products
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    
    <script>
        // Check if Toastify is loaded
        document.addEventListener('DOMContentLoaded', function() {
            const statusElement = document.getElementById('toastify-status');
            if (typeof Toastify !== 'undefined') {
                statusElement.textContent = '✅ Loaded';
                statusElement.className = 'text-success';
            } else {
                statusElement.textContent = '❌ Not Loaded';
                statusElement.className = 'text-danger';
            }
        });
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function showToast(message, type = 'success') {
            updateStatus(`Showing ${type} notification`);
            
            let background = '';
            switch(type) {
                case 'success':
                    background = 'linear-gradient(to right, #00b09b, #96c93d)';
                    break;
                case 'danger':
                case 'error':
                    background = 'linear-gradient(to right, #ff416c, #ff4b2b)';
                    break;
                case 'warning':
                    background = 'linear-gradient(to right, #f46b45, #eea849)';
                    break;
                case 'info':
                    background = 'linear-gradient(to right, #667eea, #764ba2)';
                    break;
                default:
                    background = 'linear-gradient(to right, #00b09b, #96c93d)';
            }
            
            if (typeof Toastify !== 'undefined') {
                Toastify({
                    text: message,
                    duration: 3000,
                    close: true,
                    gravity: "top",
                    position: "right",
                    backgroundColor: background,
                    stopOnFocus: true
                }).showToast();
            } else {
                alert('Toastify not available: ' + message);
            }
        }
        
        function testSuccess() {
            showToast('✅ Operasi berhasil dilakukan!', 'success');
        }
        
        function testError() {
            showToast('❌ Terjadi kesalahan sistem!', 'error');
        }
        
        function testWarning() {
            showToast('⚠️ Peringatan: Stok hampir habis!', 'warning');
        }
        
        function testInfo() {
            showToast('ℹ️ Informasi: Update tersedia', 'info');
        }
        
        function testCartNotification() {
            showToast('🛒 2 Smartphone Samsung telah ditambahkan ke keranjang', 'success');
        }
        
        function testMultiple() {
            updateStatus('Testing multiple notifications...');
            
            setTimeout(() => showToast('1️⃣ Notifikasi pertama', 'success'), 100);
            setTimeout(() => showToast('2️⃣ Notifikasi kedua', 'info'), 600);
            setTimeout(() => showToast('3️⃣ Notifikasi ketiga', 'warning'), 1100);
            setTimeout(() => showToast('4️⃣ Notifikasi keempat', 'success'), 1600);
        }
        
        // Test on page load
        setTimeout(() => {
            if (typeof Toastify !== 'undefined') {
                showToast('🎉 Halaman test berhasil dimuat!', 'success');
            }
        }, 500);
    </script>
</body>
</html>
