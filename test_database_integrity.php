<?php
/**
 * TEWUNEED - Database Integrity Testing
 * Comprehensive testing for database structure and data integrity
 */

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

// Test configuration
$test_results = [];
$total_tests = 0;
$passed_tests = 0;

/**
 * Test helper function
 */
function runTest($test_name, $test_function) {
    global $test_results, $total_tests, $passed_tests;
    
    $total_tests++;
    $start_time = microtime(true);
    
    try {
        $result = $test_function();
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        
        if ($result['success']) {
            $passed_tests++;
            $test_results[] = [
                'name' => $test_name,
                'status' => 'PASS',
                'message' => $result['message'],
                'time' => $execution_time . 'ms',
                'details' => $result['details'] ?? null
            ];
        } else {
            $test_results[] = [
                'name' => $test_name,
                'status' => 'FAIL',
                'message' => $result['message'],
                'time' => $execution_time . 'ms',
                'error' => $result['error'] ?? null
            ];
        }
    } catch (Exception $e) {
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        $test_results[] = [
            'name' => $test_name,
            'status' => 'ERROR',
            'message' => 'Test execution failed',
            'time' => $execution_time . 'ms',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 1: Core Tables Existence
 */
function testCoreTablesExistence() {
    global $conn;
    
    try {
        $required_tables = [
            'users', 'products', 'categories', 'orders', 'order_items',
            'cart', 'admin_accounts', 'admin_permissions'
        ];
        
        $stmt = $conn->query("SHOW TABLES");
        $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $missing_tables = array_diff($required_tables, $existing_tables);
        $existing_core_tables = array_intersect($required_tables, $existing_tables);
        
        if (empty($missing_tables)) {
            return [
                'success' => true,
                'message' => 'All core tables exist',
                'details' => 'Tables: ' . implode(', ', $existing_core_tables)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Some core tables are missing',
                'error' => 'Missing: ' . implode(', ', $missing_tables)
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to check core tables existence',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 2: Foreign Key Constraints
 */
function testForeignKeyConstraints() {
    global $conn;
    
    try {
        $constraints_check = [
            'products' => 'SELECT COUNT(*) FROM products p LEFT JOIN categories c ON p.category_id = c.category_id WHERE c.category_id IS NULL AND p.category_id IS NOT NULL',
            'orders' => 'SELECT COUNT(*) FROM orders o LEFT JOIN users u ON o.user_id = u.user_id WHERE u.user_id IS NULL',
            'order_items' => 'SELECT COUNT(*) FROM order_items oi LEFT JOIN orders o ON oi.order_id = o.order_id WHERE o.order_id IS NULL',
            'cart' => 'SELECT COUNT(*) FROM cart c LEFT JOIN users u ON c.user_id = u.user_id WHERE u.user_id IS NULL AND c.user_id IS NOT NULL'
        ];
        
        $integrity_issues = [];
        $total_checks = 0;
        
        foreach ($constraints_check as $table => $query) {
            $stmt = $conn->query($query);
            $orphaned_records = $stmt->fetchColumn();
            $total_checks++;
            
            if ($orphaned_records > 0) {
                $integrity_issues[] = "$table: $orphaned_records orphaned records";
            }
        }
        
        if (empty($integrity_issues)) {
            return [
                'success' => true,
                'message' => 'Foreign key integrity is maintained',
                'details' => "Checked $total_checks relationships"
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Foreign key integrity issues found',
                'error' => implode(', ', $integrity_issues)
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to check foreign key constraints',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 3: Data Consistency
 */
function testDataConsistency() {
    global $conn;
    
    try {
        $consistency_checks = [];
        
        // Check if order totals match order items
        $stmt = $conn->query("
            SELECT o.order_id, o.total_amount, 
                   COALESCE(SUM(oi.total_price), 0) as calculated_total
            FROM orders o 
            LEFT JOIN order_items oi ON o.order_id = oi.order_id 
            GROUP BY o.order_id, o.total_amount
            HAVING ABS(o.total_amount - calculated_total) > 0.01
            LIMIT 5
        ");
        $order_total_mismatches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Check for negative stock
        $stmt = $conn->query("SELECT COUNT(*) FROM products WHERE stock < 0");
        $negative_stock_count = $stmt->fetchColumn();
        
        // Check for invalid order statuses
        $valid_statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
        $status_placeholders = str_repeat('?,', count($valid_statuses) - 1) . '?';
        $stmt = $conn->prepare("SELECT COUNT(*) FROM orders WHERE order_status NOT IN ($status_placeholders)");
        $stmt->execute($valid_statuses);
        $invalid_status_count = $stmt->fetchColumn();
        
        $issues = [];
        if (!empty($order_total_mismatches)) {
            $issues[] = count($order_total_mismatches) . " orders with total amount mismatches";
        }
        if ($negative_stock_count > 0) {
            $issues[] = "$negative_stock_count products with negative stock";
        }
        if ($invalid_status_count > 0) {
            $issues[] = "$invalid_status_count orders with invalid status";
        }
        
        if (empty($issues)) {
            return [
                'success' => true,
                'message' => 'Data consistency checks passed',
                'details' => 'Order totals, stock levels, and statuses are consistent'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Data consistency issues found',
                'error' => implode(', ', $issues)
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to check data consistency',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 4: Index Performance
 */
function testIndexPerformance() {
    global $conn;
    
    try {
        $performance_queries = [
            'products_by_category' => "SELECT COUNT(*) FROM products WHERE category_id = 1",
            'orders_by_user' => "SELECT COUNT(*) FROM orders WHERE user_id = 1",
            'cart_by_user' => "SELECT COUNT(*) FROM cart WHERE user_id = 1",
            'products_search' => "SELECT COUNT(*) FROM products WHERE name LIKE '%test%'"
        ];
        
        $slow_queries = [];
        $total_time = 0;
        
        foreach ($performance_queries as $query_name => $query) {
            $start_time = microtime(true);
            $stmt = $conn->query($query);
            $stmt->fetchColumn();
            $execution_time = (microtime(true) - $start_time) * 1000;
            $total_time += $execution_time;
            
            if ($execution_time > 100) { // More than 100ms is considered slow
                $slow_queries[] = "$query_name: " . round($execution_time, 2) . "ms";
            }
        }
        
        $avg_time = round($total_time / count($performance_queries), 2);
        
        if (empty($slow_queries)) {
            return [
                'success' => true,
                'message' => 'Query performance is acceptable',
                'details' => "Average query time: {$avg_time}ms"
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Some queries are performing slowly',
                'error' => 'Slow queries: ' . implode(', ', $slow_queries)
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to check index performance',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 5: Database Size and Statistics
 */
function testDatabaseStatistics() {
    global $conn;
    
    try {
        $stats = [];
        
        // Get table counts
        $tables = ['users', 'products', 'categories', 'orders', 'cart'];
        foreach ($tables as $table) {
            try {
                $stmt = $conn->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                $stats[$table] = $count;
            } catch (Exception $e) {
                $stats[$table] = 'Error: ' . $e->getMessage();
            }
        }
        
        // Calculate total records
        $total_records = array_sum(array_filter($stats, 'is_numeric'));
        
        return [
            'success' => true,
            'message' => 'Database statistics collected successfully',
            'details' => 'Total records: ' . $total_records . ' | ' . 
                        implode(', ', array_map(function($k, $v) { 
                            return "$k: $v"; 
                        }, array_keys($stats), $stats))
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to collect database statistics',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 6: Triggers and Procedures
 */
function testTriggersAndProcedures() {
    global $conn;
    
    try {
        // Check for triggers
        $stmt = $conn->query("SHOW TRIGGERS");
        $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Check for stored procedures
        $stmt = $conn->query("SHOW PROCEDURE STATUS WHERE Db = DATABASE()");
        $procedures = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $trigger_count = count($triggers);
        $procedure_count = count($procedures);
        
        if ($trigger_count > 0 || $procedure_count > 0) {
            return [
                'success' => true,
                'message' => 'Database automation features found',
                'details' => "Triggers: $trigger_count, Procedures: $procedure_count"
            ];
        } else {
            return [
                'success' => true,
                'message' => 'No triggers or procedures found',
                'details' => 'Database uses basic structure without automation'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to check triggers and procedures',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 7: Connection Pool and Limits
 */
function testConnectionLimits() {
    global $conn;
    
    try {
        // Test multiple connections
        $connections = [];
        $max_connections = 5;
        
        for ($i = 0; $i < $max_connections; $i++) {
            try {
                $test_conn = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME,
                    DB_USER,
                    DB_PASS,
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
                $connections[] = $test_conn;
            } catch (Exception $e) {
                break;
            }
        }
        
        $successful_connections = count($connections);
        
        // Close test connections
        $connections = null;
        
        if ($successful_connections >= 3) {
            return [
                'success' => true,
                'message' => 'Connection pooling is working correctly',
                'details' => "Successfully created $successful_connections concurrent connections"
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Connection limit issues detected',
                'error' => "Only $successful_connections connections successful"
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to test connection limits',
            'error' => $e->getMessage()
        ];
    }
}

// Run all tests
runTest('Core Tables Existence', 'testCoreTablesExistence');
runTest('Foreign Key Constraints', 'testForeignKeyConstraints');
runTest('Data Consistency', 'testDataConsistency');
runTest('Index Performance', 'testIndexPerformance');
runTest('Database Statistics', 'testDatabaseStatistics');
runTest('Triggers and Procedures', 'testTriggersAndProcedures');
runTest('Connection Limits', 'testConnectionLimits');

// Calculate success rate
$success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEWUNEED - Database Integrity Test Results</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-error { color: #fd7e14; }
        .test-card { margin-bottom: 1rem; }
        .success-rate {
            font-size: 2rem;
            font-weight: bold;
        }
        .success-high { color: #28a745; }
        .success-medium { color: #ffc107; }
        .success-low { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4">
                        <i class="fas fa-database me-3"></i>
                        Database Integrity Test Results
                    </h1>
                    <p class="lead">Comprehensive testing of database structure and data integrity</p>
                </div>

                <!-- Summary Card -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-md-3">
                                <h5>Total Tests</h5>
                                <span class="display-6"><?php echo $total_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Passed</h5>
                                <span class="display-6 test-pass"><?php echo $passed_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Failed</h5>
                                <span class="display-6 test-fail"><?php echo $total_tests - $passed_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Success Rate</h5>
                                <span class="success-rate <?php 
                                    echo $success_rate >= 80 ? 'success-high' : 
                                         ($success_rate >= 60 ? 'success-medium' : 'success-low'); 
                                ?>"><?php echo $success_rate; ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="row">
                    <?php foreach ($test_results as $test): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card test-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><?php echo htmlspecialchars($test['name']); ?></h6>
                                <span class="badge <?php 
                                    echo $test['status'] === 'PASS' ? 'bg-success' : 
                                         ($test['status'] === 'FAIL' ? 'bg-danger' : 'bg-warning'); 
                                ?>">
                                    <?php echo $test['status']; ?>
                                </span>
                            </div>
                            <div class="card-body">
                                <p class="mb-2">
                                    <i class="fas <?php 
                                        echo $test['status'] === 'PASS' ? 'fa-check-circle test-pass' : 
                                             ($test['status'] === 'FAIL' ? 'fa-times-circle test-fail' : 'fa-exclamation-triangle test-error'); 
                                    ?> me-2"></i>
                                    <?php echo htmlspecialchars($test['message']); ?>
                                </p>
                                
                                <?php if (isset($test['details'])): ?>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <?php echo htmlspecialchars($test['details']); ?>
                                </small>
                                <?php endif; ?>
                                
                                <?php if (isset($test['error'])): ?>
                                <div class="alert alert-danger alert-sm mt-2 mb-0">
                                    <small><strong>Error:</strong> <?php echo htmlspecialchars($test['error']); ?></small>
                                </div>
                                <?php endif; ?>
                                
                                <div class="text-end mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo $test['time']; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary me-2">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                    <a href="admin/dashboard.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                    </a>
                    <button onclick="window.location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-2"></i>Run Tests Again
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
