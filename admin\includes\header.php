<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Initialize session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    // Don't output HTML here, just redirect
    if (!headers_sent()) {
        header('Location: admin_login.php');
        exit;
    } else {
        echo "Silakan akses halaman login admin di <a href='admin_login.php'>admin_login.php</a>";
        exit;
    }
}

// Database connection
require_once '../includes/db_connect.php';

// Common functions (if exists)
if (file_exists('../includes/functions.php')) {
    require_once '../includes/functions.php';
}

// Get admin information
$admin_id = $_SESSION['user_id'];
try {
    $stmt = $conn->prepare("SELECT * FROM users WHERE user_id = ? AND role = 'admin'");
    $stmt->execute([$admin_id]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$admin) {
        // If not admin, redirect to login
        session_destroy();
        if (!headers_sent()) {
            header('Location: admin_login.php');
            exit;
        } else {
            echo "Access denied. Please login as admin.";
            exit;
        }
    }
} catch (PDOException $e) {
    die("Database error: " . $e->getMessage());
}

// Set page title if not already set
if (!isset($page_title)) {
    $page_title = "Admin Dashboard";
}

$_SESSION['user_id'] = $admin['user_id'];
$_SESSION['role'] = 'admin';
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - TewuNeed Admin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Toastify for notifications -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-gradient: linear-gradient(180deg, #7c3aed 0%, #a855f7 50%, #c084fc 100%);
            --sidebar-bg: #212529;
            --sidebar-hover: rgba(255, 255, 255, 0.15);
            --sidebar-active: rgba(255, 255, 255, 0.25);
            --content-bg: #f8f9fa;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        .sidebar {
            min-height: 100vh;
            background: var(--primary-gradient);
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            overflow-y: auto;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 12px 20px;
            border-radius: 12px;
            margin: 4px 16px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 15px;
            text-decoration: none;
        }

        .sidebar .nav-link:hover {
            color: white;
            background: var(--sidebar-hover);
            transform: translateX(8px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .sidebar .nav-link.active {
            color: white;
            background: var(--sidebar-active);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transform: translateX(5px);
        }

        .sidebar .nav-link i {
            margin-right: 15px;
            width: 20px;
            text-align: center;
            font-size: 16px;
        }

        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            background: var(--content-bg);
        }

        .content {
            padding: 30px;
        }

        .admin-header {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .admin-title {
            color: #495057;
            font-weight: 600;
            margin: 0;
            font-size: 1.5rem;
        }

        .admin-subtitle {
            color: #6c757d;
            font-size: 0.9rem;
            margin: 0;
        }

        .card-dashboard {
            border-radius: 10px;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            border: none;
        }

        .card-dashboard:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }

        .table-responsive {
            overflow-x: auto;
            border-radius: 8px;
            box-shadow: var(--card-shadow);
        }

        .action-buttons .btn {
            margin-right: 5px;
            margin-bottom: 5px;
        }

        /* Status badges */
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-weight: 500;
        }

        .status-dibuat { background-color: #ffc107; color: #000; }
        .status-diproses { background-color: #0dcaf0; color: #000; }
        .status-dikirim { background-color: #fd7e14; color: #fff; }
        .status-terkirim { background-color: #198754; color: #fff; }
        .status-dibatalkan { background-color: #dc3545; color: #fff; }

        /* Responsive design */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                box-shadow: none;
            }

            .main-content {
                margin-left: 0;
            }

            .content {
                padding: 15px;
            }

            .sidebar .nav-link {
                margin: 2px 8px;
                padding: 10px 16px;
            }

            .sidebar .nav-link:hover {
                transform: none;
            }
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="p-4 text-center" style="padding-bottom: 2rem !important;">
            <h2 class="mb-1" style="font-weight: 700; font-size: 1.8rem;">
                <span style="color: #fbbf24;">TEWU</span><span class="text-white">NEED</span>
            </h2>
            <p class="text-white-50 mb-0" style="font-size: 14px; font-weight: 400;">Admin Dashboard</p>
        </div>

        <div class="px-3 pb-4">
            <nav class="nav flex-column">
                <a class="nav-link <?php echo ($page ?? '') === 'dashboard' ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt"></i>Dashboard
                </a>

                <a class="nav-link <?php echo ($page ?? '') === 'realtime' ? 'active' : ''; ?>" href="realtime_hub.php">
                    <i class="fas fa-broadcast-tower"></i>Real-time Hub
                </a>

                <a class="nav-link <?php echo ($page ?? '') === 'products' ? 'active' : ''; ?>" href="products_admin.php">
                    <i class="fas fa-cube"></i>Products
                </a>

                <a class="nav-link <?php echo ($page ?? '') === 'categories' ? 'active' : ''; ?>" href="categories.php">
                    <i class="fas fa-tags"></i>Categories
                </a>

                <a class="nav-link <?php echo ($page ?? '') === 'order_management' ? 'active' : ''; ?>" href="order_management.php">
                    <i class="fas fa-truck"></i>Order Management
                </a>

                <a class="nav-link <?php echo ($page ?? '') === 'users' ? 'active' : ''; ?>" href="users.php">
                    <i class="fas fa-users"></i>Users
                </a>

                <a class="nav-link <?php echo ($page ?? '') === 'reports' ? 'active' : ''; ?>" href="reports.php">
                    <i class="fas fa-chart-line"></i>Reports
                </a>

                <a class="nav-link <?php echo ($page ?? '') === 'settings' ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog"></i>Settings
                </a>
            </nav>

            <!-- User Info & Logout -->
            <div style="position: absolute; bottom: 20px; left: 16px; right: 16px;">
                <div class="text-white-50 small px-3 mb-3" style="font-size: 13px;">
                    <i class="fas fa-user-circle me-2"></i>
                    <?php echo htmlspecialchars($admin['full_name'] ?? $admin['username'] ?? 'Amos Baringbing'); ?>
                </div>

                <a class="nav-link" href="admin_logout.php" onclick="return confirm('Yakin ingin logout?')"
                   style="color: #ef4444 !important; margin: 4px 16px; padding: 8px 20px;">
                    <i class="fas fa-sign-out-alt"></i>Logout
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Admin Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="admin-title"><?php echo $page_title; ?></h1>
                    <p class="admin-subtitle">Kelola sistem TewuNeed dengan mudah</p>
                </div>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('d M Y, H:i'); ?>
                        </small>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            <?php echo htmlspecialchars($admin['full_name'] ?? $admin['username'] ?? 'Admin'); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="admin_logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content">
            <!-- Alert messages -->
            <?php if (isset($_SESSION['alert_message'])): ?>
            <div class="alert alert-<?php echo $_SESSION['alert_type'] ?? 'info'; ?> alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['alert_message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php
                // Clear the alert message
                unset($_SESSION['alert_message']);
                unset($_SESSION['alert_type']);
            endif;
            ?>

            <!-- Page Content Starts Here -->
