<?php
session_start();
header('Content-Type: application/json');

try {
    require_once '../includes/db_connect.php';
    require_once '../includes/order_status_functions.php';

    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'Not authenticated']);
        exit;
    }

    $user_id = $_SESSION['user_id'];

    // Get request data from both POST and JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    $notification_id = $input['notification_id'] ?? $_POST['notification_id'] ?? null;

    if (!$notification_id) {
        echo json_encode(['success' => false, 'message' => 'Notification ID required']);
        exit;
    }

    // Try both notification tables for compatibility
    $tables_to_update = ['notifications', 'order_notifications'];
    $success = false;

    foreach ($tables_to_update as $table) {
        try {
            $stmt = $conn->prepare("
                UPDATE {$table}
                SET is_read = TRUE
                WHERE notification_id = ? AND user_id = ?
            ");
            $result = $stmt->execute([$notification_id, $user_id]);

            if ($result && $stmt->rowCount() > 0) {
                $success = true;
                break;
            }
        } catch (Exception $e) {
            // Table might not exist, continue to next
            continue;
        }
    }

    if ($success) {
        // Get updated unread count using the function if available
        $unread_count = 0;
        if (function_exists('getUnreadNotificationsCount')) {
            $unread_count = getUnreadNotificationsCount($conn, $user_id);
        } else {
            // Fallback count
            try {
                $stmt = $conn->prepare("SELECT COUNT(*) FROM order_notifications WHERE user_id = ? AND is_read = FALSE");
                $stmt->execute([$user_id]);
                $unread_count = $stmt->fetchColumn();
            } catch (Exception $e) {
                // Ignore error
            }
        }

        echo json_encode([
            'success' => true,
            'message' => 'Notification marked as read',
            'unread_count' => $unread_count
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to mark notification as read or notification not found'
        ]);
    }

} catch (Exception $e) {
    error_log("Error in mark_notification_read.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>