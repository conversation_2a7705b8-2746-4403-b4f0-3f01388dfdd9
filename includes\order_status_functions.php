<?php
/**
 * Order Status Management Functions
 * Comprehensive system for tracking order status between admin and customers
 */

// Define order status constants
define('ORDER_STATUS_PENDING', 'pending');
define('ORDER_STATUS_CONFIRMED', 'confirmed');
define('ORDER_STATUS_PROCESSING', 'processing');
define('ORDER_STATUS_PACKED', 'packed');
define('ORDER_STATUS_SHIPPED', 'shipped');
define('ORDER_STATUS_IN_TRANSIT', 'in_transit');
define('ORDER_STATUS_OUT_FOR_DELIVERY', 'out_for_delivery');
define('ORDER_STATUS_DELIVERED', 'delivered');
define('ORDER_STATUS_CANCELLED', 'cancelled');
define('ORDER_STATUS_RETURNED', 'returned');

/**
 * Get all available order statuses with labels and descriptions
 */
function getOrderStatuses() {
    return [
        ORDER_STATUS_PENDING => [
            'label' => '<PERSON>esanan Diterima',
            'description' => 'Pesanan telah diterima dan menunggu konfirmasi',
            'color' => 'warning',
            'icon' => 'fas fa-clock',
            'logo' => '⏰' // Clock emoji as logo
        ],
        ORDER_STATUS_CONFIRMED => [
            'label' => 'Pesanan Dikonfirmasi',
            'description' => 'Pesanan telah dikonfirmasi dan pembayaran diterima',
            'color' => 'info',
            'icon' => 'fas fa-check-circle',
            'logo' => '✅' // Check mark emoji as logo
        ],
        ORDER_STATUS_PROCESSING => [
            'label' => 'Sedang Diproses',
            'description' => 'Pesanan sedang disiapkan dan diproses',
            'color' => 'primary',
            'icon' => 'fas fa-cogs',
            'logo' => '⚙️' // Gear emoji as logo
        ],
        ORDER_STATUS_PACKED => [
            'label' => 'Dikemas',
            'description' => 'Pesanan telah dikemas dan siap dikirim',
            'color' => 'secondary',
            'icon' => 'fas fa-box',
            'logo' => '📦' // Package emoji as logo
        ],
        ORDER_STATUS_SHIPPED => [
            'label' => 'Dikirim',
            'description' => 'Pesanan telah diserahkan ke kurir',
            'color' => 'info',
            'icon' => 'fas fa-shipping-fast',
            'logo' => '🚚' // Truck emoji as logo
        ],
        ORDER_STATUS_IN_TRANSIT => [
            'label' => 'Dalam Perjalanan',
            'description' => 'Pesanan sedang dalam perjalanan menuju alamat tujuan',
            'color' => 'primary',
            'icon' => 'fas fa-truck',
            'logo' => '🚛' // Delivery truck emoji as logo
        ],
        ORDER_STATUS_OUT_FOR_DELIVERY => [
            'label' => 'Sedang Diantar',
            'description' => 'Pesanan sedang dalam proses pengantaran',
            'color' => 'warning',
            'icon' => 'fas fa-motorcycle',
            'logo' => '🏍️' // Motorcycle emoji as logo
        ],
        ORDER_STATUS_DELIVERED => [
            'label' => 'Terkirim',
            'description' => 'Pesanan telah berhasil diterima',
            'color' => 'success',
            'icon' => 'fas fa-check-double',
            'logo' => '🎉' // Party emoji as logo for delivered
        ],
        ORDER_STATUS_CANCELLED => [
            'label' => 'Dibatalkan',
            'description' => 'Pesanan telah dibatalkan',
            'color' => 'danger',
            'icon' => 'fas fa-times-circle',
            'logo' => '❌' // Cross mark emoji as logo
        ],
        ORDER_STATUS_RETURNED => [
            'label' => 'Dikembalikan',
            'description' => 'Pesanan dikembalikan oleh customer',
            'color' => 'dark',
            'icon' => 'fas fa-undo',
            'logo' => '↩️' // Return arrow emoji as logo
        ],
        // Indonesian aliases for compatibility
        'dibatalkan' => [
            'label' => 'Dibatalkan',
            'description' => 'Pesanan telah dibatalkan',
            'color' => 'danger',
            'icon' => 'fas fa-times-circle',
            'logo' => '❌' // Cross mark emoji as logo
        ],
        'pending' => [
            'label' => 'Pesanan Diterima',
            'description' => 'Pesanan telah diterima dan menunggu konfirmasi',
            'color' => 'warning',
            'icon' => 'fas fa-clock',
            'logo' => '⏰' // Clock emoji as logo
        ],
        'processing' => [
            'label' => 'Sedang Diproses',
            'description' => 'Pesanan sedang disiapkan dan diproses',
            'color' => 'primary',
            'icon' => 'fas fa-cogs',
            'logo' => '⚙️' // Gear emoji as logo
        ],
        'shipped' => [
            'label' => 'Dikirim',
            'description' => 'Pesanan telah diserahkan ke kurir',
            'color' => 'info',
            'icon' => 'fas fa-shipping-fast',
            'logo' => '🚚' // Truck emoji as logo
        ],
        'delivered' => [
            'label' => 'Terkirim',
            'description' => 'Pesanan telah berhasil diterima',
            'color' => 'success',
            'icon' => 'fas fa-check-double',
            'logo' => '🎉' // Party emoji as logo for delivered
        ]
    ];
}

/**
 * Update order status with history tracking and enhanced notifications
 */
function updateOrderStatus($conn, $order_id, $new_status, $notes = '', $updated_by = null, $updated_by_type = 'system', $tracking_number = null, $estimated_delivery = null) {
    try {
        $conn->beginTransaction();

        // Get current order info
        $stmt = $conn->prepare("SELECT * FROM orders WHERE order_id = ?");
        $stmt->execute([$order_id]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$order) {
            throw new Exception("Order not found");
        }

        $statuses = getOrderStatuses();
        if (!isset($statuses[$new_status])) {
            throw new Exception("Invalid status: " . $new_status);
        }

        // Check which columns exist in orders table
        $stmt = $conn->query("DESCRIBE orders");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Determine which status column to update
        $status_column = 'STATUS'; // Default to STATUS column (uppercase)
        if (in_array('current_status', $columns)) {
            $status_column = 'current_status';
        } elseif (in_array('order_status', $columns)) {
            $status_column = 'order_status';
        } elseif (in_array('status', $columns)) {
            $status_column = 'status';
        }

        // Store old status for comparison
        $old_status = $order[$status_column] ?? 'pending';

        // Update order status
        $update_fields = ["{$status_column} = ?"];
        $update_values = [$new_status];

        // Add tracking number if column exists
        if ($tracking_number && in_array('tracking_number', $columns)) {
            $update_fields[] = "tracking_number = ?";
            $update_values[] = $tracking_number;
        }

        // Add estimated delivery if column exists
        if ($estimated_delivery && in_array('estimated_delivery', $columns)) {
            $update_fields[] = "estimated_delivery = ?";
            $update_values[] = $estimated_delivery;
        }

        // Add updated_at if column exists
        if (in_array('updated_at', $columns)) {
            $update_fields[] = "updated_at = NOW()";
        }

        $update_values[] = $order_id;

        $sql = "UPDATE orders SET " . implode(", ", $update_fields) . " WHERE order_id = ?";
        $stmt = $conn->prepare($sql);
        $update_result = $stmt->execute($update_values);

        if (!$update_result) {
            throw new Exception("Failed to update order status in database");
        }

        // Check if order_status_history table exists
        try {
            $stmt = $conn->query("DESCRIBE order_status_history");
            $history_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Prepare history insert based on available columns
            $history_fields = ['order_id', 'status'];
            $history_values = [$order_id, $new_status];
            $history_placeholders = ['?', '?'];

            if (in_array('new_status', $history_columns)) {
                $history_fields[] = 'new_status';
                $history_values[] = $new_status;
                $history_placeholders[] = '?';
            }

            if (in_array('old_status', $history_columns)) {
                $history_fields[] = 'old_status';
                $history_values[] = $old_status;
                $history_placeholders[] = '?';
            }

            if (in_array('status_label', $history_columns)) {
                $history_fields[] = 'status_label';
                $history_values[] = $statuses[$new_status]['label'];
                $history_placeholders[] = '?';
            }

            if (in_array('status_logo', $history_columns)) {
                $history_fields[] = 'status_logo';
                $history_values[] = $statuses[$new_status]['logo'];
                $history_placeholders[] = '?';
            }

            if (in_array('notes', $history_columns) && $notes) {
                $history_fields[] = 'notes';
                $history_values[] = $notes;
                $history_placeholders[] = '?';
            }

            if (in_array('updated_by', $history_columns) && $updated_by) {
                $history_fields[] = 'updated_by';
                $history_values[] = $updated_by;
                $history_placeholders[] = '?';
            }

            if (in_array('updated_by_type', $history_columns)) {
                $history_fields[] = 'updated_by_type';
                $history_values[] = $updated_by_type;
                $history_placeholders[] = '?';
            }

            if (in_array('admin_id', $history_columns) && $updated_by_type === 'admin' && $updated_by) {
                $history_fields[] = 'admin_id';
                $history_values[] = $updated_by;
                $history_placeholders[] = '?';
            }

            $history_sql = "INSERT INTO order_status_history (" . implode(', ', $history_fields) . ") VALUES (" . implode(', ', $history_placeholders) . ")";
            $stmt = $conn->prepare($history_sql);
            $stmt->execute($history_values);

        } catch (Exception $e) {
            // If history table doesn't exist, just log it but don't fail
            error_log("Order status history not recorded: " . $e->getMessage());
        }

        // Create notification with enhanced logo support
        try {
            $notification_created = createOrderNotification($conn, $order_id, $order['user_id'], $new_status, $notes, $tracking_number, $old_status);
            if (!$notification_created) {
                error_log("Warning: Order notification creation returned false for order {$order_id}");
            }
        } catch (Exception $e) {
            // If notification fails, just log it but don't fail the status update
            error_log("Order notification not created: " . $e->getMessage());
        }

        // Store real-time sync data for immediate UI updates
        try {
            storeRealTimeSyncData($conn, $order_id, $old_status, $new_status, $notes, $tracking_number, $estimated_delivery, $updated_by_type);
        } catch (Exception $e) {
            error_log("Real-time sync data not stored: " . $e->getMessage());
        }

        $conn->commit();
        
        // Log successful update
        error_log("Order status successfully updated: Order #{$order_id} from '{$old_status}' to '{$new_status}' by {$updated_by_type}");
        
        return true;

    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error updating order status: " . $e->getMessage());
        throw $e; // Re-throw to see the actual error
    }
}

/**
 * Create notification for order status update with logo support
 */
function createOrderNotification($conn, $order_id, $user_id, $status, $notes = '', $tracking_number = null, $old_status = null) {
    try {
        // Check if order_notifications table exists
        $stmt = $conn->query("SHOW TABLES LIKE 'order_notifications'");
        if ($stmt->rowCount() == 0) {
            // Create the table if it doesn't exist
            $sql = "
            CREATE TABLE order_notifications (
                notification_id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NOT NULL,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT,
                status_logo VARCHAR(10),
                old_status VARCHAR(50),
                new_status VARCHAR(50),
                type VARCHAR(50) DEFAULT 'status_update',
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_order_id (order_id),
                INDEX idx_is_read (is_read)
            )";
            $conn->exec($sql);
        } else {
            // Check if new columns exist, add them if not
            try {
                $stmt = $conn->query("DESCRIBE order_notifications");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (!in_array('status_logo', $columns)) {
                    $conn->exec("ALTER TABLE order_notifications ADD COLUMN status_logo VARCHAR(10) AFTER message");
                }
                if (!in_array('old_status', $columns)) {
                    $conn->exec("ALTER TABLE order_notifications ADD COLUMN old_status VARCHAR(50) AFTER status_logo");
                }
                if (!in_array('new_status', $columns)) {
                    $conn->exec("ALTER TABLE order_notifications ADD COLUMN new_status VARCHAR(50) AFTER old_status");
                }
            } catch (Exception $e) {
                error_log("Could not add new columns to order_notifications: " . $e->getMessage());
            }
        }

        $statuses = getOrderStatuses();
        $status_info = $statuses[$status] ?? ['label' => 'Status Update', 'description' => 'Order status updated', 'logo' => '📦'];
        $old_status_info = $old_status ? ($statuses[$old_status] ?? ['label' => 'Previous Status', 'logo' => '⏰']) : null;

        // Create enhanced title with logo
        $logo = $status_info['logo'] ?? '📦';
        $title = "{$logo} Pesanan #{$order_id} - " . $status_info['label'];
        
        // Create enhanced message with status transition
        $message = $status_info['description'];
        
        if ($old_status && $old_status !== $status) {
            $old_logo = $old_status_info['logo'] ?? '⏰';
            $old_label = $old_status_info['label'] ?? ucfirst($old_status);
            $message = "Status pesanan berubah dari {$old_logo} {$old_label} menjadi {$logo} " . $status_info['label'] . "\n\n" . $status_info['description'];
        }

        if ($notes) {
            $message .= "\n\n💬 Catatan dari admin: " . $notes;
        }

        if ($tracking_number) {
            $message .= "\n\n📋 Nomor Resi: " . $tracking_number;
        }

        // Add helpful information based on status
        switch ($status) {
            case ORDER_STATUS_CONFIRMED:
                $message .= "\n\n✅ Pembayaran Anda telah dikonfirmasi dan pesanan akan segera diproses.";
                break;
            case ORDER_STATUS_PROCESSING:
                $message .= "\n\n⚙️ Pesanan Anda sedang disiapkan oleh tim kami.";
                break;
            case ORDER_STATUS_SHIPPED:
                $message .= "\n\n🚚 Pesanan Anda telah dikirim dan sedang dalam perjalanan.";
                break;
            case ORDER_STATUS_DELIVERED:
                $message .= "\n\n🎉 Pesanan Anda telah berhasil diterima. Terima kasih telah berbelanja!";
                break;
            case ORDER_STATUS_CANCELLED:
            case 'dibatalkan':
                $message .= "\n\n❌ Pesanan Anda telah dibatalkan dan tidak akan diproses lebih lanjut.";
                break;
        }

        // Check if notification table has new columns
        $stmt = $conn->query("DESCRIBE order_notifications");
        $notification_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (in_array('status_logo', $notification_columns) && in_array('old_status', $notification_columns) && in_array('new_status', $notification_columns)) {
            $stmt = $conn->prepare("
                INSERT INTO order_notifications (order_id, user_id, title, message, status_logo, old_status, new_status, type)
                VALUES (?, ?, ?, ?, ?, ?, ?, 'status_update')
            ");
            $result = $stmt->execute([$order_id, $user_id, $title, $message, $logo, $old_status, $status]);
        } else {
            // Fallback for older table structure
            $stmt = $conn->prepare("
                INSERT INTO order_notifications (order_id, user_id, title, message, type)
                VALUES (?, ?, ?, ?, 'status_update')
            ");
            $result = $stmt->execute([$order_id, $user_id, $title, $message]);
        }

        if ($result) {
            error_log("Notification created successfully for order {$order_id}, user {$user_id}, status: {$old_status} -> {$status}");
        }

        return $result;

    } catch (Exception $e) {
        error_log("Error creating notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Get order status history
 */
function getOrderStatusHistory($conn, $order_id) {
    try {
        // First try with JOIN to get user names
        $stmt = $conn->prepare("
            SELECT osh.*,
                   COALESCE(u.full_name, u.username, u.email) as updated_by_name
            FROM order_status_history osh
            LEFT JOIN users u ON osh.updated_by = u.user_id
            WHERE osh.order_id = ?
            ORDER BY osh.created_at DESC
        ");
        $stmt->execute([$order_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (PDOException $e) {
        // Fallback: simple query without JOIN if updated_by column doesn't exist
        try {
            $stmt = $conn->prepare("
                SELECT *, NULL as updated_by_name
                FROM order_status_history
                WHERE order_id = ?
                ORDER BY created_at DESC
            ");
            $stmt->execute([$order_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (PDOException $e2) {
            // Last fallback: return empty array
            error_log("Error getting order status history: " . $e2->getMessage());
            return [];
        }
    }
}

/**
 * Get user notifications
 */
function getUserNotifications($conn, $user_id, $limit = 10, $unread_only = false) {
    $where_clause = "user_id = ?";
    $params = [$user_id];

    if ($unread_only) {
        $where_clause .= " AND is_read = FALSE";
    }

    // Ensure limit is integer
    $limit = (int)$limit;

    $sql = "
        SELECT * FROM order_notifications
        WHERE {$where_clause}
        ORDER BY created_at DESC
        LIMIT {$limit}
    ";

    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Mark notification as read
 */
function markNotificationAsRead($conn, $notification_id, $user_id) {
    $stmt = $conn->prepare("
        UPDATE order_notifications 
        SET is_read = TRUE 
        WHERE notification_id = ? AND user_id = ?
    ");
    return $stmt->execute([$notification_id, $user_id]);
}

/**
 * Get unread notifications count
 */
function getUnreadNotificationsCount($conn, $user_id) {
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM order_notifications 
        WHERE user_id = ? AND is_read = FALSE
    ");
    $stmt->execute([$user_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC)['count'];
}

/**
 * Get order with current status info
 */
function getOrderWithStatus($conn, $order_id) {
    $stmt = $conn->prepare("
        SELECT o.*, u.full_name as customer_name, u.email as customer_email
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.user_id
        WHERE o.order_id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($order) {
        $statuses = getOrderStatuses();
        // Check which status column exists and use it
        $current_status = $order['current_status'] ?? $order['STATUS'] ?? $order['order_status'] ?? $order['status'] ?? 'pending';
        $order['current_status'] = $current_status; // Normalize the status field
        $order['status_info'] = $statuses[$current_status] ?? $statuses[ORDER_STATUS_PENDING];
        $order['status_history'] = getOrderStatusHistory($conn, $order_id);
    }
    
    return $order;
}

/**
 * Get orders by status for admin
 */
function getOrdersByStatus($conn, $status = null, $limit = 50) {
    try {
        $where_clause = "1=1";
        $params = [];

        if ($status) {
            // Use a safer approach to check status column
            try {
                $status_check = $conn->prepare("DESCRIBE orders");
                $status_check->execute();
                $columns = $status_check->fetchAll(PDO::FETCH_COLUMN);

                if (in_array('current_status', $columns)) {
                    $where_clause = "current_status = ?";
                } elseif (in_array('STATUS', $columns)) {
                    $where_clause = "STATUS = ?";
                } elseif (in_array('order_status', $columns)) {
                    $where_clause = "order_status = ?";
                } else {
                    // Default fallback - try current_status first
                    $where_clause = "current_status = ?";
                }
                $params[] = $status;
            } catch (Exception $e) {
                // If DESCRIBE fails, use default column
                error_log("Error checking order columns: " . $e->getMessage());
                $where_clause = "current_status = ?";
                $params[] = $status;
            }
        }

        // Ensure limit is integer
        $limit = (int)$limit;
        if ($limit <= 0) $limit = 50;

        $sql = "
            SELECT o.*, u.full_name as customer_name, u.email as customer_email
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.user_id
            WHERE {$where_clause}
            ORDER BY o.order_date DESC
            LIMIT {$limit}
        ";

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);

        $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $statuses = getOrderStatuses();

        foreach ($orders as &$order) {
            // Check which status column exists and use it
            $current_status = $order['current_status'] ?? $order['STATUS'] ?? $order['order_status'] ?? $order['status'] ?? 'pending';
            $order['current_status'] = $current_status; // Normalize the status field
            $order['status_info'] = $statuses[$current_status] ?? $statuses[ORDER_STATUS_PENDING];
        }

        return $orders;

    } catch (Exception $e) {
        error_log("Error in getOrdersByStatus: " . $e->getMessage());
        return [];
    }
}

/**
 * Generate tracking number
 */
function generateTrackingNumber() {
    return 'TW' . date('Ymd') . strtoupper(substr(uniqid(), -8));
}

/**
 * Calculate estimated delivery date
 */
function calculateEstimatedDelivery($shipping_method = 'standard') {
    $days = 3; // default

    switch ($shipping_method) {
        case 'express':
            $days = 1;
            break;
        case 'standard':
            $days = 3;
            break;
        case 'economy':
            $days = 7;
            break;
    }

    return date('Y-m-d', strtotime("+{$days} days"));
}

/**
 * Mark all notifications as read for a user
 */
function markAllNotificationsAsRead($conn, $user_id) {
    $stmt = $conn->prepare("
        UPDATE order_notifications
        SET is_read = TRUE
        WHERE user_id = ? AND is_read = FALSE
    ");
    return $stmt->execute([$user_id]);
}

/**
 * Get status logo emoji
 */
function getOrderStatusLogo($status) {
    $statuses = getOrderStatuses();
    return $statuses[$status]['logo'] ?? '⏰';
}

/**
 * Generate status badge HTML with logo
 */
function getStatusBadgeWithLogo($status, $include_text = true) {
    $statuses = getOrderStatuses();
    $status_info = $statuses[$status] ?? $statuses[ORDER_STATUS_PENDING];

    $logo = $status_info['logo'];
    $color = $status_info['color'];
    $label = $status_info['label'];

    if ($include_text) {
        return "<span class='badge bg-{$color} status-badge' data-status='{$status}'>
                    <span class='status-logo'>{$logo}</span> {$label}
                </span>";
    } else {
        return "<span class='status-logo-only' data-status='{$status}' title='{$label}'>{$logo}</span>";
    }
}

/**
 * Generate status notification message with logo
 */
function getStatusNotificationMessage($old_status, $new_status, $admin_note = '') {
    $statuses = getOrderStatuses();
    $new_status_info = $statuses[$new_status] ?? $statuses[ORDER_STATUS_PENDING];

    $logo = $new_status_info['logo'];
    $label = $new_status_info['label'];

    $message = "Status pesanan Anda telah diperbarui menjadi: {$logo} {$label}";

    if ($admin_note) {
        $message .= "\n\nCatatan dari admin: {$admin_note}";
    }

    return $message;
}

/**
 * Store real-time sync data for immediate UI updates
 */
function storeRealTimeSyncData($conn, $order_id, $old_status, $new_status, $notes, $tracking_number, $estimated_delivery, $updated_by_type) {
    try {
        // Check if real_time_sync table exists
        $stmt = $conn->query("SHOW TABLES LIKE 'real_time_sync'");
        if ($stmt->rowCount() == 0) {
            // Create the table if it doesn't exist
            $sql = "
            CREATE TABLE real_time_sync (
                sync_id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NOT NULL,
                old_status VARCHAR(50),
                new_status VARCHAR(50) NOT NULL,
                admin_note TEXT,
                tracking_number VARCHAR(100),
                estimated_delivery DATE,
                updated_by_type VARCHAR(20) DEFAULT 'admin',
                sync_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed BOOLEAN DEFAULT FALSE,
                INDEX idx_order_id (order_id),
                INDEX idx_processed (processed),
                INDEX idx_timestamp (sync_timestamp)
            )";
            $conn->exec($sql);
        }

        $stmt = $conn->prepare("
            INSERT INTO real_time_sync (order_id, old_status, new_status, admin_note, tracking_number, estimated_delivery, updated_by_type)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        return $stmt->execute([
            $order_id,
            $old_status,
            $new_status,
            $notes,
            $tracking_number,
            $estimated_delivery,
            $updated_by_type
        ]);

    } catch (Exception $e) {
        error_log("Error storing real-time sync data: " . $e->getMessage());
        return false;
    }
}

/**
 * Get pending real-time sync updates for user
 */
function getPendingRealTimeSyncUpdates($conn, $user_id, $since_timestamp = null) {
    try {
        $since_timestamp = $since_timestamp ?: date('Y-m-d H:i:s', strtotime('-5 minutes'));
        
        $stmt = $conn->prepare("
            SELECT rts.*, o.user_id
            FROM real_time_sync rts
            JOIN orders o ON rts.order_id = o.order_id
            WHERE o.user_id = ? 
            AND rts.sync_timestamp > ?
            AND rts.processed = FALSE
            ORDER BY rts.sync_timestamp DESC
        ");
        
        $stmt->execute([$user_id, $since_timestamp]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Error getting real-time sync updates: " . $e->getMessage());
        return [];
    }
}

/**
 * Mark real-time sync updates as processed
 */
function markRealTimeSyncAsProcessed($conn, $sync_ids) {
    try {
        if (empty($sync_ids)) return true;
        
        $placeholders = str_repeat('?,', count($sync_ids) - 1) . '?';
        $stmt = $conn->prepare("UPDATE real_time_sync SET processed = TRUE WHERE sync_id IN ({$placeholders})");
        
        return $stmt->execute($sync_ids);
        
    } catch (Exception $e) {
        error_log("Error marking sync as processed: " . $e->getMessage());
        return false;
    }
}
?>
