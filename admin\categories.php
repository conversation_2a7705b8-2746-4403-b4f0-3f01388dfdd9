<?php
ob_start(); // Tambahkan ini di baris paling atas file
$page = 'categories';
$page_title = 'Manajemen Kategori';

// Pastikan session dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    $_SESSION['alert_type'] = 'danger';
    $_SESSION['alert_message'] = 'Anda harus login sebagai admin untuk mengakses halaman ini';
    header('Location: ../login.php');
    exit;
}

// Include header setelah cek autentikasi
require_once 'includes/header.php';

// Tambahkan CSS khusus untuk halaman kategori
?>
<style>
    .category-card {
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .category-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .category-icon {
        font-size: 2.5rem;
        color: #6c757d;
        margin-right: 15px;
    }
    .category-status {
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .category-count {
        font-size: 0.9rem;
        color: #6c757d;
    }
    .category-actions {
        display: flex;
        justify-content: flex-end;
        gap: 5px;
    }
    .category-form {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }
    .form-heading {
        border-bottom: 2px solid #0d6efd;
        padding-bottom: 10px;
        margin-bottom: 20px;
        color: #0d6efd;
    }
    .category-empty {
        text-align: center;
        padding: 50px 0;
        color: #6c757d;
    }
    .category-empty i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }
</style>
<?php

// Process form submission for adding/updating category
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get and validate input
        $category_name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $category_description = isset($_POST['description']) ? trim($_POST['description']) : '';
        $category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        // Generate slug
        $slug = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', $category_name));

        // Image upload functionality removed for simplicity

        // Validation
        if (empty($category_name)) {
            throw new Exception("Nama kategori wajib diisi");
        }

        // Check for duplicate category name
        $stmt = $conn->prepare("SELECT category_id FROM categories WHERE name = ? AND category_id != ?");
        $stmt->execute([$category_name, $category_id]);
        if ($stmt->rowCount() > 0) {
            throw new Exception("Kategori dengan nama tersebut sudah ada");
        }

        if ($category_id > 0) {
            // Update existing category
            $stmt = $conn->prepare("
                UPDATE categories
                SET name = ?,
                    description = ?
                WHERE category_id = ?
            ");
            $stmt->execute([
                $category_name,
                $category_description,
                $category_id
            ]);
            $_SESSION['alert_message'] = 'Kategori berhasil diperbarui';
            $_SESSION['alert_type'] = 'success';
            header('Location: categories.php');
            exit();
        } else {
            // Add new category
            $stmt = $conn->prepare("
                INSERT INTO categories (name, description, created_at)
                VALUES (?, ?, NOW())
            ");
            $stmt->execute([
                $category_name,
                $category_description
            ]);
            $_SESSION['alert_message'] = 'Kategori berhasil ditambahkan';
            $_SESSION['alert_type'] = 'success';
            header('Location: categories.php');
            exit();
        }

    } catch (Exception $e) {
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
}

// Process category deletion
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    try {
        $category_id = (int)$_GET['id'];

        // Check if category has products
        $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE category_id = ?");
        $stmt->execute([$category_id]);
        $product_count = $stmt->fetchColumn();

        if ($product_count > 0) {
            throw new Exception("Tidak dapat menghapus: terdapat $product_count produk dalam kategori ini");
        }

        // Delete category
        $stmt = $conn->prepare("DELETE FROM categories WHERE category_id = ?");
        $stmt->execute([$category_id]);

        $_SESSION['alert_message'] = 'Kategori berhasil dihapus';
        $_SESSION['alert_type'] = 'success';
        header('Location: categories.php');
        exit;

    } catch (Exception $e) {
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
        header('Location: categories.php');
        exit;
    }
}

// Cek dan tambahkan kategori default jika tabel kosong
$stmt = $conn->query("SELECT COUNT(*) as total FROM categories");
$row = $stmt->fetch(PDO::FETCH_ASSOC);
if ($row['total'] == 0) {
    $defaultCategories = [
        ['Cosmetics', 'cosmetics', 'Berbagai macam produk kosmetik dan perawatan kulit'],
        ['Milk Products', 'milk-products', 'Produk susu dan olahannya untuk kebutuhan sehari-hari'],
        ['Medicine', 'medicine', 'Obat-obatan dan produk kesehatan'],
        ['Sports', 'sports', 'Perlengkapan olahraga'],
        ['Vegetables', 'vegetables', 'Sayuran segar dan organik'],
    ];
    $stmtInsert = $conn->prepare("INSERT INTO categories (NAME, slug, description) VALUES (?, ?, ?)");
    foreach ($defaultCategories as $cat) {
        $stmtInsert->execute($cat);
    }
}

// Query kategori dengan alias agar field 'name' bisa diakses di PHP
try {
    $stmt = $conn->query("SELECT category_id, name, description, created_at FROM categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Fallback jika kolom 'name' tidak ada, coba 'NAME'
    try {
        $stmt = $conn->query("SELECT category_id, NAME as name, description, created_at FROM categories ORDER BY NAME");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e2) {
        $categories = [];
        $_SESSION['alert_message'] = 'Error loading categories: ' . $e2->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
}

// Get category for editing if ID is provided
$edit_category = null;
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    try {
        $category_id = (int)$_GET['id'];
        $stmt = $conn->prepare("SELECT * FROM categories WHERE category_id = ?");
        $stmt->execute([$category_id]);
        $edit_category = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$edit_category) {
            $_SESSION['alert_message'] = 'Kategori tidak ditemukan';
            $_SESSION['alert_type'] = 'warning';
            header('Location: categories.php');
            exit;
        }
    } catch (PDOException $e) {
        $_SESSION['alert_message'] = 'Database error: ' . $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
}
?>

<!-- Hapus seluruh kode layout container dan sidebar di sini, biarkan layout dari admin_header.php -->

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <?php if (isset($_SESSION['alert_message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['alert_type']; ?> alert-dismissible fade show" role="alert">
                        <?php
                            echo $_SESSION['alert_message'];
                            unset($_SESSION['alert_message']);
                            unset($_SESSION['alert_type']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-folder me-2"></i>Manajemen Kategori</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
                        <i class="fas fa-plus me-2"></i>Tambah Kategori Baru
                    </button>
                </div>

                <!-- Form Kategori -->
                <div class="category-form mb-4 shadow-sm">
                    <h4 class="form-heading">
                        <?php echo $edit_category ? 'Edit Kategori: ' . htmlspecialchars($edit_category['name'] ?? '') : 'Tambah Kategori Baru'; ?>
                    </h4>
                    <form action="" method="post" class="needs-validation" enctype="multipart/form-data" novalidate>
                        <?php if ($edit_category): ?>
                            <input type="hidden" name="category_id" value="<?php echo $edit_category['category_id']; ?>">
                        <?php endif; ?>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label required-field">Nama Kategori</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($edit_category['NAME'] ?? ''); ?>" required>
                                <div class="invalid-feedback">Nama kategori wajib diisi</div>
                            </div>

                            <div class="col-md-6">
                                <label for="parent_id" class="form-label">Kategori Induk (Opsional)</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">-- Tidak Ada / Kategori Utama --</option>
                                    <?php foreach ($categories as $category): ?>
                                        <?php if ($edit_category && $edit_category['category_id'] == $category['category_id']) continue; ?>
                                        <option value="<?php echo $category['category_id']; ?>" <?php echo ($edit_category && (($edit_category['parent_id'] ?? null) == $category['category_id'])) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['name'] ?? ''); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-12">
                                <label for="description" class="form-label">Deskripsi</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($edit_category['description'] ?? ''); ?></textarea>
                            </div>



                            <div class="col-12 mt-4">
                                <div class="d-flex justify-content-between">
                                    <?php if ($edit_category): ?>
                                        <a href="categories.php" class="btn btn-secondary">
                                            <i class="fas fa-times me-1"></i> Batal
                                        </a>
                                    <?php else: ?>
                                        <div></div>
                                    <?php endif; ?>

                                    <button type="submit" class="btn btn-success">
                                        <i class="fas <?php echo $edit_category ? 'fa-save' : 'fa-plus'; ?> me-1"></i>
                                        <?php echo $edit_category ? 'Simpan Perubahan' : 'Tambah Kategori'; ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Daftar Kategori -->
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Daftar Kategori</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($categories) > 0): ?>
                            <div class="row">
                                <?php foreach ($categories as $category): ?>
                                    <div class="col-md-4">
                                        <div class="card category-card h-100">
                                            <div class="category-status">
                                                <span class="badge bg-success">Aktif</span>
                                            </div>
                                                                                        <div class="card-body">
                                                <div class="d-flex align-items-center mb-3">
                                                    <div class="category-icon">
                                                        <i class="fas fa-folder"></i>
                                                    </div>
                                                    <div>
                                                        <h5 class="card-title mb-0"><?php echo htmlspecialchars($category['name'] ?? ''); ?></h5>
                                                        <p class="text-muted small mb-0">ID: <?php echo $category['category_id']; ?></p>

                                                        <?php
                                                        // Cek jumlah produk dalam kategori ini
                                                        $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE category_id = ?");
                                                        $stmt->execute([$category['category_id']]);
                                                        $product_count = $stmt->fetchColumn();
                                                        ?>
                                                        <p class="category-count mt-1">
                                                            <a href="products_admin.php?category=<?php echo $category['category_id']; ?>" class="text-decoration-none">
                                                                <i class="fas fa-box me-1"></i> <?php echo $product_count; ?> produk
                                                            </a>
                                                        </p>
                                                    </div>
                                                </div>

                                                <?php if (!empty($category['description'])): ?>
                                                <p class="card-text">
                                                    <?php
                                                    echo nl2br(htmlspecialchars(substr($category['description'], 0, 100)));
                                                    if (strlen($category['description']) > 100) echo '...';
                                                    ?>
                                                </p>
                                                <?php else: ?>
                                                <p class="card-text text-muted"><em>Tidak ada deskripsi</em></p>
                                                <?php endif; ?>

                                                <div class="d-flex justify-content-between align-items-center mt-3">
                                                    <small class="text-muted">Dibuat: <?php echo date('d M Y', strtotime($category['created_at'])); ?></small>
                                                </div>
                                            </div>

                                            <div class="card-footer bg-transparent">
                                                <div class="category-actions">
                                                    <a href="categories.php?action=edit&id=<?php echo $category['category_id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>

                                                    <?php if ($product_count == 0): ?>
                                                    <a href="#" onclick="confirmDelete(<?php echo $category['category_id']; ?>, '<?php echo htmlspecialchars(addslashes($category['name'])); ?>')" class="btn btn-sm btn-danger">
                                                        <i class="fas fa-trash"></i> Hapus
                                                    </a>
                                                    <?php else: ?>
                                                    <button class="btn btn-sm btn-danger" disabled title="Tidak dapat dihapus karena masih memiliki produk">
                                                        <i class="fas fa-trash"></i> Hapus
                                                    </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-5">
                                <i class="fas fa-folder-open fa-3x mb-3 text-muted"></i>
                                <p class="lead">Belum ada kategori.</p>
                                <button class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#categoryModal">
                                    <i class="fas fa-plus me-2"></i>Tambah Kategori Pertama
                                </button>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Modal (Add/Edit) -->
    <div class="modal fade" id="categoryModal" tabindex="-1" aria-labelledby="categoryModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="categoryModalLabel">
                        <?php echo $edit_category ? 'Edit Kategori' : 'Tambah Kategori Baru'; ?>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="" method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <?php if ($edit_category): ?>
                            <input type="hidden" name="category_id" value="<?php echo $edit_category['category_id']; ?>">
                        <?php endif; ?>

                        <div class="mb-3">
                            <label for="name" class="form-label required-field">Nama Kategori</label>
                            <input type="text" class="form-control" id="name" name="name"
                                value="<?php echo htmlspecialchars($edit_category['name'] ?? ''); ?>"
                                required>
                            <div class="form-text">Nama kategori akan ditampilkan ke pelanggan.</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($edit_category['description'] ?? ''); ?></textarea>
                            <div class="form-text">Deskripsi singkat tentang kategori ini (opsional).</div>
                        </div>


                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i><?php echo $edit_category ? 'Perbarui' : 'Simpan'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">Konfirmasi Hapus</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Anda yakin ingin menghapus kategori <strong id="deleteCategoryName"></strong>?</p>
                    <p class="mb-0 text-danger">Tindakan ini tidak dapat dibatalkan.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <a href="#" id="deleteLink" class="btn btn-danger">Hapus Kategori</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Function untuk konfirmasi hapus kategori
        function confirmDelete(categoryId, categoryName) {
            // Set nama kategori di modal
            document.getElementById('deleteCategoryName').textContent = categoryName;

            // Set link hapus dengan kategori ID
            document.getElementById('deleteLink').href = 'categories.php?action=delete&id=' + categoryId;

            // Tampilkan modal konfirmasi
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // Animasi untuk card kategori
        document.addEventListener('DOMContentLoaded', function() {
            // Tampilkan animasi pada kartu kategori
            const categoryCards = document.querySelectorAll('.category-card');
            categoryCards.forEach((card, index) => {
                card.style.opacity = 0;
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    card.style.opacity = 1;
                    card.style.transform = 'translateY(0)';
                }, 100 * index);
            });
        });

        // Form validation
        (function () {
            'use strict';

            // Fetch all the forms we want to apply custom Bootstrap validation styles to
            var forms = document.querySelectorAll('.needs-validation');

            // Loop over them and prevent submission
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
        })();

        // Generate slug from name with animation
        document.getElementById('name').addEventListener('input', function() {
            const nameField = this;
            const name = this.value;
            const slug = name.toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');

            // Animate the input field
            if (name.length > 0) {
                nameField.classList.add('active-input');
            } else {
                nameField.classList.remove('active-input');
            }

            // You can show the slug to the user if needed
            // document.getElementById('slug').value = slug;
        });

        // Add animation to form submission
        document.querySelector('#categoryModal form').addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // Show spinner
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Menyimpan...';

            // Add slide-out animation to modal
            document.getElementById('categoryModal').classList.add('slide-out');

            return true;
        });

        // Add animation for adding new category
        document.querySelector('[data-bs-target="#categoryModal"]').addEventListener('click', function() {
            this.classList.add('btn-pulse');
            setTimeout(() => {
                this.classList.remove('btn-pulse');
            }, 500);
        });
    </script>

    <style>
        /* Animation styles */
        #categoriesTable tbody tr {
            position: relative;
            transition: all 0.3s ease;
        }

        .highlight-delete {
            background-color: rgba(255, 0, 0, 0.1) !important;
            box-shadow: 0 0 8px rgba(255, 0, 0, 0.3);
        }

        .active-search {
            box-shadow: 0 0 8px rgba(13, 110, 253, 0.5);
            border-color: #0d6efd;
        }

        .action-buttons {
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .action-buttons-visible {
            opacity: 1;
            transform: scale(1.05);
        }

        .fade-in-modal {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .active-input {
            border-color: #198754;
            box-shadow: 0 0 5px rgba(25, 135, 84, 0.5);
            transition: all 0.3s ease;
        }

        .slide-out {
            animation: slideOut 0.5s ease forwards;
        }

        @keyframes slideOut {
            to { transform: translateY(-10px); opacity: 0.8; }
        }

        .btn-pulse {
            animation: pulse 0.5s ease;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</body>
</html>

<?php ob_end_flush(); ?>
