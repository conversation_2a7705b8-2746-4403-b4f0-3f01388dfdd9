<?php
/**
 * TEWUNEED Setup - AJAX Step Handler
 * Handles individual setup steps for the automated installation
 */

header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in JSON response

// Get the step number from POST data
$input = json_decode(file_get_contents('php://input'), true);
$step = isset($input['step']) ? (int)$input['step'] : -1;

function sendResponse($success, $message, $details = null, $error = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'details' => $details,
        'error' => $error
    ]);
    exit;
}

function checkDatabaseConnection() {
    try {
        $host = 'localhost';
        $dbname = 'db_tewuneed';
        $username = 'root';
        $password = '';
        
        $pdo = new PDO("mysql:host=$host", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if database exists
        $stmt = $pdo->query("SHOW DATABASES LIKE '$dbname'");
        $exists = $stmt->rowCount() > 0;
        
        return ['connection' => $pdo, 'db_exists' => $exists];
    } catch (PDOException $e) {
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

function executeSQLFile($pdo, $filename) {
    if (!file_exists($filename)) {
        throw new Exception("SQL file not found: $filename");
    }
    
    $sql = file_get_contents($filename);
    if ($sql === false) {
        throw new Exception("Could not read SQL file: $filename");
    }
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    $executed = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $executed++;
        } catch (PDOException $e) {
            // Skip some common errors that are not critical
            if (strpos($e->getMessage(), 'already exists') === false && 
                strpos($e->getMessage(), 'Duplicate entry') === false) {
                throw $e;
            }
        }
    }
    
    return $executed;
}

// Execute the requested step
try {
    switch ($step) {
        case 0: // System Requirements Check
            $checks = [];
            
            // PHP Version
            $phpVersion = PHP_VERSION;
            $checks[] = "PHP Version: $phpVersion " . (version_compare($phpVersion, '7.4.0', '>=') ? '✅' : '❌');
            
            // Required Extensions
            $extensions = ['pdo', 'pdo_mysql', 'json', 'session', 'mbstring'];
            foreach ($extensions as $ext) {
                $loaded = extension_loaded($ext);
                $checks[] = "Extension $ext: " . ($loaded ? '✅' : '❌');
            }
            
            // File Permissions
            $writableDirs = ['uploads/', 'cache/', 'database/'];
            foreach ($writableDirs as $dir) {
                if (!is_dir($dir)) {
                    @mkdir($dir, 0755, true);
                }
                $writable = is_writable($dir);
                $checks[] = "Directory $dir writable: " . ($writable ? '✅' : '❌');
            }
            
            sendResponse(true, 'System requirements verified', implode("\n", $checks));
            break;

        case 1: // Database Connection Test
            $dbInfo = checkDatabaseConnection();
            $details = $dbInfo['db_exists'] ? 'Database exists and connection successful' : 'Connection successful, database will be created';
            sendResponse(true, 'Database connection established', $details);
            break;

        case 2: // Create Database Structure
            $dbInfo = checkDatabaseConnection();
            $pdo = $dbInfo['connection'];
            
            // Create database if it doesn't exist
            if (!$dbInfo['db_exists']) {
                $pdo->exec("CREATE DATABASE db_tewuneed");
            }
            
            // Use the database
            $pdo->exec("USE db_tewuneed");
            
            // Execute the clean database structure
            $sqlFile = '../database/CLEAN_DATABASE_READY.sql';
            if (!file_exists($sqlFile)) {
                $sqlFile = 'database/CLEAN_DATABASE_READY.sql';
            }
            
            $executed = executeSQLFile($pdo, $sqlFile);
            sendResponse(true, 'Database structure created', "$executed SQL statements executed");
            break;

        case 3: // Insert Sample Data
            $dbInfo = checkDatabaseConnection();
            $pdo = $dbInfo['connection'];
            $pdo->exec("USE db_tewuneed");
            
            // Insert sample products
            $sqlFile = '../database/INSERT_200_PRODUCTS.sql';
            if (!file_exists($sqlFile)) {
                $sqlFile = 'database/INSERT_200_PRODUCTS.sql';
            }
            
            if (file_exists($sqlFile)) {
                $executed = executeSQLFile($pdo, $sqlFile);
                sendResponse(true, 'Sample data inserted', "$executed products and categories added");
            } else {
                sendResponse(true, 'Sample data skipped', 'Sample data file not found, continuing with basic data');
            }
            break;

        case 4: // Configure Settings
            // Check if config files exist and are readable
            $configFiles = ['config.php', 'config/database.php'];
            $configStatus = [];
            
            foreach ($configFiles as $file) {
                if (file_exists($file)) {
                    $configStatus[] = "$file: ✅ Found";
                } else {
                    $configStatus[] = "$file: ⚠️ Not found (will use defaults)";
                }
            }
            
            sendResponse(true, 'Configuration verified', implode("\n", $configStatus));
            break;

        case 5: // Test Website Functions
            $tests = [];
            
            // Test database connection
            try {
                $dbInfo = checkDatabaseConnection();
                $pdo = $dbInfo['connection'];
                $pdo->exec("USE db_tewuneed");
                
                // Test basic queries
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
                $userCount = $stmt->fetch()['count'];
                $tests[] = "Users table: $userCount records ✅";
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
                $productCount = $stmt->fetch()['count'];
                $tests[] = "Products table: $productCount records ✅";
                
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
                $categoryCount = $stmt->fetch()['count'];
                $tests[] = "Categories table: $categoryCount records ✅";
                
            } catch (Exception $e) {
                $tests[] = "Database test: ❌ " . $e->getMessage();
            }
            
            sendResponse(true, 'Website functions tested', implode("\n", $tests));
            break;

        case 6: // Setup Admin Account
            $dbInfo = checkDatabaseConnection();
            $pdo = $dbInfo['connection'];
            $pdo->exec("USE db_tewuneed");
            
            // Check if admin user exists
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
            $stmt->execute();
            $adminCount = $stmt->fetch()['count'];
            
            if ($adminCount == 0) {
                // Create admin user
                $stmt = $pdo->prepare("INSERT INTO users (username, password, email, full_name, role) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute(['admin', 'admin123', '<EMAIL>', 'Admin Tewuneed', 'admin']);
                sendResponse(true, 'Admin account created', 'Username: admin, Password: admin123');
            } else {
                sendResponse(true, 'Admin account verified', "$adminCount admin account(s) found");
            }
            break;

        case 7: // Final Verification
            $dbInfo = checkDatabaseConnection();
            $pdo = $dbInfo['connection'];
            $pdo->exec("USE db_tewuneed");
            
            $verification = [];
            
            // Check tables exist
            $tables = ['users', 'products', 'categories', 'orders', 'cart'];
            foreach ($tables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                $exists = $stmt->rowCount() > 0;
                $verification[] = "Table $table: " . ($exists ? '✅' : '❌');
            }
            
            // Check admin user
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
            $adminCount = $stmt->fetch()['count'];
            $verification[] = "Admin users: $adminCount " . ($adminCount > 0 ? '✅' : '❌');
            
            // Check products
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM products");
            $productCount = $stmt->fetch()['count'];
            $verification[] = "Products: $productCount " . ($productCount > 0 ? '✅' : '❌');
            
            sendResponse(true, 'Setup verification complete', implode("\n", $verification));
            break;

        default:
            sendResponse(false, 'Invalid step number', null, "Step $step is not defined");
            break;
    }

} catch (Exception $e) {
    sendResponse(false, 'Setup step failed', null, $e->getMessage());
}
?>
