/* Base styles */
body {
    font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    margin: 60px;
    margin-top: 6px;
    margin-bottom: 6px;
    font-weight: 470;
    background-color: #f4f4f4;
    padding: 20px;
  }
  
  /* Header and Navigation */
  header {
    background-color: white;
    border-radius: 10px;
    margin-bottom: 20px;
  }
  
  #logo {
    height: 240px;
    width: 350px;
  }
  
  nav {
    background-color: #006ca5;
    height: 10vh;
    width: 100%;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: auto;
  }
  
  a {
    position: relative;
    text-decoration: none;
    color: beige;
    font-size: 15px;
  }
  
  a::after {
    content: "";
    background-color: aliceblue;
    width: 0;
    height: 3px;
    margin: auto;
    display: block;
  }
  
  nav a:hover::after {
    width: 100%;
    transition: width 0.2s linear;
  }
  
  /* Product Grid */
  .products-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
  }
  
  /* Product Card */
  .product-card {
    display: flex;
    flex-direction: column;
    height: 400px;
    width: 250px;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 20px;
    cursor: pointer;
  }
  
  .product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  .product-image {
    height: 200px;
    width: 100%;
    overflow: hidden;
    position: relative;
  }
  
  .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .product-info {
    padding: 15px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
  }
  
  .product-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    height: 40px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .product-price {
    color: #e44d26;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .product-description {
    font-size: 14px;
    color: #666;
  }
  
  /* Buttons */
  .button {
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    display: inline-block;
    margin: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .add-to-cart {
    background-color: #2196F3;
    min-width: 160px;
  }
  
  .add-to-cart:hover {
    background-color: #0b7dda;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .pesan-sekarang {
    background-color: #4CAF50;
    min-width: 160px;
  }
  
  .pesan-sekarang:hover {
    background-color: #45a049;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .lihat-detail {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    width: 100%;
    text-align: center;
    transition: background-color 0.3s;
  }
  
  .lihat-detail:hover {
    background-color: #45a049;
  }
  
  /* Modal Styles */
  .modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 100;
    overflow: auto;
    animation: fadeIn 0.3s;
  }
  
  .modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 700px;
    position: relative;
    animation: slideIn 0.3s;
  }
  
  .close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
  }
  
  .close-modal:hover {
    color: #333;
  }
  
  /* Cart Icon */
  .cart-icon {
    position: relative;
    text-decoration: none;
    font-size: 20px;
    padding: 5px;
    display: inline-block;
    margin-right: 5px;
  }
  
  .cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: red;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
  }
  
  /* Services Section */
  #services {
    background-color: #008081;
    margin-bottom: 0;
    padding: 30px 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
  
  #services1 {
    border: #f5f5f5 solid 1px;
    border-bottom-left-radius: 40px;
    border-bottom-right-radius: 40px;
    background-color: #f5f5f5;
    color: #003152;
    font-weight: 470;
    margin-top: 0px;
    box-shadow: 0 1px 1px;
  }
  
  #services1 ol {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
  }
  
  /* Footer */
  footer {
    background-color: #dfebf6;
    border-radius: 10px;
    color: #242526;
    font-size: 14px;
  }
  
  .footer {
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    margin: auto;
    margin-top: 20px;
  }
  
  .footer_column {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  
  .footer_column a {
    color: #006ca5;
    font-size: 13px;
    transition: 0.2s;
  }
  
  .footer_column a:hover {
    font-weight: bold;
  }
  
  .footer_title p {
    display: flex;
    justify-content: center;
    text-align: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
  }
  
  /* Animations */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
  
  /* Mobile Responsiveness */
  @media screen and (max-width: 600px) {
    body {
      margin: 6px;
    }
  
    #logo {
      height: auto;
      width: 100%;
    }
  
    nav {
      flex-direction: column;
      align-items: center;
      height: 50vh;
    }
  
    .grid-container {
      display: none;
    }
  
    tr, td {
      display: block;
      width: 100%;
    }
  
    #introimage {
      display: none;
    }
  
    #services1 ol {
      display: block;
      flex: none;
    }
  
    #image {
      height: auto;
      width: 100%;
    }
  
    .footer {
      flex-direction: column;
      align-items: center;
    }
  
    .product-card {
      width: 100%;
    }
  }
  