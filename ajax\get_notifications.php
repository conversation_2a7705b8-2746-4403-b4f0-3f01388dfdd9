<?php
session_start();
require_once "../includes/db_connect.php";

header("Content-Type: application/json");

if (!isset($_SESSION["user_id"])) {
    echo json_encode(["success" => false, "message" => "Please login first"]);
    exit;
}

try {
    // Get unread notifications
    $stmt = $conn->prepare("
        SELECT * FROM notifications 
        WHERE user_id = ? AND is_read = FALSE 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$_SESSION["user_id"]]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total unread count
    $stmt = $conn->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = FALSE");
    $stmt->execute([$_SESSION["user_id"]]);
    $unread_count = $stmt->fetchColumn();
    
    echo json_encode([
        "success" => true,
        "notifications" => $notifications,
        "unread_count" => (int)$unread_count
    ]);
} catch (Exception $e) {
    echo json_encode(["success" => false, "message" => $e->getMessage()]);
}
?>