<?php
$page = 'terms';
$page_title = 'Terms and Conditions';
include('includes/header.php');
?>

<!-- Terms and Conditions Content -->
<div class="container py-5">
    <div class="row">
        <div class="col-12 mb-4">
            <h1 class="display-4 mb-4">Terms and Conditions</h1>
            <p class="lead">Last Updated: May 12, 2025</p>
            <hr class="my-4">
        </div>
    </div>

    <div class="row">
        <div class="col-lg-3 mb-4">
            <!-- Table of Contents -->
            <div class="card border-0 shadow-sm sticky-top" style="top: 100px;">
                <div class="card-header bg-white">
                    <h4 class="mb-0">Table of Contents</h4>
                </div>
                <div class="card-body">
                    <nav id="toc" class="navbar">
                        <nav class="nav nav-pills flex-column">
                            <a class="nav-link" href="#section1">1. <PERSON><PERSON> yang Boleh Menggunakan TEWUNEED</a>
                            <a class="nav-link" href="#section2">2. <PERSON> Membuat Akun</a>
                            <a class="nav-link" href="#section3">3. Berbelanja dan Pembayaran</a>
                            <a class="nav-link" href="#section4">4. Pengiriman</a>
                            <a class="nav-link" href="#section5">5. Pengembalian Barang</a>
                            <a class="nav-link" href="#section6">6. Contact Us</a>
                        </nav>
                    </nav>
                </div>
            </div>
        </div>

        <div class="col-lg-9">
            <!-- Terms Content -->
            <div class="terms-content">
                <section id="section1" class="mb-5">
                    <h2 class="border-bottom pb-2">1. Siapa yang Boleh Menggunakan TEWUNEED</h2>
                    Anda harus berusia minimal <strong>18 tahun</strong> untuk berbelanja di TEWUNEED. Jika belum 18 tahun, minta bantuan orang tua atau wali Anda.</p>
                </section>

                <section id="section2" class="mb-5">
                    <h2 class="border-bottom pb-2">2. Cara Membuat Akun</h2>
                    <p>Untuk berbelanja, Anda perlu membuat akun dengan informasi yang benar seperti:</p>
        <ul>
            <li>Nama lengkap</li>
            <li>Email yang aktif</li>
            <li>Nomor telepon</li>
            <li>Alamat pengiriman</li>
        </ul>
        <p>Jaga kerahasiaan password Anda dan jangan berikan kepada orang lain.</p>
        </section>

                <section id="section3" class="mb-5">
                    <h2 class="border-bottom pb-2">3. Berbelanja dan Pembayaran</h2>
                    <p>Saat berbelanja di TEWUNEED:</p>
        <ul>
            <li>Harga yang tertera sudah final (kecuali ada kesalahan sistem)</li>
            <li>Anda bisa bayar dengan kartu kredit, debit, atau transfer bank</li>
            <li>Pesanan akan diproses setelah pembayaran berhasil</li>
            <li>Kami berhak membatalkan pesanan jika ada indikasi penipuan</li>
        </ul>

        <div class="warning">
            <strong>Penting:</strong> Pastikan informasi pembayaran dan alamat pengiriman Anda benar sebelum menyelesaikan pesanan.
        </div>
                </section>

                <section id="section4" class="mb-5">
                    <h2 class="border-bottom pb-2">4. Pengiriman</h2>
                    <p>Waktu pengiriman tergantung lokasi Anda:</p>
        <ul>
            <li>Jakarta & sekitarnya: 1-2 hari kerja</li>
            <li>Kota besar lainnya: 2-4 hari kerja</li>
            <li>Daerah terpencil: 5-7 hari kerja</li>
        </ul>
        <p>Ongkos kirim akan dihitung otomatis saat checkout berdasarkan alamat Anda.</p>
                </section>

                <section id="section5" class="mb-5">
                    <h2 class="border-bottom pb-2">5. Pengembalian Barang</h2>
                    <p>Anda bisa mengembalikan barang dalam waktu <strong>30 hari</strong> jika:</p>
        <ul>
            <li>Barang masih dalam kondisi asli dan belum dipakai</li>
            <li>Kemasan masih lengkap dan rapi</li>
            <li>Ada bukti pembelian</li>
        </ul>
        
        <div class="highlight">
            <strong>Barang yang TIDAK bisa dikembalikan:</strong><br>
            • Makanan dan minuman<br>
            • Produk perawatan tubuh yang sudah dibuka<br>
            • Produk custom/pesanan khusus<br>
            • Produk digital (e-book, software, dll)
        </div>

        <p>Untuk mengembalikan barang, hubungi customer service kami atau gunakan fitur return di akun Anda.</p>
                </section>

                <section id="section13" class="mb-5">
                    <h2 class="border-bottom pb-2">6. Contact Us</h2>
                    <p>Jika kamu ada pertanyaan hubungi kami :</p>
                    <address>
                        <strong>TEWUNEED, Inc.</strong><br>
                        Aruan, Laguboti<br>
                        Toba, North Sumatra 22381,Indonesia<br>
                        Indonesia<br><br>
                        <i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a><br>
                        <i class="fas fa-phone"></i> +62 812 6874 2554
                    </address>
                </section>
            </div>
        </div>
    </div>
</div>

<script>
    // Highlight active section in the table of contents
    document.addEventListener('DOMContentLoaded', function() {
        const sections = document.querySelectorAll('.terms-content section');
        const navLinks = document.querySelectorAll('#toc .nav-link');
        
        function activateNavByScrollPosition() {
            let currentSection = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                
                if (window.scrollY >= sectionTop - 100) {
                    currentSection = '#' + section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === currentSection) {
                    link.classList.add('active');
                }
            });
        }
        
        window.addEventListener('scroll', activateNavByScrollPosition);
        
        // Initialize
        activateNavByScrollPosition();
        
        // Smooth scrolling for TOC links
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });
            });
        });
    });
</script>

<?php include('includes/footer.php'); ?>
