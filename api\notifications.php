<?php
require_once '../includes/autoload.php';

header('Content-Type: application/json');

// Check authentication
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user_id = $_SESSION['user_id'];
$db = get_db_connection();

switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        // Get user's notifications
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
        $offset = ($page - 1) * $limit;

        try {
            // Get total count
            $stmt = $db->prepare("
                SELECT COUNT(*) as total 
                FROM notifications 
                WHERE user_id = ? AND deleted_at IS NULL
            ");
            $stmt->execute([$user_id]);
            $total = $stmt->fetch()['total'];

            // Get notifications
            $stmt = $db->prepare("
                SELECT * 
                FROM notifications 
                WHERE user_id = ? AND deleted_at IS NULL 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$user_id, $limit, $offset]);
            $notifications = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => [
                    'notifications' => $notifications,
                    'pagination' => [
                        'total' => $total,
                        'page' => $page,
                        'limit' => $limit,
                        'pages' => ceil($total / $limit)
                    ]
                ]
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to fetch notifications']);
        }
        break;

    case 'POST':
        // Mark notifications as read
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['notification_ids']) || !is_array($data['notification_ids'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid notification IDs']);
            exit;
        }

        try {
            $stmt = $db->prepare("
                UPDATE notifications 
                SET read_at = NOW() 
                WHERE id IN (" . str_repeat('?,', count($data['notification_ids']) - 1) . "?) 
                AND user_id = ?
            ");
            
            $params = array_merge($data['notification_ids'], [$user_id]);
            $stmt->execute($params);

            echo json_encode([
                'success' => true,
                'message' => 'Notifications marked as read'
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update notifications']);
        }
        break;

    case 'DELETE':
        // Delete notifications
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['notification_ids']) || !is_array($data['notification_ids'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Invalid notification IDs']);
            exit;
        }

        try {
            $stmt = $db->prepare("
                UPDATE notifications 
                SET deleted_at = NOW() 
                WHERE id IN (" . str_repeat('?,', count($data['notification_ids']) - 1) . "?) 
                AND user_id = ?
            ");
            
            $params = array_merge($data['notification_ids'], [$user_id]);
            $stmt->execute($params);

            echo json_encode([
                'success' => true,
                'message' => 'Notifications deleted'
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete notifications']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
} 