/* Enhanced Modern Styles for TEWUNEED Website */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Variables for Consistent Design */
:root {
    /* Primary Color Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    --danger-gradient: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    
    /* Neutral Colors */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #6c757d;
    --text-dark: #2c3e50;
    --text-light: #6c757d;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 30px rgba(0,0,0,0.15);
    --shadow-xl: 0 20px 40px rgba(0,0,0,0.2);
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 15px;
    --radius-lg: 20px;
    --radius-xl: 25px;
    --radius-full: 50px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index layers */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: #fafbfc;
    overflow-x: hidden;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

/* Enhanced Button Styles */
.btn {
    font-weight: 600;
    border-radius: var(--radius-xl);
    padding: 12px 24px;
    transition: all var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-secondary {
    background: var(--secondary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
}

.btn-secondary:hover {
    box-shadow: 0 8px 25px rgba(245, 87, 108, 0.4);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-success:hover {
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid;
    border-image: var(--primary-gradient) 1;
    color: #667eea;
    position: relative;
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

/* Enhanced Card Styles */
.card {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    background: var(--white);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.card-img-top {
    transition: transform var(--transition-slow);
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Enhanced Form Styles */
.form-control {
    border-radius: var(--radius-md);
    border: 2px solid var(--medium-gray);
    padding: 12px 16px;
    transition: all var(--transition-normal);
    font-weight: 500;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

/* Enhanced Navigation */
.navbar {
    backdrop-filter: blur(20px);
    background: rgba(102, 126, 234, 0.95) !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 1rem 0;
    transition: all var(--transition-normal);
}

.navbar-brand {
    font-family: 'Poppins', sans-serif;
    font-weight: 800;
    font-size: 1.8rem;
    letter-spacing: 1px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.navbar-nav .nav-link {
    font-weight: 600;
    margin: 0 0.5rem;
    padding: 8px 16px !important;
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    position: relative;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

/* Enhanced Hero Section */
.hero-section {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9)),
                url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: white;
    padding: 120px 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.95;
    animation: fadeInUp 1s ease-out 0.2s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Product Cards */
.product-card {
    border: none;
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: var(--shadow-md);
    background: var(--white);
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.product-img {
    height: 250px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.product-card:hover .product-img {
    transform: scale(1.1);
}

.price-tag {
    background: var(--success-gradient);
    color: white;
    padding: 8px 16px;
    border-radius: var(--radius-xl);
    font-weight: 700;
    font-size: 1.1rem;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

/* Enhanced Category Cards */
.category-card {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-lg);
    height: 200px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: var(--shadow-md);
    cursor: pointer;
}

.category-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.category-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.category-card:hover .category-img {
    transform: scale(1.1);
}

.category-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover .category-overlay {
    opacity: 1;
}

/* Enhanced Section Titles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 3rem;
    position: relative;
    text-align: center;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

/* Enhanced Features Section */
.features-section {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
    padding: 80px 0;
    position: relative;
}

.feature-card {
    text-align: center;
    padding: 2.5rem;
    border-radius: var(--radius-lg);
    background: var(--white);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    height: 100%;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(102, 126, 234, 0.3);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Enhanced Dropdown Styles */
.dropdown-menu {
    border: none;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    padding: 0.5rem;
    margin-top: 10px;
    min-width: 220px;
    z-index: var(--z-dropdown);
}

.dropdown-item {
    border-radius: var(--radius-sm);
    margin: 2px 8px;
    padding: 0.75rem 1rem;
    transition: all var(--transition-normal);
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateX(5px);
}

.dropdown-item.active {
    background: var(--primary-gradient);
    color: white;
}

/* Enhanced Alert Styles */
.alert {
    border-radius: var(--radius-md);
    border: none;
    box-shadow: var(--shadow-md);
    font-weight: 500;
    padding: 1rem 1.5rem;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border-left: 4px solid #ffc107;
}

/* Enhanced Modal Styles */
.modal-content {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    background: var(--primary-gradient);
    color: white;
    border-bottom: none;
    padding: 1.5rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid var(--medium-gray);
    padding: 1.5rem;
}

/* Enhanced Badge Styles */
.badge {
    padding: 0.5em 1em;
    border-radius: var(--radius-xl);
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.badge.bg-success {
    background: var(--success-gradient) !important;
}

.badge.bg-danger {
    background: var(--danger-gradient) !important;
}

.badge.bg-warning {
    background: var(--warning-gradient) !important;
}

.badge.bg-primary {
    background: var(--primary-gradient) !important;
}

/* Enhanced Pagination */
.pagination .page-link {
    border-radius: var(--radius-xl);
    margin: 0 5px;
    border: none;
    color: #667eea;
    font-weight: 600;
    transition: all var(--transition-normal);
    padding: 10px 15px;
}

.pagination .page-item.active .page-link {
    background: var(--primary-gradient);
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.pagination .page-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    color: white;
    background: var(--primary-gradient);
}

/* Enhanced Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer h3, .footer h5 {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer .btn-outline-light {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.footer .btn-outline-light:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Enhanced Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--medium-gray);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    border: none;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    z-index: var(--z-fixed);
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-to-top:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    color: white;
}

/* Enhanced Search Box */
.search-box {
    border-radius: var(--radius-full);
    border: 2px solid var(--medium-gray);
    transition: all var(--transition-normal);
    padding: 12px 20px;
}

.search-box:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

/* Enhanced Filter Section */
.filter-section {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 2rem;
    margin: -30px 20px 40px 20px;
    position: relative;
    z-index: 10;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

/* Enhanced Results Info */
.results-info {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 5px solid #667eea;
    box-shadow: var(--shadow-sm);
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-section {
        padding: 80px 0;
    }
    
    .filter-section {
        margin: -20px 10px 30px 10px;
        padding: 1.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .feature-card {
        padding: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .product-card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .filter-section {
        padding: 1rem;
    }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
    animation: slideUp 0.6s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Utility Classes */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--primary-gradient);
}

.bg-gradient-secondary {
    background: var(--secondary-gradient);
}

.bg-gradient-success {
    background: var(--success-gradient);
}

.shadow-custom {
    box-shadow: var(--shadow-lg);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                var(--primary-gradient) border-box;
}

/* Enhanced Hover Effects */
.hover-lift {
    transition: transform var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-5px);
}

.hover-scale {
    transition: transform var(--transition-normal);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

/* Enhanced Focus States */
.form-control:focus,
.btn:focus,
.dropdown-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4c93);
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .back-to-top,
    .btn,
    .dropdown {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}