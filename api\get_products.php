<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    require_once '../config/database.php';
    
    // Check database connection
    if (!isset($pdo)) {
        throw new Exception('Database connection not established');
    }

    // Get category filter if provided
    $categoryId = isset($_GET['category_id']) ? (int)$_GET['category_id'] : null;
    
    // Base query
    $query = "
        SELECT 
            p.product_id,
            p.name,
            p.description,
            p.price,
            p.stock,
            p.image,
            p.weight,
            c.name as category_name,
            c.slug as category_slug,
            COALESCE(AVG(pr.rating), 0) as rating,
            COUNT(DISTINCT pr.review_id) as review_count,
            GROUP_CONCAT(DISTINCT pi.image_url) as additional_images
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        LEFT JOIN product_reviews pr ON p.product_id = pr.product_id
        LEFT JOIN product_images pi ON p.product_id = pi.product_id
        WHERE p.is_active = 1
    ";
    
    // Add category filter if provided
    if ($categoryId) {
        $query .= " AND p.category_id = :category_id";
    }
    
    // Group by to handle aggregations
    $query .= " GROUP BY p.product_id";
    
    // Prepare and execute query
    $stmt = $pdo->prepare($query);
    if ($categoryId) {
        $stmt->bindParam(':category_id', $categoryId);
    }
    $stmt->execute();
    
    // Fetch all products
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get base URL for images
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . $host . '/tewuneed';
    
    // Format the products data
    foreach ($products as &$product) {
        // Format price
        $product['price_formatted'] = 'Rp ' . number_format($product['price'], 0, ',', '.');
        
        // Format image URL
        if (!empty($product['image'])) {
            $product['image'] = $baseUrl . '/' . $product['image'];
        }
        
        // Format additional images
        if (!empty($product['additional_images'])) {
            $images = explode(',', $product['additional_images']);
            $product['additional_images'] = array_map(function($img) use ($baseUrl) {
                return $baseUrl . '/' . $img;
            }, $images);
        } else {
            $product['additional_images'] = [];
        }
        
        // Get active promotions for the product
        $promoStmt = $pdo->prepare("
            SELECT 
                pr.promotion_id,
                pr.name,
                pr.discount_type,
                pr.discount_value,
                pr.start_date,
                pr.end_date
            FROM promotions pr
            JOIN product_promotions pp ON pr.promotion_id = pp.promotion_id
            WHERE pp.product_id = :product_id
            AND pr.is_active = 1
            AND pr.start_date <= NOW()
            AND pr.end_date >= NOW()
        ");
        $promoStmt->execute(['product_id' => $product['product_id']]);
        $promotions = $promoStmt->fetchAll();
        
        // Calculate discounted price if there are active promotions
        if (!empty($promotions)) {
            $maxDiscount = 0;
            foreach ($promotions as $promo) {
                $discount = $promo['discount_type'] === 'percentage' 
                    ? ($product['price'] * $promo['discount_value'] / 100)
                    : $promo['discount_value'];
                $maxDiscount = max($maxDiscount, $discount);
            }
            $product['discounted_price'] = max(0, $product['price'] - $maxDiscount);
            $product['discounted_price_formatted'] = 'Rp ' . number_format($product['discounted_price'], 0, ',', '.');
            $product['promotions'] = $promotions;
        } else {
            $product['discounted_price'] = null;
            $product['discounted_price_formatted'] = null;
            $product['promotions'] = [];
        }
    }
    
    // Return success response
    echo json_encode([
        'status' => 'success',
        'data' => $products
    ]);
    
} catch (Exception $e) {
    // Log error for debugging
    error_log("Error in get_products.php: " . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Failed to fetch products: ' . $e->getMessage()
    ]);
} 