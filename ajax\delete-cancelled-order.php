<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

try {
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    $order_id = isset($input['order_id']) ? (int)$input['order_id'] : 0;
    $user_id = (int)$_SESSION['user_id'];
    
    if ($order_id <= 0) {
        throw new Exception('Invalid order ID');
    }
    
    // Check if order belongs to user and is cancelled
    $stmt = $conn->prepare("
        SELECT order_id, order_number, order_status, user_id, cancelled_at
        FROM orders 
        WHERE order_id = ? AND user_id = ?
    ");
    $stmt->execute([$order_id, $user_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        throw new Exception('Order not found or access denied');
    }
    
    // Check if order is cancelled
    if ($order['order_status'] !== 'dibatalkan') {
        throw new Exception('Only cancelled orders can be deleted');
    }
    
    // Start transaction for safe deletion
    $conn->beginTransaction();
    
    try {
        // Delete order items first (foreign key constraint)
        $delete_items_stmt = $conn->prepare("DELETE FROM order_items WHERE order_id = ?");
        $delete_items_stmt->execute([$order_id]);
        $deleted_items = $delete_items_stmt->rowCount();
        
        // Delete order status history
        $delete_history_stmt = $conn->prepare("DELETE FROM order_status_history WHERE order_id = ?");
        $delete_history_stmt->execute([$order_id]);
        $deleted_history = $delete_history_stmt->rowCount();
        
        // Delete the order itself
        $delete_order_stmt = $conn->prepare("DELETE FROM orders WHERE order_id = ? AND user_id = ?");
        $delete_order_stmt->execute([$order_id, $user_id]);
        $deleted_order = $delete_order_stmt->rowCount();
        
        if ($deleted_order === 0) {
            throw new Exception('Failed to delete order');
        }
        
        // Commit transaction
        $conn->commit();
        
        // Log successful deletion
        error_log("Order Deleted - Order ID: {$order_id}, User ID: {$user_id}, Items: {$deleted_items}, History: {$deleted_history}");
        
        echo json_encode([
            'success' => true,
            'message' => 'Cancelled order deleted successfully',
            'order_id' => $order_id,
            'order_number' => $order['order_number'] ?? "#$order_id",
            'deleted_items' => $deleted_items,
            'deleted_history' => $deleted_history
        ]);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
