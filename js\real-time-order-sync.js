/**
 * Real-time Order Status Synchronization System
 * Connects Admin Order Management with Customer Order Tracking
 */

class RealTimeOrderSync {
    constructor() {
        this.isAdmin = window.location.pathname.includes('/admin/');
        this.isCustomer = !this.isAdmin;
        this.lastCheckTime = Date.now();
        this.syncInterval = null;
        this.checkInterval = 10000; // Check every 10 seconds
        this.storageKey = 'tewuneed_order_status_sync';
        
        this.init();
    }

    init() {
        console.log(`[RealTimeOrderSync] Initializing for ${this.isAdmin ? 'Admin' : 'Customer'}`);
        
        if (this.isAdmin) {
            this.initAdminSync();
        } else {
            this.initCustomerSync();
        }
        
        this.setupStorageListener();
        this.startSyncMonitoring();
    }

    /**
     * Initialize Admin-side synchronization
     */
    initAdminSync() {
        console.log('[RealTimeOrderSync] Admin sync initialized');
        
        // Listen for order status updates in admin
        this.setupAdminOrderUpdateListeners();
        
        // Monitor for successful status updates
        this.monitorAdminStatusUpdates();
    }

    /**
     * Initialize Customer-side synchronization
     */
    initCustomerSync() {
        console.log('[RealTimeOrderSync] Customer sync initialized');
        
        // Check for real-time updates
        this.checkForOrderUpdates();
    }

    /**
     * Setup admin order update listeners
     */
    setupAdminOrderUpdateListeners() {
        // Listen for form submissions in admin order management
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[method="POST"]') && 
                e.target.querySelector('input[name="action"][value="update_status"]')) {
                
                const formData = new FormData(e.target);
                const orderId = formData.get('order_id');
                const newStatus = formData.get('status');
                const notes = formData.get('notes');
                const trackingNumber = formData.get('tracking_number');
                const estimatedDelivery = formData.get('estimated_delivery');
                
                console.log('[RealTimeOrderSync] Admin updating order:', {
                    orderId, newStatus, notes, trackingNumber, estimatedDelivery
                });
                
                // Store the update for real-time sync
                this.storeAdminUpdate(orderId, {
                    new_status: newStatus,
                    admin_note: notes,
                    tracking_number: trackingNumber,
                    estimated_delivery: estimatedDelivery,
                    timestamp: Date.now(),
                    admin_name: 'Admin'
                });
            }
        });

        // Listen for AJAX status updates
        this.interceptAjaxUpdates();
    }

    /**
     * Intercept AJAX updates for real-time sync
     */
    interceptAjaxUpdates() {
        // Override fetch for AJAX calls
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const response = await originalFetch(...args);
            
            // Check if this is an order status update
            if (args[0].includes('quick_actions.php') || args[0].includes('order_actions.php')) {
                try {
                    const clonedResponse = response.clone();
                    const data = await clonedResponse.json();
                    
                    if (data.success && data.order_id) {
                        console.log('[RealTimeOrderSync] AJAX order update detected:', data);
                        
                        this.storeAdminUpdate(data.order_id, {
                            new_status: data.new_status || data.status,
                            admin_note: data.message || 'Status updated via quick action',
                            tracking_number: data.tracking_number,
                            timestamp: Date.now(),
                            admin_name: 'Admin'
                        });
                    }
                } catch (e) {
                    // Not JSON response, ignore
                }
            }
            
            return response;
        };
    }

    /**
     * Store admin update for real-time sync
     */
    storeAdminUpdate(orderId, updateData) {
        try {
            const existingData = JSON.parse(localStorage.getItem(this.storageKey) || '{}');
            
            existingData[orderId] = {
                ...updateData,
                timestamp: Date.now(),
                processed: false
            };
            
            localStorage.setItem(this.storageKey, JSON.stringify(existingData));
            
            console.log('[RealTimeOrderSync] Stored admin update for order:', orderId, updateData);
            
            // Trigger storage event for other tabs
            window.dispatchEvent(new StorageEvent('storage', {
                key: this.storageKey,
                newValue: JSON.stringify(existingData)
            }));
            
        } catch (error) {
            console.error('[RealTimeOrderSync] Error storing admin update:', error);
        }
    }

    /**
     * Check for order updates (customer side)
     */
    async checkForOrderUpdates() {
        try {
            // Check localStorage for immediate updates
            this.processLocalStorageUpdates();
            
            // Also check database for persistent updates
            const response = await fetch('ajax/check-order-updates.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    last_check: this.lastCheckTime
                })
            });

            if (response.ok) {
                const data = await response.json();
                
                if (data.success && data.updates && data.updates.length > 0) {
                    console.log('[RealTimeOrderSync] Database updates found:', data.updates);
                    
                    data.updates.forEach(update => {
                        this.processOrderUpdate(update);
                    });
                    
                    this.lastCheckTime = Date.now();
                }
            }
            
        } catch (error) {
            console.error('[RealTimeOrderSync] Error checking for updates:', error);
        }
    }

    /**
     * Process localStorage updates (customer side)
     */
    processLocalStorageUpdates() {
        try {
            const syncData = localStorage.getItem(this.storageKey);
            if (!syncData) return;
            
            const updates = JSON.parse(syncData);
            
            Object.keys(updates).forEach(orderId => {
                const update = updates[orderId];
                
                // Only process recent, unprocessed updates
                const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
                if (update.timestamp > fiveMinutesAgo && 
                    update.timestamp > this.lastCheckTime && 
                    !update.processed) {
                    
                    console.log('[RealTimeOrderSync] Processing localStorage update for order:', orderId);
                    
                    this.processOrderUpdate({
                        order_id: orderId,
                        new_status: update.new_status,
                        admin_note: update.admin_note,
                        tracking_number: update.tracking_number,
                        estimated_delivery: update.estimated_delivery,
                        source: 'localStorage'
                    });
                    
                    // Mark as processed
                    update.processed = true;
                    localStorage.setItem(this.storageKey, JSON.stringify(updates));
                }
            });
            
        } catch (error) {
            console.error('[RealTimeOrderSync] Error processing localStorage updates:', error);
        }
    }

    /**
     * Process individual order update
     */
    processOrderUpdate(update) {
        console.log('[RealTimeOrderSync] Processing order update:', update);

        // Handle order deletion
        if (update.action === 'deleted' || update.new_status === 'deleted') {
            this.handleOrderDeletion(update);
            return;
        }

        // Update order status in UI with logo
        this.updateOrderStatusInUI(update);

        // Show enhanced notification with logo
        this.showUpdateNotification(update);

        // Update tracking information if available
        if (update.tracking_number) {
            this.updateTrackingInfo(update.order_id, update.tracking_number);
        }

        // Update estimated delivery if available
        if (update.estimated_delivery) {
            this.updateEstimatedDelivery(update.order_id, update.estimated_delivery);
        }

        // Trigger visual effects for status change
        this.triggerStatusChangeEffects(update.order_id, update.old_status, update.new_status);
    }

    /**
     * Handle order deletion with real-time notification
     */
    handleOrderDeletion(update) {
        console.log('[RealTimeOrderSync] Handling order deletion:', update);

        const orderId = update.order_id;
        const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);

        if (orderCard) {
            // Show deletion notification first
            this.showDeletionNotification(update);

            // Add deletion animation
            orderCard.style.transition = 'all 0.8s ease';
            orderCard.style.opacity = '0';
            orderCard.style.transform = 'scale(0.8) translateY(-20px)';
            orderCard.style.backgroundColor = '#ffebee';
            orderCard.style.border = '2px solid #f44336';

            // Remove from DOM after animation
            setTimeout(() => {
                orderCard.remove();

                // Check if no orders left and show appropriate message
                this.checkAndShowNoOrdersMessage();
            }, 800);
        }
    }

    /**
     * Show deletion notification
     */
    showDeletionNotification(update) {
        const notification = document.createElement('div');
        notification.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 350px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';

        const orderNumber = update.order_number || '#' + update.order_id;
        const adminName = update.admin_name || 'Administrator';

        notification.innerHTML = `
            <div class="d-flex align-items-start">
                <div class="me-3" style="font-size: 1.5em;">🗑️</div>
                <div class="flex-grow-1">
                    <strong>Pesanan ${orderNumber} Dibatalkan</strong><br>
                    <div class="mt-1">Pesanan Anda telah dibatalkan oleh ${adminName} dan dihapus dari riwayat pesanan.</div>
                    ${update.admin_note ? `<small class="text-muted mt-1 d-block">💬 ${update.admin_note}</small>` : ''}
                    <small class="text-muted mt-1 d-block">⏰ ${new Date().toLocaleString()}</small>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 10 seconds (longer for deletion)
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.add('fade');
                setTimeout(() => notification.remove(), 300);
            }
        }, 10000);

        // Play notification sound
        this.playNotificationSound();
    }

    /**
     * Check and show no orders message if needed
     */
    checkAndShowNoOrdersMessage() {
        const ordersContainer = document.getElementById('ordersContainer');
        const remainingOrders = document.querySelectorAll('[data-order-id]');

        if (ordersContainer && remainingOrders.length === 0) {
            ordersContainer.innerHTML = `
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">Tidak Ada Pesanan</h4>
                        <p class="text-muted">Semua pesanan Anda telah diproses. Mulai berbelanja untuk item baru!</p>
                        <a href="Products.php" class="btn btn-primary">
                            <i class="fas fa-shopping-cart me-2"></i>Mulai Berbelanja
                        </a>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Update order status in UI with enhanced logo support
     */
    updateOrderStatusInUI(update) {
        const orderId = update.order_id;
        
        // Update status badge with logo
        const statusBadges = document.querySelectorAll(`#status-${orderId}, [data-order-id="${orderId}"] .status-badge, .status-badge[data-status]`);
        statusBadges.forEach(statusBadge => {
            if (statusBadge.closest(`[data-order-id="${orderId}"]`) || statusBadge.id === `status-${orderId}`) {
                const statusConfig = this.getStatusConfig(update.new_status);
                const logo = update.new_status_logo || statusConfig.logo || '📦';
                
                statusBadge.className = `badge bg-${statusConfig.color} status-badge`;
                statusBadge.innerHTML = `<span class="status-logo">${logo}</span> <i class="fas ${statusConfig.icon} me-1"></i>${update.new_status_label || statusConfig.label}`;
                statusBadge.setAttribute('data-status', update.new_status);
                
                // Add animation
                statusBadge.classList.add('status-update-animation');
                setTimeout(() => statusBadge.classList.remove('status-update-animation'), 600);
            }
        });
        
        // Update standalone logo elements
        const logoElements = document.querySelectorAll(`[data-order-id="${orderId}"] .status-logo, .status-logo-${orderId}`);
        logoElements.forEach(logoElement => {
            const logo = update.new_status_logo || this.getStatusConfig(update.new_status).logo || '📦';
            logoElement.textContent = logo;
            logoElement.setAttribute('title', update.new_status_label || update.new_status);
        });
        
        // Update order card if exists
        const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);
        if (orderCard) {
            orderCard.setAttribute('data-current-status', update.new_status);
        }
        
        // Update any status text elements
        const statusTextElements = document.querySelectorAll(`[data-order-id="${orderId}"] .status-text, .order-status-${orderId}`);
        statusTextElements.forEach(element => {
            element.textContent = update.new_status_label || update.new_status;
        });
    }

    /**
     * Show enhanced update notification with logo
     */
    showUpdateNotification(update) {
        const statusConfig = this.getStatusConfig(update.new_status);
        const newLogo = update.new_status_logo || statusConfig.logo || '📦';
        const oldLogo = update.old_status_logo || '⏰';
        
        const notification = document.createElement('div');
        notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 350px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);';
        
        let statusChangeText = '';
        if (update.old_status && update.old_status !== update.new_status) {
            const oldLabel = update.old_status_label || update.old_status;
            const newLabel = update.new_status_label || statusConfig.label;
            statusChangeText = `${oldLogo} ${oldLabel} → ${newLogo} ${newLabel}`;
        } else {
            statusChangeText = `${newLogo} ${update.new_status_label || statusConfig.label}`;
        }
        
        notification.innerHTML = `
            <div class="d-flex align-items-start">
                <div class="me-3" style="font-size: 1.5em;">${newLogo}</div>
                <div class="flex-grow-1">
                    <strong>Pesanan ${update.order_number || '#' + update.order_id} Diperbarui!</strong><br>
                    <div class="mt-1">${statusChangeText}</div>
                    ${update.admin_note ? `<small class="text-muted mt-1 d-block">💬 ${update.admin_note}</small>` : ''}
                    ${update.tracking_number ? `<small class="text-muted mt-1 d-block">📋 Resi: ${update.tracking_number}</small>` : ''}
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Add entrance animation
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // Auto remove after 7 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.add('fade');
                setTimeout(() => notification.remove(), 300);
            }
        }, 7000);
        
        // Play notification sound if available
        this.playNotificationSound();
    }

    /**
     * Update tracking information
     */
    updateTrackingInfo(orderId, trackingNumber) {
        const trackingElements = document.querySelectorAll(`[data-order-id="${orderId}"] .tracking-number, .tracking-${orderId}`);
        trackingElements.forEach(element => {
            element.innerHTML = `<code>${trackingNumber}</code>`;
        });
    }

    /**
     * Update estimated delivery
     */
    updateEstimatedDelivery(orderId, estimatedDelivery) {
        const deliveryElements = document.querySelectorAll(`[data-order-id="${orderId}"] .estimated-delivery, .delivery-${orderId}`);
        deliveryElements.forEach(element => {
            const date = new Date(estimatedDelivery);
            element.textContent = date.toLocaleDateString();
        });
    }

    /**
     * Get status configuration with logo support
     */
    getStatusConfig(status) {
        const configs = {
            'pending': { label: 'Pesanan Diterima', color: 'warning', icon: 'fa-clock', logo: '⏰' },
            'confirmed': { label: 'Pesanan Dikonfirmasi', color: 'info', icon: 'fa-check', logo: '✅' },
            'processing': { label: 'Sedang Diproses', color: 'primary', icon: 'fa-cog', logo: '⚙️' },
            'packed': { label: 'Dikemas', color: 'secondary', icon: 'fa-box', logo: '📦' },
            'shipped': { label: 'Dikirim', color: 'info', icon: 'fa-truck', logo: '🚚' },
            'in_transit': { label: 'Dalam Perjalanan', color: 'primary', icon: 'fa-truck', logo: '🚛' },
            'out_for_delivery': { label: 'Sedang Diantar', color: 'warning', icon: 'fa-motorcycle', logo: '🏍️' },
            'delivered': { label: 'Terkirim', color: 'success', icon: 'fa-check-circle', logo: '✅' },
            'cancelled': { label: 'Dibatalkan', color: 'danger', icon: 'fa-times-circle', logo: '❌' },
            'returned': { label: 'Dikembalikan', color: 'dark', icon: 'fa-undo', logo: '↩️' }
        };
        
        return configs[status] || { label: status, color: 'secondary', icon: 'fa-circle', logo: '📦' };
    }

    /**
     * Trigger visual effects for status change
     */
    triggerStatusChangeEffects(orderId, oldStatus, newStatus) {
        // Add pulse effect to order card
        const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);
        if (orderCard) {
            orderCard.classList.add('order-update-pulse');
            setTimeout(() => orderCard.classList.remove('order-update-pulse'), 1000);
        }

        // Add glow effect to status badge
        const statusBadge = document.querySelector(`[data-order-id="${orderId}"] .status-badge`);
        if (statusBadge) {
            statusBadge.classList.add('status-glow');
            setTimeout(() => statusBadge.classList.remove('status-glow'), 2000);
        }

        // Scroll to order if not visible
        if (orderCard && !this.isElementInViewport(orderCard)) {
            orderCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    /**
     * Play notification sound
     */
    playNotificationSound() {
        try {
            // Create a simple notification sound using Web Audio API
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            // Fallback: no sound if Web Audio API is not supported
            console.log('Notification sound not available');
        }
    }

    /**
     * Check if element is in viewport
     */
    isElementInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Setup storage event listener for cross-tab sync
     */
    setupStorageListener() {
        window.addEventListener('storage', (e) => {
            if (e.key === this.storageKey && this.isCustomer) {
                console.log('[RealTimeOrderSync] Storage event detected, processing updates');
                this.processLocalStorageUpdates();
            }
        });
    }

    /**
     * Start sync monitoring
     */
    startSyncMonitoring() {
        this.syncInterval = setInterval(() => {
            if (this.isCustomer) {
                this.checkForOrderUpdates();
            }
        }, this.checkInterval);
        
        console.log(`[RealTimeOrderSync] Sync monitoring started (${this.checkInterval}ms interval)`);
    }

    /**
     * Stop sync monitoring
     */
    stopSyncMonitoring() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
            console.log('[RealTimeOrderSync] Sync monitoring stopped');
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.realTimeOrderSync = new RealTimeOrderSync();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (window.realTimeOrderSync) {
        window.realTimeOrderSync.stopSyncMonitoring();
    }
});
