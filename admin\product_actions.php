<?php
session_start();
require_once '../config/database.php';

// NONAKTIFKAN SEMENTARA AGAR AJAX EDIT STOK BERJALAN TANPA LOGIN
// if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
//     header('Location: admin_login.php');
//     exit;
// }

// Database connection
$conn = getConnection();

// Process stock update
if (isset($_GET['action']) && $_GET['action'] === 'update_stock') {
    try {
        $product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
        $new_stock = isset($_POST['new_stock']) ? (int)$_POST['new_stock'] : 0;
        $adjustment_type = isset($_POST['adjustment_type']) ? $_POST['adjustment_type'] : 'set'; // set, add, subtract
        $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';
        
        if ($product_id <= 0) {
            throw new Exception("ID produk tidak valid");
        }
        
        // Get current stock
        $stmt = $conn->prepare("SELECT product_id, stock, name FROM products WHERE product_id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            throw new Exception("Produk tidak ditemukan");
        }
        
        $old_stock = (int)$product['stock'];
        $final_stock = $old_stock;
        
        // Calculate final stock based on adjustment type
        switch ($adjustment_type) {
            case 'add':
                $final_stock = $old_stock + $new_stock;
                break;
            case 'subtract':
                $final_stock = $old_stock - $new_stock;
                if ($final_stock < 0) {
                    $final_stock = 0; // Prevent negative stock
                }
                break;
            case 'set':
            default:
                $final_stock = $new_stock;
                break;
        }
        
        // Begin transaction
        $conn->beginTransaction();
        
        // Update product stock
        $stmt = $conn->prepare("UPDATE products SET stock = ? WHERE product_id = ?");
        $stmt->execute([$final_stock, $product_id]);
        
        // Log the inventory change
        $stmt = $conn->prepare("
            INSERT INTO inventory_logs (
                product_id, admin_id, old_stock, new_stock, 
                adjustment, notes, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $product_id,
            $_SESSION['user_id'] ?? null,
            $old_stock,
            $final_stock,
            $final_stock - $old_stock,
            $notes
        ]);
        
        // Commit transaction
        $conn->commit();
        
        $successMsg = "Stok produk '".htmlspecialchars($product['name'])."' berhasil diperbarui dari $old_stock menjadi $final_stock";
        
        // Jika request AJAX, balas JSON
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) || (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => $successMsg]);
            exit;
        }
        // Jika bukan AJAX, redirect
        $_SESSION['alert_message'] = $successMsg;
        $_SESSION['alert_type'] = 'success';
        header('Location: products.php');
        exit;
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $errMsg = $e->getMessage();
        // Jika request AJAX, balas JSON error
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) || (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $errMsg]);
            exit;
        }
        // Jika bukan AJAX, redirect
        $_SESSION['alert_message'] = $errMsg;
        $_SESSION['alert_type'] = 'danger';
        header('Location: products.php');
        exit;
    }
}

// Process product status toggle
if (isset($_GET['action']) && $_GET['action'] === 'toggle_status') {
    try {
        $product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if ($product_id <= 0) {
            throw new Exception("ID produk tidak valid");
        }
        
        // Get current status
        $stmt = $conn->prepare("SELECT product_id, is_active, name FROM products WHERE product_id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            throw new Exception("Produk tidak ditemukan");
        }
        
        // Toggle status
        $new_status = $product['is_active'] ? 0 : 1;
        $status_text = $new_status ? 'aktif' : 'nonaktif';
        
        $stmt = $conn->prepare("UPDATE products SET is_active = ? WHERE product_id = ?");
        $stmt->execute([$new_status, $product_id]);
        
        $_SESSION['alert_message'] = "Status produk \"".htmlspecialchars($product['name'])."\" berhasil diubah menjadi $status_text";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to products page
    header('Location: products.php');
    exit;
}

// Process duplication
if (isset($_GET['action']) && $_GET['action'] === 'duplicate') {
    try {
        $product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if ($product_id <= 0) {
            throw new Exception("ID produk tidak valid");
        }
        
        // Get product data
        $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            throw new Exception("Produk tidak ditemukan");
        }
        
        // Begin transaction
        $conn->beginTransaction();
        
        // Create a copy of the product
        $new_name = $product['name'] . ' (Copy)';
        $new_slug = $product['slug'] . '-copy-' . time();
        
        $stmt = $conn->prepare("
            INSERT INTO products (
                category_id, name, slug, description, 
                price, cost_price, stock, weight, image, is_active, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()
            )
        ");
        
        $stmt->execute([
            $product['category_id'],
            $new_name,
            $new_slug,
            $product['description'],
            $product['price'],
            $product['cost_price'] ?? 0,
            $product['stock'] ?? 0,
            $product['weight'] ?? 0,
            $product['image'],
            $product['is_active'] ?? 1
        ]);
        
        $new_product_id = $conn->lastInsertId();
        
        // Copy product search index if exists
        try {
            $stmt = $conn->prepare("
                INSERT INTO product_search (product_id, name, description, category_name)
                SELECT ?, ?, description, category_name
                FROM product_search
                WHERE product_id = ?
            ");
            $stmt->execute([$new_product_id, $new_name, $product_id]);
        } catch (PDOException $e) {
            // Table might not exist, continue
        }
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['alert_message'] = "Produk \"".htmlspecialchars($product['name'])."\" berhasil diduplikasi";
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to products page
    header('Location: products.php');
    exit;
}

// Process bulk actions
if (isset($_POST['bulk_action']) && !empty($_POST['selected_products'])) {
    try {
        $bulk_action = $_POST['bulk_action'];
        $selected_products = $_POST['selected_products'];
        
        if (!is_array($selected_products) || empty($selected_products)) {
            throw new Exception("Tidak ada produk yang dipilih");
        }
        
        // Validate product IDs
        $product_ids = array_map('intval', $selected_products);
        $product_ids = array_filter($product_ids, function($id) { return $id > 0; });
        
        if (empty($product_ids)) {
            throw new Exception("Tidak ada produk valid yang dipilih");
        }
        
        // Begin transaction
        $conn->beginTransaction();
        
        $count = count($product_ids);
        $placeholders = implode(',', array_fill(0, $count, '?'));
        
        switch ($bulk_action) {
            case 'activate':
                $stmt = $conn->prepare("UPDATE products SET is_active = 1 WHERE product_id IN ($placeholders)");
                $stmt->execute($product_ids);
                $_SESSION['alert_message'] = "Berhasil mengaktifkan $count produk";
                break;
                
            case 'deactivate':
                $stmt = $conn->prepare("UPDATE products SET is_active = 0 WHERE product_id IN ($placeholders)");
                $stmt->execute($product_ids);
                $_SESSION['alert_message'] = "Berhasil menonaktifkan $count produk";
                break;
                
            case 'delete':
                // Check if products can be deleted (no associated orders)
                // Implementation depends on your order structure
                
                // Delete products
                $stmt = $conn->prepare("DELETE FROM products WHERE product_id IN ($placeholders)");
                $stmt->execute($product_ids);
                
                // Delete from search index
                try {
                    $stmt = $conn->prepare("DELETE FROM product_search WHERE product_id IN ($placeholders)");
                    $stmt->execute($product_ids);
                } catch (PDOException $e) {
                    // Table might not exist, continue
                }
                
                $_SESSION['alert_message'] = "Berhasil menghapus $count produk";
                break;
                
            case 'update_category':
                if (!isset($_POST['category_id']) || empty($_POST['category_id'])) {
                    throw new Exception("Kategori tidak dipilih");
                }
                
                $category_id = (int)$_POST['category_id'];
                
                $stmt = $conn->prepare("UPDATE products SET category_id = ? WHERE product_id IN ($placeholders)");
                $params = array_merge([$category_id], $product_ids);
                $stmt->execute($params);
                
                $_SESSION['alert_message'] = "Berhasil mengubah kategori untuk $count produk";
                break;
                
            default:
                throw new Exception("Aksi tidak dikenal");
        }
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['alert_type'] = 'success';
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
    
    // Redirect back to products page
    header('Location: products.php');
    exit;
}

// Redirect to products page if no action was taken
header('Location: products.php');
exit;