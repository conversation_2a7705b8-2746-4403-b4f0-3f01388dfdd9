/**
 * Real-time Order Status Sync CSS
 * Visual effects and animations for order status updates
 */

/* Status update animations */
.status-update-animation {
    animation: statusPulse 0.6s ease-in-out;
}

@keyframes statusPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Order card pulse effect */
.order-update-pulse {
    animation: orderPulse 1s ease-in-out;
    border: 2px solid #007bff !important;
}

@keyframes orderPulse {
    0% { 
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
        transform: scale(1.02);
    }
    100% { 
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
        transform: scale(1);
    }
}

/* Status badge glow effect */
.status-glow {
    animation: statusGlow 2s ease-in-out;
}

@keyframes statusGlow {
    0%, 100% { 
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
    }
    50% { 
        box-shadow: 0 0 20px rgba(0, 123, 255, 0.8);
    }
}

/* Status logo styling */
.status-logo {
    font-size: 1.1em;
    margin-right: 0.25rem;
    display: inline-block;
    animation: logoSpin 0.5s ease-in-out;
}

@keyframes logoSpin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.2); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Notification styling */
.alert.position-fixed {
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
    border-left: 4px solid #007bff;
}

.alert.position-fixed.show {
    transform: translateX(0);
}

/* Notification queue animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-toast {
    animation: slideInRight 0.3s ease-out;
    margin-bottom: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 8px;
    max-width: 400px;
}

.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

@keyframes fadeOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Notification container styling */
#notificationContainer {
    max-width: 400px;
    z-index: 9999;
}

/* Status badge enhancements */
.status-badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.3s ease;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Status-specific colors and effects */
.status-badge[data-status="pending"] {
    background: linear-gradient(135deg, #ffc107, #ffca2c) !important;
    color: #000 !important;
}

.status-badge[data-status="confirmed"] {
    background: linear-gradient(135deg, #17a2b8, #20c997) !important;
}

.status-badge[data-status="processing"] {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.status-badge[data-status="packed"] {
    background: linear-gradient(135deg, #6c757d, #495057) !important;
}

.status-badge[data-status="shipped"] {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
}

.status-badge[data-status="in_transit"] {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
}

.status-badge[data-status="out_for_delivery"] {
    background: linear-gradient(135deg, #fd7e14, #e55a00) !important;
}

.status-badge[data-status="delivered"] {
    background: linear-gradient(135deg, #28a745, #1e7e34) !important;
}

.status-badge[data-status="cancelled"] {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
}

.status-badge[data-status="returned"] {
    background: linear-gradient(135deg, #343a40, #23272b) !important;
}

/* Order tracking enhancements */
.order-status-timeline {
    position: relative;
    padding-left: 2rem;
}

.order-status-timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -0.5rem;
    top: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #dee2e6;
    z-index: 1;
}

.timeline-item.active::before {
    background: #007bff;
    border-color: #007bff;
    animation: timelinePulse 2s infinite;
}

@keyframes timelinePulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .alert.position-fixed {
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        min-width: auto !important;
        max-width: calc(100vw - 20px) !important;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }
    
    .status-logo {
        font-size: 1em;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .order-update-pulse {
        border-color: #0d6efd !important;
    }
    
    .alert.position-fixed {
        background-color: #212529;
        border-color: #495057;
        color: #fff;
    }
    
    .timeline-item::before {
        background: #212529;
        border-color: #495057;
    }
}

/* Print styles */
@media print {
    .alert.position-fixed,
    .status-update-animation,
    .order-update-pulse,
    .status-glow {
        display: none !important;
    }
}

/* Accessibility improvements */
.status-badge:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .status-update-animation,
    .order-update-pulse,
    .status-glow,
    .status-logo,
    .timeline-item.active::before {
        animation: none !important;
    }
    
    .alert.position-fixed {
        transition: none !important;
    }
    
    .status-badge {
        transition: none !important;
    }
}