<?php
/**
 * User Management Processing
 * Handles AJAX requests for adding, updating, and managing users
 */

// Aktifkan error reporting untuk debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// <PERSON><PERSON> sesi jika belum dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Set header untuk JSON response
header('Content-Type: application/json');

// Check if user is logged in as admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access'
    ]);
    exit;
}

// Process AJAX request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    $response = [
        'success' => false,
        'message' => 'Unknown action'
    ];
    
    try {
        $conn = getDbConnection();
        
        switch ($action) {
            case 'add':
                // Add new user
                $username = isset($_POST['username']) ? trim($_POST['username']) : '';
                $email = isset($_POST['email']) ? trim($_POST['email']) : '';
                $password = isset($_POST['password']) ? $_POST['password'] : '';
                $full_name = isset($_POST['full_name']) ? trim($_POST['full_name']) : '';
                $role = isset($_POST['role']) ? $_POST['role'] : 'user';
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                // Validation
                if (empty($username)) {
                    throw new Exception("Username is required");
                }
                
                if (empty($email)) {
                    throw new Exception("Email is required");
                }
                
                if (empty($password)) {
                    throw new Exception("Password is required");
                }
                
                // Check for duplicate username
                $stmt = $conn->prepare("SELECT user_id FROM users WHERE username = ?");
                $stmt->execute([$username]);
                if ($stmt->rowCount() > 0) {
                    throw new Exception("Username already exists");
                }
                
                // Check for duplicate email
                $stmt = $conn->prepare("SELECT user_id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->rowCount() > 0) {
                    throw new Exception("Email already exists");
                }
                
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Insert new user
                $stmt = $conn->prepare("
                    INSERT INTO users (
                        username, email, password, full_name, role, status, created_at, updated_at
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, NOW(), NOW()
                    )
                ");
                
                $status = $is_active ? 'active' : 'inactive';
                
                $stmt->execute([
                    $username,
                    $email,
                    $hashed_password,
                    $full_name,
                    $role,
                    $status
                ]);
                
                $response = [
                    'success' => true,
                    'message' => 'User successfully added',
                    'user_id' => $conn->lastInsertId()
                ];
                break;
                
            case 'update':
                // Update existing user
                $user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
                $username = isset($_POST['username']) ? trim($_POST['username']) : '';
                $email = isset($_POST['email']) ? trim($_POST['email']) : '';
                $password = isset($_POST['password']) ? $_POST['password'] : '';
                $full_name = isset($_POST['full_name']) ? trim($_POST['full_name']) : '';
                $role = isset($_POST['role']) ? $_POST['role'] : 'user';
                $is_active = isset($_POST['is_active']) ? 1 : 0;
                
                // Validation
                if (empty($user_id)) {
                    throw new Exception("User ID is required");
                }
                
                if (empty($username)) {
                    throw new Exception("Username is required");
                }
                
                if (empty($email)) {
                    throw new Exception("Email is required");
                }
                
                // Check if user exists
                $stmt = $conn->prepare("SELECT * FROM users WHERE user_id = ?");
                $stmt->execute([$user_id]);
                if ($stmt->rowCount() === 0) {
                    throw new Exception("User not found");
                }
                
                // Check for duplicate username
                $stmt = $conn->prepare("SELECT user_id FROM users WHERE username = ? AND user_id != ?");
                $stmt->execute([$username, $user_id]);
                if ($stmt->rowCount() > 0) {
                    throw new Exception("Username already exists");
                }
                
                // Check for duplicate email
                $stmt = $conn->prepare("SELECT user_id FROM users WHERE email = ? AND user_id != ?");
                $stmt->execute([$email, $user_id]);
                if ($stmt->rowCount() > 0) {
                    throw new Exception("Email already exists");
                }
                
                // Update user with or without password
                if (!empty($password)) {
                    // Update with new password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("
                        UPDATE users SET
                            username = ?,
                            email = ?,
                            password = ?,
                            full_name = ?,
                            role = ?,
                            status = ?,
                            updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    
                    $status = $is_active ? 'active' : 'inactive';
                    
                    $stmt->execute([
                        $username,
                        $email,
                        $hashed_password,
                        $full_name,
                        $role,
                        $status,
                        $user_id
                    ]);
                } else {
                    // Update without changing password
                    $stmt = $conn->prepare("
                        UPDATE users SET
                            username = ?,
                            email = ?,
                            full_name = ?,
                            role = ?,
                            status = ?,
                            updated_at = NOW()
                        WHERE user_id = ?
                    ");
                    
                    $status = $is_active ? 'active' : 'inactive';
                    
                    $stmt->execute([
                        $username,
                        $email,
                        $full_name,
                        $role,
                        $status,
                        $user_id
                    ]);
                }
                
                $response = [
                    'success' => true,
                    'message' => 'User successfully updated'
                ];
                break;
                
            case 'activate':
                // Activate user
                $user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
                
                if (empty($user_id)) {
                    throw new Exception("User ID is required");
                }
                
                $stmt = $conn->prepare("UPDATE users SET status = 'active', updated_at = NOW() WHERE user_id = ?");
                $stmt->execute([$user_id]);
                
                $response = [
                    'success' => true,
                    'message' => 'User activated successfully'
                ];
                break;
                
            case 'deactivate':
                // Deactivate user
                $user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
                
                if (empty($user_id)) {
                    throw new Exception("User ID is required");
                }
                
                // Cannot deactivate yourself
                if ($user_id == $_SESSION['user_id']) {
                    throw new Exception("You cannot deactivate your own account");
                }
                
                $stmt = $conn->prepare("UPDATE users SET status = 'inactive', updated_at = NOW() WHERE user_id = ?");
                $stmt->execute([$user_id]);
                
                $response = [
                    'success' => true,
                    'message' => 'User deactivated successfully'
                ];
                break;
                
            case 'delete':
                // Delete user
                $user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
                
                if (empty($user_id)) {
                    throw new Exception("User ID is required");
                }
                
                // Cannot delete yourself
                if ($user_id == $_SESSION['user_id']) {
                    throw new Exception("You cannot delete your own account");
                }
                
                // Check if user has orders
                $stmt = $conn->prepare("SELECT COUNT(*) FROM orders WHERE user_id = ?");
                $stmt->execute([$user_id]);
                $order_count = $stmt->fetchColumn();
                
                if ($order_count > 0) {
                    throw new Exception("Cannot delete user with orders. Deactivate instead.");
                }
                
                // Delete user
                $stmt = $conn->prepare("DELETE FROM users WHERE user_id = ?");
                $stmt->execute([$user_id]);
                
                $response = [
                    'success' => true,
                    'message' => 'User deleted successfully'
                ];
                break;
                
            default:
                throw new Exception("Unknown action: $action");
        }
        
    } catch (Exception $e) {
        $response = [
            'success' => false,
            'message' => $e->getMessage()
        ];
    }
    
    // Return JSON response
    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// Redirect to users page if accessed directly
header('Location: users.php');
exit;
