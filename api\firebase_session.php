<?php
// Start session with looser restriction to avoid potential issues
session_start([
    'cookie_samesite' => 'Lax',
    'cookie_httponly' => true
]);

// Allow CORS from any origin for development purposes
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Access-Control-Max-Age: 86400'); // 24 hours cache

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../includes/firebase_auth.php';

header('Content-Type: application/json');

// Enable error logging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't show errors to users, but log them
ini_set('log_errors', 1);
ini_set('error_log', '../logs/firebase_errors.log');

// Verify authentication request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Get the JSON data from the request
        $json = file_get_contents('php://input');
        
        if (empty($json)) {
            throw new Exception('Empty request body');
        }
        
        $data = json_decode($json, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON: ' . json_last_error_msg());
        }
        
        // Log received data for debugging (remove in production)
        error_log('Received Firebase data: ' . print_r($data, true));
        
        // Validate the data
        if (!empty($data) && isset($data['user'])) {
            // Create session from Firebase data
            $userData = $data['user'];
            $success = createSessionFromFirebase($userData);
            
            if ($success) {
                // Log session data for debugging
                error_log('Session created with ID: ' . session_id());
                error_log('Session data: ' . print_r($_SESSION, true));
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Session created successfully',
                    'session_id' => session_id(), // Include session ID for debugging
                    'user' => [
                        'uid' => $_SESSION['firebase_user_id'],
                        'displayName' => $_SESSION['user_name'],
                        'email' => $_SESSION['user_email'],
                        'role' => $_SESSION['role']
                    ]
                ]);
                exit;
            } else {
                throw new Exception('Failed to create session from Firebase data');
            }
        } else {
            throw new Exception('User data missing from request');
        }
    } catch (Exception $e) {
        // Log the error
        error_log('Firebase session error: ' . $e->getMessage());
        
        // Send detailed error in response
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            'error_details' => $e->getMessage(),
            'php_version' => PHP_VERSION,
            'session_status' => session_status()
        ]);
    }
} else {
    // Method not allowed
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
}
?>
