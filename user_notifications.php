<?php
/**
 * TEWUNEED - User Notifications Page
 * Display user notifications and order updates
 */

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/auth_check.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$page_title = 'Notifications';

// Mark notification as read if requested
if (isset($_POST['mark_read']) && isset($_POST['notification_id'])) {
    try {
        $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE notification_id = ? AND user_id = ?");
        $stmt->execute([$_POST['notification_id'], $user_id]);
    } catch (Exception $e) {
        error_log("Error marking notification as read: " . $e->getMessage());
    }
}

// Mark all notifications as read if requested
if (isset($_POST['mark_all_read'])) {
    try {
        $stmt = $conn->prepare("UPDATE notifications SET is_read = 1 WHERE user_id = ?");
        $stmt->execute([$user_id]);
    } catch (Exception $e) {
        error_log("Error marking all notifications as read: " . $e->getMessage());
    }
}

// Get user notifications
try {
    $stmt = $conn->prepare("
        SELECT n.*, 
               DATE_FORMAT(n.created_at, '%d %M %Y %H:%i') as formatted_date
        FROM notifications n 
        WHERE n.user_id = ? 
        ORDER BY n.created_at DESC 
        LIMIT 50
    ");
    $stmt->execute([$user_id]);
    $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Error fetching notifications: " . $e->getMessage());
    $notifications = [];
}

// Get unread count
try {
    $stmt = $conn->prepare("SELECT COUNT(*) as unread_count FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$user_id]);
    $unread_count = $stmt->fetch()['unread_count'] ?? 0;
} catch (Exception $e) {
    $unread_count = 0;
}

include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-bell me-2"></i>Notifications
                    </h2>
                    <p class="text-muted mb-0">
                        <?php if ($unread_count > 0): ?>
                            You have <?php echo $unread_count; ?> unread notification<?php echo $unread_count > 1 ? 's' : ''; ?>
                        <?php else: ?>
                            All notifications are read
                        <?php endif; ?>
                    </p>
                </div>
                
                <?php if ($unread_count > 0): ?>
                <form method="POST" class="d-inline">
                    <button type="submit" name="mark_all_read" class="btn btn-outline-primary">
                        <i class="fas fa-check-double me-1"></i>Mark All Read
                    </button>
                </form>
                <?php endif; ?>
            </div>

            <!-- Notifications List -->
            <?php if (empty($notifications)): ?>
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Notifications</h5>
                    <p class="text-muted">You don't have any notifications yet.</p>
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-home me-1"></i>Go to Homepage
                    </a>
                </div>
            </div>
            <?php else: ?>
            <div class="notifications-list">
                <?php foreach ($notifications as $notification): ?>
                <div class="card mb-3 <?php echo $notification['is_read'] ? '' : 'border-primary'; ?>">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <div class="notification-icon me-3">
                                        <?php
                                        $icon_class = 'fas fa-info-circle text-info';
                                        $type = $notification['type'] ?? 'info';
                                        
                                        switch ($type) {
                                            case 'order':
                                                $icon_class = 'fas fa-shopping-bag text-success';
                                                break;
                                            case 'payment':
                                                $icon_class = 'fas fa-credit-card text-warning';
                                                break;
                                            case 'shipping':
                                                $icon_class = 'fas fa-truck text-primary';
                                                break;
                                            case 'system':
                                                $icon_class = 'fas fa-cog text-secondary';
                                                break;
                                            case 'promotion':
                                                $icon_class = 'fas fa-tag text-danger';
                                                break;
                                        }
                                        ?>
                                        <i class="<?php echo $icon_class; ?> fa-lg"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-1 <?php echo $notification['is_read'] ? 'text-muted' : 'text-dark fw-bold'; ?>">
                                            <?php echo htmlspecialchars($notification['title']); ?>
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo $notification['formatted_date']; ?>
                                        </small>
                                    </div>
                                </div>
                                
                                <p class="mb-2 <?php echo $notification['is_read'] ? 'text-muted' : 'text-dark'; ?>">
                                    <?php echo nl2br(htmlspecialchars($notification['message'])); ?>
                                </p>
                                
                                <?php if (!$notification['is_read']): ?>
                                <form method="POST" class="d-inline">
                                    <input type="hidden" name="notification_id" value="<?php echo $notification['notification_id']; ?>">
                                    <button type="submit" name="mark_read" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-check me-1"></i>Mark as Read
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                            
                            <?php if (!$notification['is_read']): ?>
                            <div class="notification-badge">
                                <span class="badge bg-primary rounded-pill">New</span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- Back Button -->
            <div class="text-center mt-4">
                <a href="profile.php" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-user me-1"></i>Back to Profile
                </a>
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home me-1"></i>Homepage
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.notifications-list .card {
    transition: all 0.3s ease;
}

.notifications-list .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.notification-icon {
    width: 40px;
    text-align: center;
}

.notification-badge {
    min-width: 60px;
    text-align: center;
}

.border-primary {
    border-left: 4px solid #0d6efd !important;
}
</style>

<script>
// Auto-refresh notifications every 30 seconds
setInterval(function() {
    // Only refresh if there are unread notifications
    <?php if ($unread_count > 0): ?>
    location.reload();
    <?php endif; ?>
}, 30000);

// Mark notification as read when clicked
document.addEventListener('DOMContentLoaded', function() {
    const notificationCards = document.querySelectorAll('.notifications-list .card');
    
    notificationCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking on buttons
            if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                return;
            }
            
            const markReadForm = card.querySelector('form[method="POST"]');
            if (markReadForm && markReadForm.querySelector('input[name="notification_id"]')) {
                markReadForm.submit();
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
