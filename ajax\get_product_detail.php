<?php
require_once "../includes/db_connect.php";

header("Content-Type: application/json");

$product_id = $_GET["product_id"] ?? 0;

try {
    $stmt = $conn->prepare("
        SELECT p.*, c.name as category_name,
               COALESCE(prs.average_rating, 0) as average_rating,
               COALESCE(prs.total_reviews, 0) as total_reviews
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        LEFT JOIN product_rating_summary prs ON p.product_id = prs.product_id
        WHERE p.product_id = ?
    ");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        echo json_encode(["success" => false, "message" => "Product not found"]);
        exit;
    }
    
    echo json_encode([
        "success" => true,
        "product" => $product
    ]);
} catch (Exception $e) {
    echo json_encode(["success" => false, "message" => $e->getMessage()]);
}
?>