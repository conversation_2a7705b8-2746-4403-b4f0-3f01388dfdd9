<?php
ob_start();
// File header admin - digunakan setelah pengecekan autentikasi di admin_auth.php

// Set active page if not already set
if (!isset($active_page)) {
    $active_page = '';
}

// Set page title if not already set
if (!isset($page_title)) {
    $page_title = 'Admin Dashboard';
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - TewuNeed Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- jQuery -->    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Admin Core JavaScript -->
    <script src="/tewuneed2/admin/js/admin-core.js"></script>
    <style>
        .sidebar {
            background-color: #212529;
            color: white;
            min-height: 100vh;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin-bottom: 0.25rem;
        }
        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar .nav-link.active {
            color: white;
            background-color: #0d6efd;
        }
        .sidebar .nav-link i {
            margin-right: 0.5rem;
            width: 1.25rem;
            text-align: center;
        }
        .order-progress .progress-step .step-icon {
            width: 3rem;
            height: 3rem;
            background-color: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 0.5rem;
            font-size: 1.25rem;
            color: #6c757d;
            position: relative;
            z-index: 2;
        }
        .order-progress .progress-step.active .step-icon {
            background-color: #0d6efd;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0 sidebar">
                <div class="p-3 text-center mb-3">
                    <h5>TewuNeed Admin</h5>
                </div>
                <div class="px-3">
                    <nav class="nav flex-column">
                        <a class="nav-link <?php echo $active_page === 'dashboard' ? 'active' : ''; ?>" href="dashboard.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                        <a class="nav-link <?php echo $active_page === 'realtime' ? 'active' : ''; ?>" href="realtime_hub.php">
                            <i class="fas fa-broadcast-tower"></i> Real-time Hub
                        </a>
                        <a class="nav-link <?php echo $active_page === 'products' ? 'active' : ''; ?>" href="products_admin.php">
                            <i class="fas fa-cube"></i> Products
                        </a>
                        <a class="nav-link <?php echo $active_page === 'categories' ? 'active' : ''; ?>" href="categories.php">
                            <i class="fas fa-tags"></i> Categories
                        </a>
                        <a class="nav-link <?php echo $active_page === 'order_management' ? 'active' : ''; ?>" href="order_management.php">
                            <i class="fas fa-truck"></i> Order Management
                        </a>
                        <a class="nav-link <?php echo $active_page === 'users' ? 'active' : ''; ?>" href="users.php">
                            <i class="fas fa-users"></i> Users
                        </a>
                        <a class="nav-link <?php echo $active_page === 'reports' ? 'active' : ''; ?>" href="reports.php">
                            <i class="fas fa-chart-line"></i> Reports
                        </a>
                        <a class="nav-link <?php echo $active_page === 'settings' ? 'active' : ''; ?>" href="settings.php">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                        <a class="nav-link" href="admin_logout.php">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3"><?php echo $page_title; ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                            <?php if ($active_page !== 'dashboard'): ?>
                            <li class="breadcrumb-item active"><?php echo ucfirst($active_page); ?></li>
                            <?php endif; ?>
                        </ol>
                    </nav>
                </div>
                
                <?php if (isset($_SESSION['alert_message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['alert_type']; ?> alert-dismissible fade show mb-4">
                    <?php 
                        echo $_SESSION['alert_message']; 
                        unset($_SESSION['alert_message']);
                        unset($_SESSION['alert_type']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
