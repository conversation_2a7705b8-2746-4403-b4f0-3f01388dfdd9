<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

try {
    $conn = getConnection();
    
    // Get featured products with their current promotions
    $stmt = $conn->prepare("
        SELECT 
            p.product_id,
            p.name,
            p.description,
            p.price,
            p.stock,
            p.image,
            c.name as category_name,
            COALESCE(
                MIN(
                    CASE 
                        WHEN pr.discount_type = 'percentage' 
                        THEN p.price * (1 - pr.discount_value/100)
                        ELSE p.price - pr.discount_value
                    END
                ),
                p.price
            ) as final_price
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        LEFT JOIN product_promotions pp ON p.product_id = pp.product_id
        LEFT JOIN promotions pr ON pp.promotion_id = pr.promotion_id
            AND pr.is_active = 1
            AND pr.start_date <= CURRENT_TIMESTAMP
            AND pr.end_date >= CURRENT_TIMESTAMP
        WHERE p.is_active = 1
        AND p.stock > 0
        GROUP BY p.product_id
        ORDER BY p.created_at DESC
        LIMIT 8
    ");
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format prices and add currency symbol
    foreach ($products as &$product) {
        $product['price_formatted'] = 'Rp ' . number_format($product['price'], 0, ',', '.');
        $product['final_price_formatted'] = 'Rp ' . number_format($product['final_price'], 0, ',', '.');
        $product['has_discount'] = $product['price'] > $product['final_price'];
    }
    
    echo json_encode([
        'status' => 'success',
        'data' => $products
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 