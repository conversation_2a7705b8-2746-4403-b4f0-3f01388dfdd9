<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';
require_once '../includes/StockReservation.php';

// Set response header to JSON
header('Content-Type: application/json');

// Verify that the user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Silakan login untuk mengubah keranjang belanja'
    ]);
    exit;
}

// Validate product_id and quantity
if (!isset($_POST['product_id']) || !is_numeric($_POST['product_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'ID produk tidak valid'
    ]);
    exit;
}

$product_id = (int)$_POST['product_id'];
$quantity = isset($_POST['quantity']) && is_numeric($_POST['quantity']) ? (int)$_POST['quantity'] : 1;

if ($quantity <= 0) {
    echo json_encode([
        'success' => false,
        'message' => 'Jumlah harus lebih dari nol'
    ]);
    exit;
}

try {
    global $conn;
    
    // Begin transaction for data consistency
    $conn->beginTransaction();
    
    // Initialize stock reservation system
    $stockReservation = new StockReservation($conn);

    // Check if product exists and has enough stock
    $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ? AND is_active = 1");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        throw new Exception('Produk tidak tersedia');
    }

    // Check available stock (actual stock minus reserved stock)
    $availableStock = $stockReservation->getAvailableStock($product_id);
    if ($quantity > $availableStock) {
        throw new Exception("Stok tidak mencukupi. Stok tersedia: {$availableStock}");
    }
    
    // Get user's cart id
    $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$cart) {
        throw new Exception('Keranjang belanja tidak ditemukan');
    }
    
    $cart_id = $cart['cart_id'];

    // Get current cart item quantity to calculate difference
    $stmt = $conn->prepare("SELECT quantity FROM cart_items WHERE cart_id = ? AND product_id = ?");
    $stmt->execute([$cart_id, $product_id]);
    $currentItem = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$currentItem) {
        throw new Exception('Item tidak ditemukan di keranjang');
    }

    $currentQuantity = $currentItem['quantity'];
    $quantityDifference = $quantity - $currentQuantity;

    // Handle stock reservation based on quantity change
    if ($quantityDifference > 0) {
        // Increasing quantity - reserve additional stock
        $reservationResult = $stockReservation->reserveStock($product_id, $quantityDifference, $_SESSION['user_id']);
        if (!$reservationResult['success']) {
            throw new Exception('Gagal mereservasi stok tambahan: ' . $reservationResult['message']);
        }
    } elseif ($quantityDifference < 0) {
        // Decreasing quantity - cancel some reservation
        $cancelQuantity = abs($quantityDifference);
        $cancelResult = $stockReservation->cancelReservation($product_id, $_SESSION['user_id'], null, $cancelQuantity);
        if (!$cancelResult['success']) {
            error_log("Warning: Could not cancel reservation for product {$product_id}: " . $cancelResult['message']);
            // Continue anyway, as the cart item should still be updated
        }
    }

    // Update cart item quantity
    $stmt = $conn->prepare("
        UPDATE cart_items
        SET quantity = ?
        WHERE cart_id = ? AND product_id = ?
    ");
    $stmt->execute([$quantity, $cart_id, $product_id]);

    if ($stmt->rowCount() === 0) {
        throw new Exception('Gagal mengupdate item di keranjang');
    }
    
    // Commit transaction
    $conn->commit();
    
    // Log activity
    logActivity($_SESSION['user_id'], 'cart_update', "Updated quantity of product ID: {$product_id} in cart to {$quantity}");
    
    echo json_encode([
        'success' => true,
        'message' => 'Jumlah produk berhasil diperbarui',
        'product_id' => $product_id,
        'quantity' => $quantity
    ]);
    
} catch (Exception $e) {
    // Rollback transaction in case of error
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
