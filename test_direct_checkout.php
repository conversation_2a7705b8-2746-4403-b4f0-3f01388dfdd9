<?php
/**
 * Direct Checkout Test
 * This file directly tests the checkout process without the complex UI
 */

session_start();
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id']) && !isset($_SESSION['firebase_user_id'])) {
    echo "<h2>❌ User not logged in</h2>";
    echo "<p><a href='login.php'>Please login first</a></p>";
    exit;
}

$user_id = $_SESSION['local_user_id'] ?? $_SESSION['user_id'];

echo "<h2>🧪 Direct Checkout Test</h2>";

// Check if this is a POST request (form submission)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>📤 Processing POST Request...</h3>";
    
    // Display submitted data
    echo "<h4>Submitted Data:</h4>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    // Simulate the checkout process
    try {
        // Validate required fields
        $required_fields = ['shipping_name', 'shipping_email', 'shipping_phone', 'shipping_address', 'payment_method'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Missing field: " . $field);
            }
        }
        
        echo "<p style='color: green;'>✅ All required fields present</p>";
        
        // Validate email
        if (!filter_var($_POST['shipping_email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception("Invalid email address");
        }
        echo "<p style='color: green;'>✅ Email validation passed</p>";
        
        // Validate phone
        if (!preg_match('/^[0-9]{10,15}$/', $_POST['shipping_phone'])) {
            throw new Exception("Invalid phone number");
        }
        echo "<p style='color: green;'>✅ Phone validation passed</p>";
        
        // Get cart items
        $cart_id = getOrCreateCart($user_id);
        if (!$cart_id) {
            throw new Exception("Could not get cart");
        }
        
        $stmt = $conn->prepare("
            SELECT ci.cart_item_id, ci.product_id, ci.quantity,
                   COALESCE(p.name, p.NAME) as name, p.price, p.image, p.stock
            FROM cart_items ci
            JOIN products p ON ci.product_id = p.product_id
            WHERE ci.cart_id = ?
        ");
        $stmt->execute([$cart_id]);
        $cart_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($cart_items) === 0) {
            throw new Exception("Cart is empty");
        }
        
        echo "<p style='color: green;'>✅ Cart has " . count($cart_items) . " items</p>";
        
        // Calculate totals
        $subtotal = 0;
        foreach ($cart_items as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }
        
        $shipping_cost = 10000; // Default shipping
        $total = $subtotal + $shipping_cost;
        
        echo "<p style='color: blue;'>💰 Subtotal: Rp " . number_format($subtotal) . "</p>";
        echo "<p style='color: blue;'>🚚 Shipping: Rp " . number_format($shipping_cost) . "</p>";
        echo "<p style='color: blue;'>💳 Total: Rp " . number_format($total) . "</p>";
        
        // Test order creation (without actually creating)
        $order_number = generateOrderNumber();
        echo "<p style='color: green;'>✅ Order number generated: " . $order_number . "</p>";
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>🎉 Checkout Test SUCCESSFUL!</h4>";
        echo "<p>All validations passed. The checkout process would work correctly.</p>";
        echo "<p><strong>Next step:</strong> The actual checkout.php should redirect to payment page.</p>";
        echo "</div>";
        
        // Show what would happen next
        echo "<h4>🔄 What happens next in real checkout:</h4>";
        echo "<ol>";
        echo "<li>Create order in database</li>";
        echo "<li>Add order items</li>";
        echo "<li>Confirm stock reservations</li>";
        echo "<li>Clear cart</li>";
        echo "<li>Redirect to payment page</li>";
        echo "</ol>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>❌ Test Failed</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
} else {
    // Show the test form
    echo "<p>This form will test the checkout process directly.</p>";
    
    // Get cart info for display
    try {
        $cart_id = getOrCreateCart($user_id);
        $stmt = $conn->prepare("
            SELECT ci.cart_item_id, ci.product_id, ci.quantity,
                   COALESCE(p.name, p.NAME) as name, p.price
            FROM cart_items ci
            JOIN products p ON ci.product_id = p.product_id
            WHERE ci.cart_id = ?
        ");
        $stmt->execute([$cart_id]);
        $cart_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($cart_items) > 0) {
            echo "<h4>🛒 Current Cart Items:</h4>";
            echo "<ul>";
            foreach ($cart_items as $item) {
                echo "<li>{$item['name']} - Qty: {$item['quantity']} - Rp " . number_format($item['price']) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>⚠️ Cart is Empty</h4>";
            echo "<p>Please <a href='index.php'>add some products to cart</a> before testing checkout.</p>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error getting cart: " . $e->getMessage() . "</p>";
    }
    
    ?>
    
    <form method="POST" style="max-width: 600px; margin: 20px 0;">
        <h4>📝 Test Checkout Form</h4>
        
        <div style="margin-bottom: 15px;">
            <label>Full Name *</label><br>
            <input type="text" name="shipping_name" value="Test User" required style="width: 100%; padding: 8px;">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>Email *</label><br>
            <input type="email" name="shipping_email" value="<EMAIL>" required style="width: 100%; padding: 8px;">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>Phone *</label><br>
            <input type="text" name="shipping_phone" value="081234567890" required style="width: 100%; padding: 8px;">
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>Address *</label><br>
            <textarea name="shipping_address" required style="width: 100%; padding: 8px; height: 80px;">Test Address 123, Test City</textarea>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>Shipping Method</label><br>
            <select name="shipping_method" style="width: 100%; padding: 8px;">
                <option value="standard">Standard (Rp 10.000)</option>
                <option value="express">Express (Rp 25.000)</option>
            </select>
        </div>
        
        <div style="margin-bottom: 15px;">
            <label>Payment Method *</label><br>
            <select name="payment_method" required style="width: 100%; padding: 8px;">
                <option value="bank_transfer">Bank Transfer</option>
                <option value="cod">Cash on Delivery</option>
                <option value="qris">QRIS</option>
                <option value="virtual_account">Virtual Account</option>
            </select>
        </div>
        
        <button type="submit" style="background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer;">
            🧪 Test Checkout Process
        </button>
    </form>
    
    <hr>
    <h4>🔗 Quick Links</h4>
    <p>
        <a href="checkout.php" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;">Real Checkout</a>
        <a href="debug_checkout.php" style="background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;">Debug Tool</a>
        <a href="cart.php" style="background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 3px;">View Cart</a>
    </p>
    
    <?php
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #333;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
}

ul, ol {
    padding-left: 20px;
}

li {
    margin: 5px 0;
}
</style>
