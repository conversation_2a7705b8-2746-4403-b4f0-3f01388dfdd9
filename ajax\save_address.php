<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    $address_id = $_POST['address_id'] ?? null; // For editing existing address

    // Get form data
    $label = trim($_POST['label'] ?? '');
    $recipient_name = trim($_POST['recipient_name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $city = trim($_POST['city'] ?? '');
    $province = $_POST['province'] ?? '';
    $postal_code = trim($_POST['postal_code'] ?? '');
    $is_default = isset($_POST['is_default']) ? 1 : 0;

    // Validate required fields
    if (empty($label) || empty($recipient_name) || empty($phone) ||
        empty($address) || empty($city) || empty($province) || empty($postal_code)) {
        echo json_encode(['success' => false, 'message' => 'All fields are required']);
        exit;
    }

    // Validate phone number
    $phone_digits = preg_replace('/\D/', '', $phone);
    if (strlen($phone_digits) < 10 || strlen($phone_digits) > 15) {
        echo json_encode(['success' => false, 'message' => 'Phone number must be between 10-15 digits']);
        exit;
    }

    // Validate postal code (Indonesian postal code format)
    if (!preg_match('/^\d{5}$/', $postal_code)) {
        echo json_encode(['success' => false, 'message' => 'Postal code must be 5 digits']);
        exit;
    }

    // Check if this is an edit or new address
    $is_edit = !empty($address_id);

    if ($is_edit) {
        // Verify address belongs to user
        $stmt = $conn->prepare("SELECT * FROM user_addresses WHERE address_id = ? AND user_id = ?");
        $stmt->execute([$address_id, $user_id]);
        $existing_address = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$existing_address) {
            echo json_encode(['success' => false, 'message' => 'Address not found']);
            exit;
        }
    } else {
        // Check if user already has 5 addresses (limit for new addresses)
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_addresses WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $address_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($address_count >= 5) {
            echo json_encode(['success' => false, 'message' => 'You can only have maximum 5 addresses']);
            exit;
        }
    }

    // Start transaction
    $conn->beginTransaction();

    // If this is set as default, remove default from other addresses
    if ($is_default) {
        if ($is_edit) {
            $stmt = $conn->prepare("UPDATE user_addresses SET is_default = 0 WHERE user_id = ? AND address_id != ?");
            $stmt->execute([$user_id, $address_id]);
        } else {
            $stmt = $conn->prepare("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?");
            $stmt->execute([$user_id]);
        }
    } else {
        // If this is the first address, make it default
        if (!$is_edit && ($address_count ?? 0) == 0) {
            $is_default = 1;
        }
    }

    if ($is_edit) {
        // Update existing address
        $stmt = $conn->prepare("
            UPDATE user_addresses
            SET label = ?, recipient_name = ?, phone = ?, address = ?,
                city = ?, province = ?, postal_code = ?, is_default = ?, updated_at = NOW()
            WHERE address_id = ? AND user_id = ?
        ");

        $result = $stmt->execute([
            $label,
            $recipient_name,
            $phone,
            $address,
            $city,
            $province,
            $postal_code,
            $is_default,
            $address_id,
            $user_id
        ]);
    } else {
        // Insert new address
        $stmt = $conn->prepare("
            INSERT INTO user_addresses
            (user_id, label, recipient_name, phone, address, city, province, postal_code, is_default, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");

        $result = $stmt->execute([
            $user_id,
            $label,
            $recipient_name,
            $phone,
            $address,
            $city,
            $province,
            $postal_code,
            $is_default
        ]);

        if ($result) {
            $address_id = $conn->lastInsertId();
        }
    }

    if ($result) {

        // Commit transaction
        $conn->commit();

        // Log activity
        $activity_type = $is_edit ? 'address_update' : 'address_add';
        $activity_description = $is_edit ? "Updated address: {$label}" : "Added new address: {$label}";

        $activity_stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, description, created_at)
            VALUES (?, ?, ?, NOW())
        ");
        $activity_stmt->execute([
            $user_id,
            $activity_type,
            $activity_description
        ]);

        echo json_encode([
            'success' => true,
            'message' => $is_edit ? 'Address updated successfully' : 'Address saved successfully',
            'address_id' => $address_id,
            'is_edit' => $is_edit
        ]);
    } else {
        $conn->rollBack();
        echo json_encode(['success' => false, 'message' => 'Failed to save address']);
    }

} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    error_log("Save address error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while saving address']);
}
?>
