<?php
require_once '../includes/autoload.php';

// Enable CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

// Handle different request methods
switch ($method) {
    case 'POST':
        if (!isset($data['action'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Action is required']);
            break;
        }

        switch ($data['action']) {
            case 'login':
                if (!isset($data['email']) || !isset($data['password'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Email and password are required']);
                    break;
                }

                try {
                    $db = get_db_connection();
                    $stmt = $db->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
                    $stmt->execute([$data['email']]);
                    $user = $stmt->fetch();

                    if ($user && password_verify($data['password'], $user['password'])) {
                        // Generate session token
                        $token = bin2hex(random_bytes(32));
                        
                        // Store token in database
                        $stmt = $db->prepare("UPDATE users SET session_token = ?, last_login = NOW() WHERE id = ?");
                        $stmt->execute([$token, $user['id']]);

                        // Set session
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['user_role'] = $user['role'];
                        $_SESSION['session_token'] = $token;

                        echo json_encode([
                            'success' => true,
                            'message' => 'Login successful',
                            'data' => [
                                'user' => [
                                    'id' => $user['id'],
                                    'name' => $user['name'],
                                    'email' => $user['email'],
                                    'role' => $user['role']
                                ],
                                'token' => $token
                            ]
                        ]);
                    } else {
                        http_response_code(401);
                        echo json_encode(['success' => false, 'message' => 'Invalid email or password']);
                    }
                } catch (PDOException $e) {
                    http_response_code(500);
                    echo json_encode(['success' => false, 'message' => 'Database error']);
                }
                break;

            case 'register':
                if (!isset($data['name']) || !isset($data['email']) || !isset($data['password'])) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Name, email and password are required']);
                    break;
                }

                if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Invalid email format']);
                    break;
                }

                if (strlen($data['password']) < 8) {
                    http_response_code(400);
                    echo json_encode(['success' => false, 'message' => 'Password must be at least 8 characters']);
                    break;
                }

                try {
                    $db = get_db_connection();
                    
                    // Check if email already exists
                    $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
                    $stmt->execute([$data['email']]);
                    if ($stmt->fetch()) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'message' => 'Email already registered']);
                        break;
                    }

                    // Create new user
                    $stmt = $db->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, 'user', 'active')");
                    $result = $stmt->execute([
                        $data['name'],
                        $data['email'],
                        password_hash($data['password'], PASSWORD_DEFAULT)
                    ]);

                    if ($result) {
                        $user_id = $db->lastInsertId();
                        echo json_encode([
                            'success' => true,
                            'message' => 'Registration successful',
                            'data' => ['user_id' => $user_id]
                        ]);
                    } else {
                        throw new PDOException('Failed to create user');
                    }
                } catch (PDOException $e) {
                    http_response_code(500);
                    echo json_encode(['success' => false, 'message' => 'Failed to register user']);
                }
                break;

            case 'logout':
                if (isset($_SESSION['user_id'])) {
                    try {
                        $db = get_db_connection();
                        $stmt = $db->prepare("UPDATE users SET session_token = NULL WHERE id = ?");
                        $stmt->execute([$_SESSION['user_id']]);
                    } catch (PDOException $e) {
                        // Log error but continue with logout
                    }
                }

                session_destroy();
                echo json_encode(['success' => true, 'message' => 'Logged out successfully']);
                break;

            default:
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
                break;
        }
        break;

    case 'GET':
        if (isset($_SESSION['user_id'])) {
            try {
                $db = get_db_connection();
                $stmt = $db->prepare("SELECT id, name, email, role FROM users WHERE id = ? AND status = 'active'");
                $stmt->execute([$_SESSION['user_id']]);
                $user = $stmt->fetch();

                if ($user) {
                    echo json_encode([
                        'success' => true,
                        'data' => [
                            'user' => $user,
                            'is_logged_in' => true
                        ]
                    ]);
                } else {
                    session_destroy();
                    echo json_encode([
                        'success' => true,
                        'data' => ['is_logged_in' => false]
                    ]);
                }
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Database error']);
            }
        } else {
            echo json_encode([
                'success' => true,
                'data' => ['is_logged_in' => false]
            ]);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}
?> 