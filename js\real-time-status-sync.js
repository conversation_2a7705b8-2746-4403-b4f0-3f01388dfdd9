/**
 * Real-time Order Status Synchronization System
 * Handles dynamic logo updates and notifications
 */

class RealTimeStatusSync {
    constructor() {
        this.pollingInterval = 10000; // 10 seconds (reduced frequency)
        this.isPolling = false;
        this.lastUpdateTime = Date.now();
        this.currentOrderId = null;
        this.notificationSound = null;
        this.processedNotifications = new Set(); // Track processed notifications
        this.maxRetries = 3;
        this.retryCount = 0;
        this.init();
    }

    init() {
        console.log('Initializing Real-time Status Sync...');
        this.setupNotificationSound();
        this.startPolling();
        this.bindEvents();
        this.checkForUpdates();
    }

    setupNotificationSound() {
        // Create notification sound (optional)
        try {
            this.notificationSound = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
        } catch (e) {
            console.log('Notification sound not available');
        }
    }

    startPolling() {
        if (this.isPolling) return;
        
        this.isPolling = true;
        this.pollForUpdates();
    }

    stopPolling() {
        this.isPolling = false;
    }

    async pollForUpdates() {
        if (!this.isPolling) return;

        try {
            await this.checkForUpdates();
            this.retryCount = 0; // Reset retry count on success
        } catch (error) {
            console.error('Error polling for updates:', error);
            this.retryCount++;

            // If too many retries, increase polling interval
            if (this.retryCount >= this.maxRetries) {
                this.pollingInterval = Math.min(this.pollingInterval * 1.5, 30000); // Max 30 seconds
                console.warn('Increased polling interval due to errors:', this.pollingInterval);
            }
        }

        // Schedule next poll
        setTimeout(() => this.pollForUpdates(), this.pollingInterval);
    }

    async checkForUpdates() {
        try {
            const response = await fetch('ajax/get_order_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    last_update: this.lastUpdateTime
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.success && data.updates && data.updates.length > 0) {
                this.processUpdates(data.updates);
                this.lastUpdateTime = Date.now();
            }

            // Check for notifications
            if (data.notifications && data.notifications.length > 0) {
                this.processNotifications(data.notifications);
            }

        } catch (error) {
            console.error('Error checking for updates:', error);
        }
    }

    processUpdates(updates) {
        updates.forEach(update => {
            this.updateOrderStatus(update);
            this.showStatusChangeNotification(update);
        });
    }

    updateOrderStatus(update) {
        const orderId = update.order_id;
        const newStatus = update.new_status;
        const statusInfo = update.status_info;

        // Update status badge in order list
        const statusBadge = document.querySelector(`tr[data-order-id="${orderId}"] .status-badge`);
        if (statusBadge) {
            // Add animation class
            statusBadge.classList.add('status-updating');
            
            setTimeout(() => {
                // Update badge content
                statusBadge.className = `badge bg-${statusInfo.color} status-badge`;
                statusBadge.setAttribute('data-status', newStatus);
                statusBadge.innerHTML = `
                    <span class="status-logo">${statusInfo.logo}</span>
                    <i class="${statusInfo.icon} me-1"></i>
                    ${statusInfo.label}
                `;
                
                // Remove animation class
                statusBadge.classList.remove('status-updating');
                statusBadge.classList.add('status-updated');
                
                // Remove updated class after animation
                setTimeout(() => {
                    statusBadge.classList.remove('status-updated');
                }, 1000);
            }, 300);
        }

        // Update tracking number if provided
        if (update.tracking_number) {
            const trackingCell = document.querySelector(`tr[data-order-id="${orderId}"] .tracking-number`);
            if (trackingCell) {
                trackingCell.innerHTML = `<code>${update.tracking_number}</code>`;
            }
        }

        // Update statistics if on admin page
        this.updateStatistics();
    }

    showStatusChangeNotification(update) {
        // Disable status change notifications to reduce annoyance
        return;

        const statusInfo = update.status_info;
        const message = `${statusInfo.logo} Pesanan #${update.order_id} - ${statusInfo.label}`;

        this.showNotification(message, 'info', {
            icon: statusInfo.logo,
            autoHide: true,
            duration: 5000
        });

        // Play notification sound
        if (this.notificationSound) {
            this.notificationSound.play().catch(e => console.log('Could not play sound'));
        }
    }

    processNotifications(notifications) {
        notifications.forEach(notification => {
            const notificationId = notification.notification_id || `${notification.title}-${notification.created_at}`;

            // Check if we've already processed this notification
            if (!this.processedNotifications.has(notificationId)) {
                this.processedNotifications.add(notificationId);
                this.showUserNotification(notification);
            }
        });
    }

    showUserNotification(notification) {
        // Show notification in notification center
        this.addToNotificationCenter(notification);

        // Disable popup notifications to reduce annoyance
        // Add to notification queue instead of showing immediately
        // if (!document.hidden && this.processedNotifications.size < 10) {
        //     this.addToNotificationQueue({
        //         title: notification.title,
        //         type: 'info',
        //         message: notification.message,
        //         icon: notification.status_logo,
        //         autoHide: true,
        //         duration: 4000,
        //         actions: [
        //             {
        //                 text: 'Lihat Detail',
        //                 action: () => this.viewOrderDetails(notification.order_id)
        //             },
        //             {
        //                 text: 'Tandai Dibaca',
        //                 action: () => this.markNotificationRead(notification.notification_id)
        //             }
        //         ]
        //     });
        // }
    }

    // Notification queue system to prevent overwhelming users
    addToNotificationQueue(notificationData) {
        if (!this.notificationQueue) {
            this.notificationQueue = [];
            this.isProcessingQueue = false;
            this.lastNotificationTime = 0;
        }

        this.notificationQueue.push(notificationData);

        if (!this.isProcessingQueue) {
            this.processNotificationQueue();
        }
    }

    async processNotificationQueue() {
        if (this.notificationQueue.length === 0) {
            this.isProcessingQueue = false;
            return;
        }

        this.isProcessingQueue = true;

        // Calculate delay based on time since last notification
        const now = Date.now();
        const timeSinceLastNotification = now - this.lastNotificationTime;
        const minimumDelay = 2000; // 2 seconds minimum between notifications

        if (timeSinceLastNotification < minimumDelay) {
            const waitTime = minimumDelay - timeSinceLastNotification;
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        const notificationData = this.notificationQueue.shift();

        // Show the notification
        this.showNotification(notificationData.title, notificationData.type, notificationData);
        this.lastNotificationTime = Date.now();

        // Process next notification after a short delay
        setTimeout(() => {
            this.processNotificationQueue();
        }, 1000);
    }

    addToNotificationCenter(notification) {
        const notificationCenter = document.getElementById('notificationCenter');
        if (!notificationCenter) return;

        const notificationElement = document.createElement('div');
        notificationElement.className = 'notification-item unread';
        notificationElement.setAttribute('data-notification-id', notification.notification_id);
        
        notificationElement.innerHTML = `
            <div class="notification-header">
                <span class="notification-logo">${notification.status_logo}</span>
                <span class="notification-title">${notification.title}</span>
                <span class="notification-time">${this.formatTime(notification.created_at)}</span>
            </div>
            <div class="notification-message">${notification.message}</div>
            <div class="notification-actions">
                <button class="btn btn-sm btn-outline-primary" onclick="realTimeSync.viewOrderDetails(${notification.order_id})">
                    Lihat Detail
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="realTimeSync.markNotificationRead(${notification.notification_id})">
                    Tandai Dibaca
                </button>
            </div>
        `;

        notificationCenter.insertBefore(notificationElement, notificationCenter.firstChild);
        
        // Update notification count
        this.updateNotificationCount();
    }

    async markNotificationRead(notificationId) {
        try {
            const response = await fetch('ajax/mark_notification_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    notification_id: notificationId
                })
            });

            const data = await response.json();
            
            if (data.success) {
                const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
                if (notificationElement) {
                    notificationElement.classList.remove('unread');
                    notificationElement.classList.add('read');
                }
                this.updateNotificationCount();
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }

    updateNotificationCount() {
        const unreadCount = document.querySelectorAll('.notification-item.unread').length;
        const countBadge = document.getElementById('notificationCount');
        if (countBadge) {
            countBadge.textContent = unreadCount;
            countBadge.style.display = unreadCount > 0 ? 'inline' : 'none';
        }
    }

    showNotification(title, type = 'info', options = {}) {
        // Check if too many notifications are already visible
        const existingNotifications = document.querySelectorAll('.notification-toast');
        if (existingNotifications.length >= 3) {
            // Remove oldest notification to make room
            existingNotifications[0].remove();
        }

        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show notification-toast`;
        notification.style.cssText = 'animation: slideInRight 0.3s ease-out; margin-bottom: 10px;';

        let content = `
            <div class="d-flex align-items-center">
                ${options.icon ? `<span class="me-2 fs-4">${options.icon}</span>` : ''}
                <div class="flex-grow-1">
                    <strong>${title}</strong>
                    ${options.message ? `<div class="small">${options.message}</div>` : ''}
                </div>
            </div>
        `;

        if (options.actions && options.actions.length > 0) {
            content += '<div class="mt-2">';
            options.actions.forEach((action, index) => {
                const actionId = `action_${Date.now()}_${index}`;
                content += `<button class="btn btn-sm btn-outline-light me-2" id="${actionId}">${action.text}</button>`;
            });
            content += '</div>';
        }

        content += '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';

        notification.innerHTML = content;

        // Add to notification container
        let container = document.getElementById('notificationContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }

        // Add event listeners for action buttons
        if (options.actions && options.actions.length > 0) {
            options.actions.forEach((action, index) => {
                const actionId = `action_${Date.now()}_${index}`;
                setTimeout(() => {
                    const button = document.getElementById(actionId);
                    if (button) {
                        button.addEventListener('click', action.action);
                    }
                }, 100);
            });
        }

        container.appendChild(notification);

        // Auto-hide if specified
        if (options.autoHide !== false) {
            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, options.duration || 3000);
        }
    }

    updateStatistics() {
        // Update dashboard statistics if on admin page
        if (window.location.pathname.includes('admin')) {
            // This would be called to refresh statistics
            // Implementation depends on specific admin dashboard structure
        }
    }

    viewOrderDetails(orderId) {
        // Redirect to order details or open modal
        if (typeof viewOrder === 'function') {
            viewOrder(orderId);
        } else {
            window.location.href = `order-detail.php?id=${orderId}`;
        }
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // Less than 1 minute
            return 'Baru saja';
        } else if (diff < 3600000) { // Less than 1 hour
            return `${Math.floor(diff / 60000)} menit yang lalu`;
        } else if (diff < 86400000) { // Less than 1 day
            return `${Math.floor(diff / 3600000)} jam yang lalu`;
        } else {
            return date.toLocaleDateString('id-ID');
        }
    }

    bindEvents() {
        // Bind to form submissions for immediate feedback
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[method="POST"]') && e.target.querySelector('input[name="action"][value="update_status"]')) {
                // Show immediate feedback
                const orderId = e.target.querySelector('input[name="order_id"]').value;
                const newStatus = e.target.querySelector('select[name="status"]').value;
                
                this.showNotification('Memperbarui status pesanan...', 'info', {
                    icon: '⏳',
                    autoHide: true,
                    duration: 2000
                });
            }
        });

        // Handle page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopPolling();
            } else {
                this.startPolling();
                this.checkForUpdates(); // Immediate check when page becomes visible
            }
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.realTimeSync = new RealTimeStatusSync();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealTimeStatusSync;
}
