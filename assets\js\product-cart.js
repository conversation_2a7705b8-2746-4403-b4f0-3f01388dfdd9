/**
 * Product Cart Handler - For adding products to cart from product pages
 */
document.addEventListener('DOMContentLoaded', function() {
    // Find all Add to Cart buttons
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    
    // Add event listeners
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get product details
            const productId = parseInt(this.getAttribute('data-product-id'));
            const productName = this.getAttribute('data-product-name');
            const productPrice = parseInt(this.getAttribute('data-product-price'));
            const productImage = this.getAttribute('data-product-image') || 'default-product.jpg';
            
            // Disable button and show loading
            const originalText = this.innerHTML;
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Menambahkan...';
            
            // Get current cart
            let cart = JSON.parse(localStorage.getItem('tewuneed_cart') || '[]');
            
            // Check if product already exists in cart
            const existingItemIndex = cart.findIndex(item => item.product_id === productId);
            
            if (existingItemIndex >= 0) {
                // Update quantity if product exists
                cart[existingItemIndex].quantity += 1;
            } else {
                // Add new item
                cart.push({
                    product_id: productId,
                    name: productName,
                    price: productPrice,
                    quantity: 1,
                    image: productImage
                });
            }
            
            // Save cart
            localStorage.setItem('tewuneed_cart', JSON.stringify(cart));
            
            // Update cart count
            updateCartCount();
            
            // Show success message
            showAlert('success', `<i class="bi bi-check-circle-fill"></i> ${productName} telah ditambahkan ke keranjang.`);
            
            // Reset button with success indication
            setTimeout(() => {
                this.disabled = false;
                this.innerHTML = '<i class="bi bi-check-circle-fill"></i> Ditambahkan';
                
                // Reset button text after delay
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            }, 500);
        });
    });
    
    // Update cart count in header
    function updateCartCount() {
        const cart = JSON.parse(localStorage.getItem('tewuneed_cart') || '[]');
        const count = cart.reduce((total, item) => total + item.quantity, 0);
        
        const countBadge = document.querySelector('.cart-count');
        if (countBadge) {
            countBadge.textContent = count;
            countBadge.style.display = count > 0 ? 'inline-block' : 'none';
        }
    }
    
    // Show alert message
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const alertContainer = document.getElementById('alert-container');
        if (alertContainer) {
            alertContainer.innerHTML = alertHtml;
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => {
                        alertContainer.innerHTML = '';
                    }, 150);
                }
            }, 5000);
        }
    }
    
    // Initialize cart count
    updateCartCount();
});
