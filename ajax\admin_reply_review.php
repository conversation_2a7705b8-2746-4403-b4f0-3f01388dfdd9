<?php
session_start();
require_once "../includes/db_connect.php";

header("Content-Type: application/json");

if (!isset($_SESSION["user_id"]) || $_SESSION["role"] !== "admin") {
    echo json_encode(["success" => false, "message" => "Unauthorized access"]);
    exit;
}

$review_id = $_POST["review_id"] ?? 0;
$reply_text = trim($_POST["reply_text"] ?? "");

if (empty($reply_text)) {
    echo json_encode(["success" => false, "message" => "Reply text is required"]);
    exit;
}

try {
    // Add admin reply to review
    $stmt = $conn->prepare("
        UPDATE product_reviews 
        SET admin_reply = ?, admin_reply_date = NOW(), admin_id = ?
        WHERE review_id = ?
    ");
    $stmt->execute([$reply_text, $_SESSION["user_id"], $review_id]);
    
    if ($stmt->rowCount() > 0) {
        echo json_encode([
            "success" => true,
            "message" => "Admin reply added successfully",
            "reply_text" => $reply_text,
            "reply_date" => date("Y-m-d H:i:s")
        ]);
    } else {
        echo json_encode(["success" => false, "message" => "Review not found"]);
    }
} catch (Exception $e) {
    echo json_encode(["success" => false, "message" => $e->getMessage()]);
}
?>