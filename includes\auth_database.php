<?php
/**
 * Authentication Database Setup
 * Creates necessary tables for the authentication system if they don't exist
 */

require_once __DIR__ . '/db_connect.php';

/**
 * Set up the necessary authentication tables
 * @return bool True if successful
 */
function setupAuthTables() {
    try {
        global $conn;
        
        // Check if admin_activity_log table exists
        $tableExists = false;
        $result = $conn->query("SHOW TABLES LIKE 'admin_activity_log'");
        $tableExists = $result->rowCount() > 0;
        
        if (!$tableExists) {
            // Create admin_activity_log table
            $conn->exec("
                CREATE TABLE admin_activity_log (
                    log_id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    action VARCHAR(50) NOT NULL,
                    details TEXT,
                    ip_address VARCHAR(45),
                    created_at DATETIME NOT NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
        }
        
        // Check if auth_tokens table exists
        $tableExists = false;
        $result = $conn->query("SHOW TABLES LIKE 'auth_tokens'");
        $tableExists = $result->rowCount() > 0;
        
        if (!$tableExists) {
            // Create auth_tokens table
            $conn->exec("
                CREATE TABLE auth_tokens (
                    token_id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    token_hash VARCHAR(255) NOT NULL,
                    expires_at DATETIME NOT NULL,
                    created_at DATETIME NOT NULL,
                    last_used_at DATETIME,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    UNIQUE KEY (user_id, token_hash(191))
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
        }
        
        // Check if login_attempts table exists
        $tableExists = false;
        $result = $conn->query("SHOW TABLES LIKE 'login_attempts'");
        $tableExists = $result->rowCount() > 0;
        
        if (!$tableExists) {
            // Create login_attempts table
            $conn->exec("
                CREATE TABLE login_attempts (
                    attempt_id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(255),
                    ip_address VARCHAR(45) NOT NULL,
                    attempt_time DATETIME NOT NULL,
                    success TINYINT(1) DEFAULT 0,
                    INDEX (ip_address),
                    INDEX (username(191))
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
        }
        
        return true;
    } catch (PDOException $e) {
        error_log('Database setup error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Check and record login attempt for potential brute force attacks
 * @param string $username Username attempted
 * @param string $ip IP address
 * @param bool $success Whether login was successful
 * @return bool True if too many failed attempts, false otherwise
 */
function checkLoginAttempts($username, $ip, $success = false) {
    try {
        global $conn;
        
        // Always record this attempt
        $stmt = $conn->prepare("
            INSERT INTO login_attempts (username, ip_address, attempt_time, success)
            VALUES (?, ?, NOW(), ?)
        ");
        $stmt->execute([$username, $ip, $success ? 1 : 0]);
        
        if ($success) {
            return false; // Successful login, no need to check for brute force
        }
        
        // Check if too many failed attempts (5 in 15 minutes)
        $stmt = $conn->prepare("
            SELECT COUNT(*) as attempts
            FROM login_attempts
            WHERE (username = ? OR ip_address = ?)
            AND success = 0
            AND attempt_time > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
        ");
        $stmt->execute([$username, $ip]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return ($result['attempts'] >= 5); // Return true if too many attempts
    } catch (PDOException $e) {
        error_log('Login attempt check error: ' . $e->getMessage());
        return false; // Error occurred, don't lock out the user
    }
}

/**
 * Reset login attempts after successful login
 * @param string $username Username
 * @param string $ip IP address
 */
function resetLoginAttempts($username, $ip) {
    try {
        global $conn;
        
        $stmt = $conn->prepare("
            UPDATE login_attempts
            SET success = 1
            WHERE (username = ? OR ip_address = ?)
            AND attempt_time > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        $stmt->execute([$username, $ip]);
    } catch (PDOException $e) {
        error_log('Reset login attempts error: ' . $e->getMessage());
    }
}

// Run the setup automatically when this file is included
setupAuthTables();
