<?php
// Set page variables for header
$page = 'wishlist';
$page_title = 'My Wishlist';

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['redirect_after_login'] = 'wishlist.php';
    header('Location: login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$success_message = '';
$error_message = '';

// Handle wishlist actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $product_id = intval($_POST['product_id'] ?? 0);

    try {
        if ($action === 'remove' && $product_id > 0) {
            // Remove from wishlist
            $stmt = $conn->prepare("DELETE FROM wishlist WHERE user_id = ? AND product_id = ?");
            $stmt->execute([$user_id, $product_id]);
            $success_message = 'Product removed from wishlist';
        } elseif ($action === 'add_to_cart' && $product_id > 0) {
            // Add to cart
            $stmt = $conn->prepare("
                INSERT INTO cart (user_id, product_id, quantity, created_at) 
                VALUES (?, ?, 1, NOW()) 
                ON DUPLICATE KEY UPDATE quantity = quantity + 1, updated_at = NOW()
            ");
            $stmt->execute([$user_id, $product_id]);
            $success_message = 'Product added to cart';
        }
    } catch (Exception $e) {
        $error_message = 'Error processing request: ' . $e->getMessage();
    }
}

// Get wishlist items
try {
    $stmt = $conn->prepare("
        SELECT w.*, p.name, p.price, p.image, p.stock, p.is_active,
               c.name as category_name
        FROM wishlist w
        JOIN products p ON w.product_id = p.product_id
        LEFT JOIN categories c ON p.category_id = c.category_id
        WHERE w.user_id = ?
        ORDER BY w.created_at DESC
    ");
    $stmt->execute([$user_id]);
    $wishlist_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error_message = 'Error loading wishlist: ' . $e->getMessage();
    $wishlist_items = [];
}

// Include header
include('includes/header.php');
?>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="mb-0">
                    <i class="fas fa-heart text-danger me-2"></i>My Wishlist
                </h1>
                <span class="badge bg-primary fs-6"><?php echo count($wishlist_items); ?> items</span>
            </div>

            <!-- Display Messages -->
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (empty($wishlist_items)): ?>
                <!-- Empty Wishlist -->
                <div class="text-center py-5">
                    <i class="fas fa-heart fa-4x text-muted mb-4"></i>
                    <h3 class="text-muted mb-3">Your Wishlist is Empty</h3>
                    <p class="text-muted mb-4">Save your favorite products to your wishlist and shop them later!</p>
                    <a href="Products.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                    </a>
                </div>
            <?php else: ?>
                <!-- Wishlist Items -->
                <div class="row">
                    <?php foreach ($wishlist_items as $item): ?>
                        <div class="col-md-6 col-lg-4 col-xl-3 mb-4">
                            <div class="card h-100 wishlist-card" style="border-radius: 15px; overflow: hidden;">
                                <!-- Product Image -->
                                <div class="position-relative">
                                    <img src="uploads/<?php echo htmlspecialchars($item['image']); ?>" 
                                         class="card-img-top" 
                                         alt="<?php echo htmlspecialchars($item['name']); ?>"
                                         style="height: 200px; object-fit: cover;">
                                    
                                    <!-- Remove from Wishlist Button -->
                                    <form method="POST" class="position-absolute top-0 end-0 m-2">
                                        <input type="hidden" name="action" value="remove">
                                        <input type="hidden" name="product_id" value="<?php echo $item['product_id']; ?>">
                                        <button type="submit" class="btn btn-danger btn-sm rounded-circle" 
                                                title="Remove from wishlist"
                                                onclick="return confirm('Remove this item from wishlist?')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </form>

                                    <!-- Stock Status -->
                                    <?php if ($item['stock'] <= 0): ?>
                                        <div class="position-absolute bottom-0 start-0 m-2">
                                            <span class="badge bg-danger">Out of Stock</span>
                                        </div>
                                    <?php elseif ($item['stock'] <= 5): ?>
                                        <div class="position-absolute bottom-0 start-0 m-2">
                                            <span class="badge bg-warning">Low Stock</span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="card-body d-flex flex-column">
                                    <!-- Category -->
                                    <small class="text-muted mb-1">
                                        <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($item['category_name']); ?>
                                    </small>

                                    <!-- Product Name -->
                                    <h6 class="card-title mb-2">
                                        <a href="product-detail.php?id=<?php echo $item['product_id']; ?>" 
                                           class="text-decoration-none text-dark">
                                            <?php echo htmlspecialchars($item['name']); ?>
                                        </a>
                                    </h6>

                                    <!-- Price -->
                                    <div class="mb-3">
                                        <span class="h5 text-primary mb-0">
                                            Rp <?php echo number_format($item['price'], 0, ',', '.'); ?>
                                        </span>
                                    </div>

                                    <!-- Stock Info -->
                                    <small class="text-muted mb-3">
                                        <i class="fas fa-box me-1"></i>
                                        <?php echo $item['stock']; ?> in stock
                                    </small>

                                    <!-- Action Buttons -->
                                    <div class="mt-auto">
                                        <?php if ($item['stock'] > 0 && $item['is_active']): ?>
                                            <form method="POST" class="d-grid gap-2">
                                                <input type="hidden" name="action" value="add_to_cart">
                                                <input type="hidden" name="product_id" value="<?php echo $item['product_id']; ?>">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <button class="btn btn-secondary w-100" disabled>
                                                <i class="fas fa-ban me-2"></i>Unavailable
                                            </button>
                                        <?php endif; ?>

                                        <a href="product-detail.php?id=<?php echo $item['product_id']; ?>" 
                                           class="btn btn-outline-primary w-100 mt-2">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a>
                                    </div>
                                </div>

                                <!-- Added Date -->
                                <div class="card-footer bg-light">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        Added <?php echo date('M j, Y', strtotime($item['created_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Wishlist Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center py-4">
                                <h5 class="mb-3">
                                    <i class="fas fa-shopping-bag me-2"></i>Ready to Shop?
                                </h5>
                                <p class="text-muted mb-3">
                                    Continue shopping to discover more amazing products!
                                </p>
                                <a href="Products.php" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-plus me-2"></i>Continue Shopping
                                </a>
                                <a href="cart.php" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-shopping-cart me-2"></i>View Cart
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.wishlist-card {
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.wishlist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.wishlist-card .card-img-top {
    transition: transform 0.3s ease;
}

.wishlist-card:hover .card-img-top {
    transform: scale(1.05);
}

.btn-danger.btn-sm.rounded-circle {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    background: rgba(220, 53, 69, 0.9) !important;
}

.btn-danger.btn-sm.rounded-circle:hover {
    background: rgba(220, 53, 69, 1) !important;
    transform: scale(1.1);
}

.badge {
    backdrop-filter: blur(10px);
}

.card-footer {
    border-top: 1px solid rgba(0,0,0,0.1);
}

/* Loading animation for buttons */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .wishlist-card {
        margin-bottom: 1rem;
    }
    
    .btn-lg {
        font-size: 1rem;
        padding: 0.5rem 1rem;
    }
}
</style>

<script>
// Add loading state to form submissions
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function() {
        const submitBtn = this.querySelector('button[type="submit"]');
        if (submitBtn && !submitBtn.disabled) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            submitBtn.disabled = true;
            
            // Re-enable after 3 seconds as fallback
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        }
    });
});

// Add smooth scroll to top when page loads
window.addEventListener('load', function() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
});

// Add confirmation for remove actions
document.querySelectorAll('form input[value="remove"]').forEach(input => {
    const form = input.closest('form');
    form.addEventListener('submit', function(e) {
        if (!confirm('Are you sure you want to remove this item from your wishlist?')) {
            e.preventDefault();
        }
    });
});
</script>

<?php include('includes/footer.php'); ?>
