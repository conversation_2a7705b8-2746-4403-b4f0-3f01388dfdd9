<?php
require_once '../includes/autoload.php';

header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

// Handle different request methods
switch ($method) {
    case 'GET':
        try {
            $db = get_db_connection();
            
            if (isset($_GET['id'])) {
                // Get single category
                $stmt = $db->prepare("
                    SELECT c.*, 
                           (SELECT COUNT(*) FROM products p WHERE p.category_id = c.id AND p.status = 'active') as product_count
                    FROM categories c
                    WHERE c.id = ? AND c.status = 'active'
                ");
                $stmt->execute([$_GET['id']]);
                $category = $stmt->fetch();

                if ($category) {
                    echo json_encode(['success' => true, 'data' => $category]);
                } else {
                    http_response_code(404);
                    echo json_encode(['success' => false, 'message' => 'Category not found']);
                }
            } else {
                // Get all categories
                $stmt = $db->prepare("
                    SELECT c.*,
                           (SELECT COUNT(*) FROM products p WHERE p.category_id = c.category_id AND p.is_active = 1) as product_count
                    FROM categories c
                    WHERE c.is_active = 1
                    ORDER BY c.name ASC
                ");
                $stmt->execute();
                $categories = $stmt->fetchAll();

                echo json_encode(['success' => true, 'data' => $categories]);
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Database error']);
        }
        break;

    case 'POST':
        // Add new category (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['name'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Category name is required']);
            break;
        }

        try {
            $db = get_db_connection();
            $stmt = $db->prepare("INSERT INTO categories (name, description, status) VALUES (?, ?, 'active')");
            $result = $stmt->execute([
                $data['name'],
                $data['description'] ?? ''
            ]);

            if ($result) {
                $category_id = $db->lastInsertId();
                echo json_encode([
                    'success' => true,
                    'message' => 'Category added successfully',
                    'data' => ['id' => $category_id]
                ]);
            } else {
                throw new PDOException('Failed to add category');
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to add category']);
        }
        break;

    case 'PUT':
        // Update category (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Category ID is required']);
            break;
        }

        try {
            $db = get_db_connection();
            $updates = [];
            $params = [];

            $fields = ['name', 'description', 'status'];
            foreach ($fields as $field) {
                if (isset($data[$field])) {
                    $updates[] = "$field = ?";
                    $params[] = $data[$field];
                }
            }

            if (empty($updates)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'No fields to update']);
                break;
            }

            $params[] = $data['id'];
            $query = "UPDATE categories SET " . implode(', ', $updates) . " WHERE id = ?";
            
            $stmt = $db->prepare($query);
            $result = $stmt->execute($params);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Category updated successfully']);
            } else {
                throw new PDOException('Failed to update category');
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update category']);
        }
        break;

    case 'DELETE':
        // Delete category (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Category ID is required']);
            break;
        }

        try {
            $db = get_db_connection();
            
            // Check if category has products
            $stmt = $db->prepare("SELECT COUNT(*) FROM products WHERE category_id = ? AND status = 'active'");
            $stmt->execute([$data['id']]);
            if ($stmt->fetchColumn() > 0) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Cannot delete category with active products']);
                break;
            }

            // Soft delete category
            $stmt = $db->prepare("UPDATE categories SET status = 'deleted' WHERE id = ?");
            $result = $stmt->execute([$data['id']]);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Category deleted successfully']);
            } else {
                throw new PDOException('Failed to delete category');
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete category']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
} 