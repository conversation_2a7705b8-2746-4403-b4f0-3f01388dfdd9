<?php
require_once '../includes/autoload.php';

// Enable CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header("Content-Type: application/json");

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

// Handle different request methods
switch ($method) {
    case 'GET':
        // Get products with optional filters
        $category_id = $_GET['category_id'] ?? null;
        $search = $_GET['search'] ?? null;
        $page = $_GET['page'] ?? 1;
        $limit = $_GET['limit'] ?? 12;

        try {
            $pdo = getConnection();
            $query = "SELECT p.*, c.name as category_name
                     FROM products p
                     LEFT JOIN categories c ON p.category_id = c.category_id
                     WHERE p.is_active = 1";
            $params = [];

            if ($category_id) {
                $query .= " AND p.category_id = ?";
                $params[] = $category_id;
            }

            if ($search) {
                $query .= " AND (p.name LIKE ? OR p.description LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }

            $query .= " ORDER BY p.created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = ($page - 1) * $limit;

            $stmt = $pdo->prepare($query);
            $stmt->execute($params);
            $products = $stmt->fetchAll();

            // Get total count for pagination
            $count_query = "SELECT COUNT(*) FROM products p WHERE p.is_active = 1";
            if ($category_id) {
                $count_query .= " AND p.category_id = ?";
            }
            if ($search) {
                $count_query .= " AND (p.name LIKE ? OR p.description LIKE ?)";
            }

            $stmt = $pdo->prepare($count_query);
            $stmt->execute($params);
            $total = $stmt->fetchColumn();

            echo json_encode([
                'success' => true,
                'data' => [
                    'products' => $products,
                    'pagination' => [
                        'total' => $total,
                        'per_page' => $limit,
                        'current_page' => $page,
                        'last_page' => ceil($total / $limit)
                    ]
                ]
            ]);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Database error']);
        }
        break;

    case 'POST':
        // Add new product (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['name']) || !isset($data['category_id']) || !isset($data['price'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Missing required fields']);
            break;
        }

        try {
            $pdo = getConnection();
            $stmt = $pdo->prepare("INSERT INTO products (name, description, price, category_id, stock, status) 
                                VALUES (?, ?, ?, ?, ?, 'active')");
            $result = $stmt->execute([
                $data['name'],
                $data['description'] ?? '',
                $data['price'],
                $data['category_id'],
                $data['stock'] ?? 0
            ]);

            if ($result) {
                $product_id = $pdo->lastInsertId();
                echo json_encode([
                    'success' => true,
                    'message' => 'Product added successfully',
                    'data' => ['id' => $product_id]
                ]);
            } else {
                throw new PDOException('Failed to add product');
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to add product']);
        }
        break;

    case 'PUT':
        // Update product (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Product ID is required']);
            break;
        }

        try {
            $pdo = getConnection();
            $updates = [];
            $params = [];

            $fields = ['name', 'description', 'price', 'category_id', 'stock', 'status'];
            foreach ($fields as $field) {
                if (isset($data[$field])) {
                    $updates[] = "$field = ?";
                    $params[] = $data[$field];
                }
            }

            if (empty($updates)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'No fields to update']);
                break;
            }

            $params[] = $data['id'];
            $query = "UPDATE products SET " . implode(', ', $updates) . " WHERE id = ?";
            
            $stmt = $pdo->prepare($query);
            $result = $stmt->execute($params);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Product updated successfully']);
            } else {
                throw new PDOException('Failed to update product');
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to update product']);
        }
        break;

    case 'DELETE':
        // Delete product (admin only)
        if (!is_admin()) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Unauthorized']);
            break;
        }

        if (!isset($data['id'])) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'Product ID is required']);
            break;
        }

        try {
            $pdo = getConnection();
            $stmt = $pdo->prepare("UPDATE products SET status = 'deleted' WHERE id = ?");
            $result = $stmt->execute([$data['id']]);

            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Product deleted successfully']);
            } else {
                throw new PDOException('Failed to delete product');
            }
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => 'Failed to delete product']);
        }
        break;

    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Method not allowed']);
        break;
}
?> 