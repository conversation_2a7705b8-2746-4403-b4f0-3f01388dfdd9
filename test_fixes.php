<?php
/**
 * TEWUNEED - Test Fixes
 * Test script to verify all fixes are working properly
 */

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

$test_results = [];

/**
 * Test helper function
 */
function addTestResult($test_name, $status, $message, $details = null) {
    global $test_results;
    $test_results[] = [
        'name' => $test_name,
        'status' => $status, // pass, fail, warning
        'message' => $message,
        'details' => $details,
        'timestamp' => date('H:i:s')
    ];
}

// Test 1: Check if user_notifications.php exists and is accessible
if (file_exists('user_notifications.php')) {
    addTestResult(
        'User Notifications File',
        'pass',
        'user_notifications.php file exists',
        'File is accessible and ready to use'
    );
} else {
    addTestResult(
        'User Notifications File',
        'fail',
        'user_notifications.php file not found',
        'The file should exist in the root directory'
    );
}

// Test 2: Check if AJAX add to cart file exists and is properly configured
if (file_exists('ajax/add_to_cart.php')) {
    $content = file_get_contents('ajax/add_to_cart.php');
    if (strpos($content, 'json_encode') !== false) {
        addTestResult(
            'Add to Cart AJAX',
            'pass',
            'AJAX add to cart file exists and returns JSON',
            'File properly configured for JSON responses'
        );
    } else {
        addTestResult(
            'Add to Cart AJAX',
            'warning',
            'AJAX file exists but may not return proper JSON',
            'Check if the file returns proper JSON responses'
        );
    }
} else {
    addTestResult(
        'Add to Cart AJAX',
        'fail',
        'ajax/add_to_cart.php file not found',
        'This file is required for cart functionality'
    );
}

// Test 3: Check if order-success.php has product details
if (file_exists('order-success.php')) {
    $content = file_get_contents('order-success.php');
    if (strpos($content, 'order_items') !== false && strpos($content, 'product_name') !== false) {
        addTestResult(
            'Order Success Product Details',
            'pass',
            'order-success.php includes product details',
            'File shows purchased items with details'
        );
    } else {
        addTestResult(
            'Order Success Product Details',
            'fail',
            'order-success.php missing product details',
            'File should display purchased items'
        );
    }
} else {
    addTestResult(
        'Order Success Product Details',
        'fail',
        'order-success.php file not found',
        'This file is required for order confirmation'
    );
}

// Test 4: Check if order-detail.php has product details
if (file_exists('order-detail.php')) {
    $content = file_get_contents('order-detail.php');
    if (strpos($content, 'product_image') !== false && strpos($content, 'table-responsive') !== false) {
        addTestResult(
            'Order Detail Product Display',
            'pass',
            'order-detail.php includes product details table',
            'File shows detailed product information with images'
        );
    } else {
        addTestResult(
            'Order Detail Product Display',
            'warning',
            'order-detail.php may be missing some product details',
            'Check if product images and table are properly displayed'
        );
    }
} else {
    addTestResult(
        'Order Detail Product Display',
        'fail',
        'order-detail.php file not found',
        'This file is required for order details'
    );
}

// Test 5: Check if index.php has improved showAlert function
if (file_exists('index.php')) {
    $content = file_get_contents('index.php');
    if (strpos($content, 'alert-notification') !== false && strpos($content, 'fadeOut') !== false) {
        addTestResult(
            'Homepage Alert Notifications',
            'pass',
            'index.php has improved alert notifications',
            'Notifications should appear in top-right with animations'
        );
    } else {
        addTestResult(
            'Homepage Alert Notifications',
            'warning',
            'index.php alert function may need improvement',
            'Check if notifications appear properly'
        );
    }
} else {
    addTestResult(
        'Homepage Alert Notifications',
        'fail',
        'index.php file not found',
        'This is the main homepage file'
    );
}

// Test 6: Check if Products.php has improved showAlert function
if (file_exists('Products.php')) {
    $content = file_get_contents('Products.php');
    if (strpos($content, 'alert-notification') !== false) {
        addTestResult(
            'Products Page Alert Notifications',
            'pass',
            'Products.php has improved alert notifications',
            'Notifications should work properly on products page'
        );
    } else {
        addTestResult(
            'Products Page Alert Notifications',
            'warning',
            'Products.php alert function may need improvement',
            'Check if notifications appear properly'
        );
    }
} else {
    addTestResult(
        'Products Page Alert Notifications',
        'fail',
        'Products.php file not found',
        'This is the main products catalog file'
    );
}

// Test 7: Database connectivity test
try {
    $stmt = $conn->query("SELECT 1");
    addTestResult(
        'Database Connection',
        'pass',
        'Database connection is working',
        'All database-dependent features should work'
    );
} catch (Exception $e) {
    addTestResult(
        'Database Connection',
        'fail',
        'Database connection failed',
        'Error: ' . $e->getMessage()
    );
}

// Test 8: Check if required tables exist
try {
    $required_tables = ['users', 'products', 'orders', 'order_items', 'cart', 'cart_items'];
    $stmt = $conn->query("SHOW TABLES");
    $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $missing_tables = array_diff($required_tables, $existing_tables);
    
    if (empty($missing_tables)) {
        addTestResult(
            'Required Database Tables',
            'pass',
            'All required tables exist',
            'Tables: ' . implode(', ', $required_tables)
        );
    } else {
        addTestResult(
            'Required Database Tables',
            'fail',
            'Some required tables are missing',
            'Missing: ' . implode(', ', $missing_tables)
        );
    }
} catch (Exception $e) {
    addTestResult(
        'Required Database Tables',
        'fail',
        'Could not check database tables',
        'Error: ' . $e->getMessage()
    );
}

// Calculate summary
$total_tests = count($test_results);
$passed_tests = count(array_filter($test_results, function($test) { return $test['status'] === 'pass'; }));
$failed_tests = count(array_filter($test_results, function($test) { return $test['status'] === 'fail'; }));
$warning_tests = count(array_filter($test_results, function($test) { return $test['status'] === 'warning'; }));

$success_rate = round(($passed_tests / $total_tests) * 100, 1);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEWUNEED - Fix Verification Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-card {
            margin-bottom: 1rem;
            border-left: 4px solid #dee2e6;
        }
        .test-card.pass {
            border-left-color: #28a745;
        }
        .test-card.fail {
            border-left-color: #dc3545;
        }
        .test-card.warning {
            border-left-color: #ffc107;
        }
        .status-icon.pass { color: #28a745; }
        .status-icon.fail { color: #dc3545; }
        .status-icon.warning { color: #ffc107; }
        .success-rate {
            font-size: 3rem;
            font-weight: bold;
        }
        .success-rate.high { color: #28a745; }
        .success-rate.medium { color: #ffc107; }
        .success-rate.low { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4">
                        <i class="fas fa-tools me-3"></i>
                        Fix Verification Test
                    </h1>
                    <p class="lead">Testing all fixes for TEWUNEED website issues</p>
                </div>

                <!-- Overall Results -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-md-3">
                                <h5>Success Rate</h5>
                                <div class="success-rate <?php echo $success_rate >= 80 ? 'high' : ($success_rate >= 60 ? 'medium' : 'low'); ?>">
                                    <?php echo $success_rate; ?>%
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h5>Total Tests</h5>
                                <span class="display-6"><?php echo $total_tests; ?></span>
                            </div>
                            <div class="col-md-2">
                                <h5>Passed</h5>
                                <span class="display-6 text-success"><?php echo $passed_tests; ?></span>
                            </div>
                            <div class="col-md-2">
                                <h5>Warnings</h5>
                                <span class="display-6 text-warning"><?php echo $warning_tests; ?></span>
                            </div>
                            <div class="col-md-2">
                                <h5>Failed</h5>
                                <span class="display-6 text-danger"><?php echo $failed_tests; ?></span>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="alert <?php 
                                echo $success_rate >= 80 ? 'alert-success' : 
                                     ($success_rate >= 60 ? 'alert-warning' : 'alert-danger'); 
                            ?>">
                                <strong>Overall Status: </strong>
                                <?php 
                                echo $success_rate >= 80 ? 'All major fixes are working correctly!' : 
                                     ($success_rate >= 60 ? 'Most fixes are working - some issues need attention' : 
                                      'Several critical issues detected - immediate attention required'); 
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="mb-4">
                    <h4 class="mb-3">
                        <i class="fas fa-list-check me-2"></i>
                        Test Results
                    </h4>
                    
                    <?php foreach ($test_results as $test): ?>
                    <div class="card test-card <?php echo $test['status']; ?>">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-2">
                                        <i class="fas <?php 
                                            echo $test['status'] === 'pass' ? 'fa-check-circle' : 
                                                 ($test['status'] === 'warning' ? 'fa-exclamation-triangle' : 'fa-times-circle'); 
                                        ?> status-icon <?php echo $test['status']; ?> me-2"></i>
                                        <?php echo htmlspecialchars($test['name']); ?>
                                    </h6>
                                    <p class="mb-2"><?php echo htmlspecialchars($test['message']); ?></p>
                                    
                                    <?php if ($test['details']): ?>
                                    <small class="text-muted">
                                        <strong>Details:</strong> <?php echo htmlspecialchars($test['details']); ?>
                                    </small>
                                    <?php endif; ?>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo $test['timestamp']; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <button onclick="window.location.reload()" class="btn btn-primary me-2">
                        <i class="fas fa-redo me-2"></i>Run Tests Again
                    </button>
                    <a href="index.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-home me-2"></i>Test Homepage
                    </a>
                    <a href="Products.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-shopping-bag me-2"></i>Test Products
                    </a>
                    <a href="user_notifications.php" class="btn btn-outline-info">
                        <i class="fas fa-bell me-2"></i>Test Notifications
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
