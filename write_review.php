<?php
/**
 * Write Review Page
 * Halaman untuk menulis ulasan produk
 */

session_start();
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';
require_once 'includes/ReviewManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    header('Location: login.php');
    exit;
}

// Get product ID
$product_id = isset($_GET['product_id']) ? (int)$_GET['product_id'] : 0;

if (!$product_id) {
    header('Location: index.php');
    exit;
}

// Get product details
try {
    $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        header('Location: index.php');
        exit;
    }
} catch (PDOException $e) {
    error_log("Error getting product: " . $e->getMessage());
    header('Location: index.php');
    exit;
}

$reviewManager = new ReviewManager();
$error_message = null;
$success_message = null;

// Check if user already reviewed this product
try {
    $stmt = $conn->prepare("
        SELECT review_id FROM product_reviews
        WHERE product_id = ? AND user_id = ?
    ");
    $stmt->execute([$product_id, $_SESSION['user_id']]);
    $existing_review = $stmt->fetch();

    if ($existing_review) {
        $_SESSION['alert_type'] = 'info';
        $_SESSION['alert_message'] = 'You have already reviewed this product.';
        header('Location: product_reviews.php?product_id=' . $product_id);
        exit;
    }
} catch (PDOException $e) {
    error_log("Error checking existing review: " . $e->getMessage());
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_review'])) {
    try {
        // Validate input
        $rating = isset($_POST['rating']) ? (int)$_POST['rating'] : 0;
        $review_title = trim($_POST['review_title'] ?? '');
        $review_text = trim($_POST['review_text'] ?? '');
        $is_anonymous = isset($_POST['is_anonymous']) ? true : false;

        if ($rating < 1 || $rating > 5) {
            throw new Exception("Please select a rating between 1 and 5 stars");
        }

        if (empty($review_text)) {
            throw new Exception("Please write your review");
        }

        if (strlen($review_text) < 10) {
            throw new Exception("Review must be at least 10 characters long");
        }

        if (strlen($review_text) > 2000) {
            throw new Exception("Review must not exceed 2000 characters");
        }

        // Add review
        $review_id = $reviewManager->addReview(
            $product_id,
            $_SESSION['user_id'],
            $rating,
            $review_title,
            $review_text,
            null, // order_id (null for fake reviews)
            $is_anonymous
        );

        $_SESSION['alert_type'] = 'success';
        $_SESSION['alert_message'] = 'Thank you! Your review has been submitted successfully.';
        header('Location: product_reviews.php?product_id=' . $product_id);
        exit;

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

$page = 'write_review';
$page_title = 'Write Review - ' . $product['name'];
require_once 'includes/header.php';
?>

<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="index.php">Home</a></li>
            <li class="breadcrumb-item"><a href="product-detail.php?id=<?php echo $product_id; ?>"><?php echo htmlspecialchars($product['name']); ?></a></li>
            <li class="breadcrumb-item"><a href="product_reviews.php?product_id=<?php echo $product_id; ?>">Reviews</a></li>
            <li class="breadcrumb-item active">Write Review</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Write a Review
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Product Info -->
                    <div class="product-info bg-light p-3 rounded mb-4">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <img src="<?php echo htmlspecialchars($product['image']); ?>"
                                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                                     class="img-thumbnail" style="width: 80px; height: 80px; object-fit: cover;">
                            </div>
                            <div class="col">
                                <h5 class="mb-1"><?php echo htmlspecialchars($product['name']); ?></h5>
                                <p class="text-muted mb-0">
                                    Price: Rp <?php echo number_format($product['price'], 0, ',', '.'); ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <?php if ($error_message): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Review Form -->
                    <form method="post" id="reviewForm">
                        <!-- Rating -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Rating *</label>
                            <div class="rating-input">
                                <div class="star-rating-input">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                        <input type="radio" name="rating" value="<?php echo $i; ?>"
                                               id="star<?php echo $i; ?>" required>
                                        <label for="star<?php echo $i; ?>" class="star-label">
                                            <i class="fas fa-star"></i>
                                        </label>
                                    <?php endfor; ?>
                                </div>
                                <div class="rating-text mt-2">
                                    <span id="ratingText" class="text-muted">Click to rate</span>
                                </div>
                            </div>
                        </div>

                        <!-- Review Title -->
                        <div class="mb-3">
                            <label for="review_title" class="form-label fw-bold">Review Title (Optional)</label>
                            <input type="text" class="form-control" id="review_title" name="review_title"
                                   placeholder="Summarize your review in a few words"
                                   maxlength="255" value="<?php echo htmlspecialchars($_POST['review_title'] ?? ''); ?>">
                            <div class="form-text">Help others understand your experience at a glance</div>
                        </div>

                        <!-- Review Text -->
                        <div class="mb-3">
                            <label for="review_text" class="form-label fw-bold">Your Review *</label>
                            <textarea class="form-control" id="review_text" name="review_text" rows="6"
                                      placeholder="Share your experience with this product. What did you like or dislike? How was the quality, delivery, etc.?"
                                      required minlength="10" maxlength="2000"><?php echo htmlspecialchars($_POST['review_text'] ?? ''); ?></textarea>
                            <div class="form-text">
                                <span id="charCount">0</span>/2000 characters (minimum 10 characters)
                            </div>
                        </div>

                        <!-- Review Guidelines -->
                        <div class="alert alert-info mb-3">
                            <h6><i class="fas fa-info-circle me-2"></i>Review Guidelines:</h6>
                            <ul class="mb-0 small">
                                <li>Be honest and helpful to other customers</li>
                                <li>Focus on the product features and your experience</li>
                                <li>Avoid inappropriate language or personal information</li>
                                <li>Reviews are public and will be visible to all users</li>
                            </ul>
                        </div>

                        <!-- Anonymous Option -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_anonymous" name="is_anonymous"
                                       <?php echo isset($_POST['is_anonymous']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_anonymous">
                                    Post this review anonymously
                                </label>
                                <div class="form-text">Your name will be hidden and shown as "Anonymous User"</div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <a href="product_reviews.php?product_id=<?php echo $product_id; ?>"
                               class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Reviews
                            </a>
                            <button type="submit" name="submit_review" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Submit Review
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.star-rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 5px;
}

.star-rating-input input[type="radio"] {
    display: none;
}

.star-label {
    cursor: pointer;
    font-size: 2rem;
    color: #ddd;
    transition: color 0.2s ease;
}

.star-label:hover,
.star-label:hover ~ .star-label {
    color: #ffc107;
}

.star-rating-input input[type="radio"]:checked ~ .star-label {
    color: #ffc107;
}

.star-rating-input:hover .star-label {
    color: #ddd;
}

.star-rating-input:hover .star-label:hover,
.star-rating-input:hover .star-label:hover ~ .star-label {
    color: #ffc107;
}

.product-info {
    border-left: 4px solid #0d6efd;
}

#review_text {
    resize: vertical;
    min-height: 120px;
}

.form-label.fw-bold {
    color: #495057;
}

.alert-info {
    border-left: 4px solid #0dcaf0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const ratingInputs = document.querySelectorAll('input[name="rating"]');
    const ratingText = document.getElementById('ratingText');
    const reviewText = document.getElementById('review_text');
    const charCount = document.getElementById('charCount');

    // Rating text mapping
    const ratingTexts = {
        1: '⭐ Poor - Very disappointed',
        2: '⭐⭐ Fair - Below expectations',
        3: '⭐⭐⭐ Good - Meets expectations',
        4: '⭐⭐⭐⭐ Very Good - Exceeds expectations',
        5: '⭐⭐⭐⭐⭐ Excellent - Outstanding!'
    };

    // Update rating text
    ratingInputs.forEach(input => {
        input.addEventListener('change', function() {
            ratingText.textContent = ratingTexts[this.value];
            ratingText.className = 'text-warning fw-bold';
        });
    });

    // Character counter
    function updateCharCount() {
        const count = reviewText.value.length;
        charCount.textContent = count;

        if (count < 10) {
            charCount.className = 'text-danger';
        } else if (count > 1800) {
            charCount.className = 'text-warning';
        } else {
            charCount.className = 'text-success';
        }
    }

    reviewText.addEventListener('input', updateCharCount);
    updateCharCount(); // Initial count

    // Form validation
    document.getElementById('reviewForm').addEventListener('submit', function(e) {
        const rating = document.querySelector('input[name="rating"]:checked');
        const text = reviewText.value.trim();

        if (!rating) {
            e.preventDefault();
            alert('Please select a rating');
            return;
        }

        if (text.length < 10) {
            e.preventDefault();
            alert('Review must be at least 10 characters long');
            reviewText.focus();
            return;
        }
    });

    // Auto-resize textarea
    reviewText.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.max(120, this.scrollHeight) + 'px';
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
