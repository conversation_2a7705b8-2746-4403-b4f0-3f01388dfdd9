<?php
require_once "../includes/db_connect.php";

header("Content-Type: application/json");

$search_term = $_GET["q"] ?? "";
$category_id = $_GET["category"] ?? "";
$min_price = $_GET["min_price"] ?? "";
$max_price = $_GET["max_price"] ?? "";
$sort_by = $_GET["sort"] ?? "name";

$where_conditions = ["p.stock > 0"];
$params = [];

if (!empty($search_term)) {
    $where_conditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
    $params[] = "%$search_term%";
    $params[] = "%$search_term%";
}

if (!empty($category_id)) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category_id;
}

if (!empty($min_price)) {
    $where_conditions[] = "p.price >= ?";
    $params[] = $min_price;
}

if (!empty($max_price)) {
    $where_conditions[] = "p.price <= ?";
    $params[] = $max_price;
}

$order_by = "p.name ASC";
switch ($sort_by) {
    case "price_low":
        $order_by = "p.price ASC";
        break;
    case "price_high":
        $order_by = "p.price DESC";
        break;
    case "newest":
        $order_by = "p.created_at DESC";
        break;
    case "rating":
        $order_by = "COALESCE(prs.average_rating, 0) DESC";
        break;
}

try {
    $sql = "
        SELECT p.*, c.name as category_name,
               COALESCE(prs.average_rating, 0) as average_rating,
               COALESCE(prs.total_reviews, 0) as total_reviews
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        LEFT JOIN product_rating_summary prs ON p.product_id = prs.product_id
        WHERE " . implode(" AND ", $where_conditions) . "
        ORDER BY $order_by
        LIMIT 20
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        "success" => true,
        "products" => $products,
        "count" => count($products)
    ]);
} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}
?>