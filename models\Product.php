<?php
require_once __DIR__ . '/../config/database.php';

class Product {
    private $db;

    public function __construct() {
        $this->db = getConnection();
    }

    public function getAllProducts($category_id = null, $limit = null, $offset = 0) {
        try {
            $sql = "SELECT p.*, c.name as category_name, c.slug as category_slug 
                    FROM products p 
                    JOIN categories c ON p.category_id = c.category_id 
                    WHERE p.is_active = TRUE";
            
            if ($category_id) {
                $sql .= " AND p.category_id = :category_id";
            }
            
            $sql .= " ORDER BY p.created_at DESC";
            
            if ($limit) {
                $sql .= " LIMIT :limit OFFSET :offset";
            }

            $stmt = $this->db->prepare($sql);
            
            if ($category_id) {
                $stmt->bindParam(':category_id', $category_id, PDO::PARAM_INT);
            }
            
            if ($limit) {
                $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
                $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            }

            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error in getAllProducts: " . $e->getMessage());
            return false;
        }
    }

    public function getProductById($product_id) {
        try {
            $sql = "SELECT p.*, c.name as category_name, c.slug as category_slug 
                    FROM products p 
                    JOIN categories c ON p.category_id = c.category_id 
                    WHERE p.product_id = :product_id AND p.is_active = TRUE";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $product = $stmt->fetch();
            
            if ($product) {
                // Get product features
                $sql = "SELECT * FROM product_features WHERE product_id = :product_id ORDER BY feature_order";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
                $stmt->execute();
                $product['features'] = $stmt->fetchAll();
                
                // Get product specifications
                $sql = "SELECT * FROM product_specifications WHERE product_id = :product_id ORDER BY spec_order";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
                $stmt->execute();
                $product['specifications'] = $stmt->fetchAll();
                
                // Get product nutrition info if exists
                $sql = "SELECT * FROM product_nutrition WHERE product_id = :product_id ORDER BY nutrient_order";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
                $stmt->execute();
                $product['nutrition'] = $stmt->fetchAll();
                
                // Get additional images
                $sql = "SELECT * FROM product_images WHERE product_id = :product_id ORDER BY is_primary DESC";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
                $stmt->execute();
                $product['images'] = $stmt->fetchAll();
            }
            
            return $product;
        } catch (PDOException $e) {
            error_log("Error in getProductById: " . $e->getMessage());
            return false;
        }
    }

    public function searchProducts($query, $category_id = null) {
        try {
            $sql = "SELECT p.*, c.name as category_name, c.slug as category_slug 
                    FROM products p 
                    JOIN categories c ON p.category_id = c.category_id 
                    WHERE p.is_active = TRUE AND 
                          (p.name LIKE :query OR p.description LIKE :query)";
            
            if ($category_id) {
                $sql .= " AND p.category_id = :category_id";
            }
            
            $sql .= " ORDER BY p.created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $searchQuery = "%{$query}%";
            $stmt->bindParam(':query', $searchQuery, PDO::PARAM_STR);
            
            if ($category_id) {
                $stmt->bindParam(':category_id', $category_id, PDO::PARAM_INT);
            }
            
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Error in searchProducts: " . $e->getMessage());
            return false;
        }
    }

    public function createProduct($data) {
        try {
            $this->db->beginTransaction();

            $sql = "INSERT INTO products (category_id, name, slug, description, price, cost_price, 
                                        stock, image, weight, is_active) 
                    VALUES (:category_id, :name, :slug, :description, :price, :cost_price, 
                            :stock, :image, :weight, :is_active)";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':category_id' => $data['category_id'],
                ':name' => $data['name'],
                ':slug' => $this->createSlug($data['name']),
                ':description' => $data['description'],
                ':price' => $data['price'],
                ':cost_price' => $data['cost_price'],
                ':stock' => $data['stock'],
                ':image' => $data['image'] ?? null,
                ':weight' => $data['weight'] ?? null,
                ':is_active' => $data['is_active'] ?? true
            ]);
            
            $product_id = $this->db->lastInsertId();
            
            // Insert features if provided
            if (!empty($data['features'])) {
                $sql = "INSERT INTO product_features (product_id, feature_text, feature_order) 
                        VALUES (:product_id, :feature_text, :feature_order)";
                $stmt = $this->db->prepare($sql);
                
                foreach ($data['features'] as $order => $feature) {
                    $stmt->execute([
                        ':product_id' => $product_id,
                        ':feature_text' => $feature,
                        ':feature_order' => $order
                    ]);
                }
            }
            
            // Insert specifications if provided
            if (!empty($data['specifications'])) {
                $sql = "INSERT INTO product_specifications (product_id, spec_name, spec_value, spec_order) 
                        VALUES (:product_id, :spec_name, :spec_value, :spec_order)";
                $stmt = $this->db->prepare($sql);
                
                foreach ($data['specifications'] as $order => $spec) {
                    $stmt->execute([
                        ':product_id' => $product_id,
                        ':spec_name' => $spec['name'],
                        ':spec_value' => $spec['value'],
                        ':spec_order' => $order
                    ]);
                }
            }
            
            $this->db->commit();
            return $product_id;
        } catch (PDOException $e) {
            $this->db->rollBack();
            error_log("Error in createProduct: " . $e->getMessage());
            return false;
        }
    }

    private function createSlug($string) {
        $string = strtolower($string);
        $string = preg_replace('/[^a-z0-9\-]/', '-', $string);
        $string = preg_replace('/-+/', '-', $string);
        $string = trim($string, '-');
        return $string;
    }
}
?> 