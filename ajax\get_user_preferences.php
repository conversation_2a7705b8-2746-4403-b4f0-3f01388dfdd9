<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

try {
    // Get user preferences
    $stmt = $conn->prepare("
        SELECT * FROM user_preferences 
        WHERE user_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $preferences = $stmt->fetch(PDO::FETCH_ASSOC);

    // If no preferences found, return defaults
    if (!$preferences) {
        $preferences = [
            'email_notifications' => true,
            'sms_notifications' => false,
            'marketing_emails' => true,
            'language' => 'id',
            'currency' => 'IDR'
        ];
    } else {
        // Convert string values to boolean for checkboxes
        $preferences['email_notifications'] = (bool)$preferences['email_notifications'];
        $preferences['sms_notifications'] = (bool)$preferences['sms_notifications'];
        $preferences['marketing_emails'] = (bool)$preferences['marketing_emails'];
    }

    echo json_encode([
        'success' => true,
        'preferences' => $preferences
    ]);

} catch (Exception $e) {
    error_log('Error getting user preferences: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error loading preferences: ' . $e->getMessage()
    ]);
}
?>
