<?php
// Firebase Authentication Helper Functions

/**
 * Check if a user is logged in via Firebase
 * 
 * @return bool True if user is logged in
 */
function isFirebaseLoggedIn() {
    return isset($_SESSION['firebase_user_id']) && !empty($_SESSION['firebase_user_id']);
}

/**
 * Create a PHP session from Firebase Auth
 * 
 * @param array $userData User data from Firebase
 * @return bool Success status
 */
function createSessionFromFirebase($userData) {
    if (!empty($userData)) {
        $_SESSION['firebase_user_id'] = $userData['uid'] ?? null;
        $_SESSION['user_name'] = $userData['displayName'] ?? null;
        $_SESSION['user_email'] = $userData['email'] ?? null;
        $_SESSION['user_photo'] = $userData['photoURL'] ?? null;
        $_SESSION['role'] = $userData['role'] ?? 'customer';
        $_SESSION['last_activity'] = time();
        
        // For legacy compatibility
        $_SESSION['user_id'] = $userData['uid'] ?? null;
        
        return true;
    }
    
    return false;
}

/**
 * Get the current user data
 * 
 * @return array User data
 */
function getCurrentUser() {
    $userData = [];
    
    if (isFirebaseLoggedIn()) {
        $userData = [
            'uid' => $_SESSION['firebase_user_id'] ?? null,
            'displayName' => $_SESSION['user_name'] ?? null,
            'email' => $_SESSION['user_email'] ?? null,
            'photoURL' => $_SESSION['user_photo'] ?? null,
            'role' => $_SESSION['role'] ?? 'customer'
        ];
    }
    
    return $userData;
}

/**
 * Check if a user is logged in (for legacy compatibility)
 * 
 * @return bool True if user is logged in
 */
function isLoggedIn() {
    return isFirebaseLoggedIn();
}

/**
 * Return a database connection (fallback for legacy code)
 * This is a compatibility function to prevent errors
 * 
 * @return PDO|null Database connection
 */
function getDbConnection() {
    static $conn = null;
    
    if ($conn === null) {
        try {
            $conn = new PDO("mysql:host=localhost;dbname=db_tewuneed", "root", "");
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // Silently fail but log error
            error_log("Database connection error: " . $e->getMessage());
        }
    }
    
    return $conn;
}
?>
