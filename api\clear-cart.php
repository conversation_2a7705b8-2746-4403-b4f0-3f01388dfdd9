<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set response header to JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Parse JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Log request data for debugging
error_log('Clear Cart Request: ' . $input);

try {
    // Clear cart session
    $_SESSION['cart'] = [];
    
    $response['success'] = true;
    $response['message'] = 'Cart cleared successfully';
    $response['data'] = [
        'cart_count' => 0
    ];
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    error_log('Clear cart error: ' . $e->getMessage());
}

// Return JSON response
echo json_encode($response);
