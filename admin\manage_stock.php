<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: admin_login.php');
    exit;
}

// Database connection
$conn = getConnection();

// Get product details if ID is provided
$product = null;
if (isset($_GET['id']) && is_numeric($_GET['id'])) {
    $product_id = (int)$_GET['id'];
    
    try {
        $stmt = $conn->prepare("
            SELECT p.*, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.category_id
            WHERE p.product_id = ?
        ");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            $_SESSION['alert_message'] = "Produk tidak ditemukan";
            $_SESSION['alert_type'] = 'danger';
            header('Location: products.php');
            exit;
        }
        
        // Get stock history
        $stmt = $conn->prepare("
            SELECT il.*, u.username as admin_name
            FROM inventory_logs il
            LEFT JOIN users u ON il.admin_id = u.user_id
            WHERE il.product_id = ?
            ORDER BY il.created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$product_id]);
        $stock_history = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        $_SESSION['alert_message'] = "Database error: " . $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
        header('Location: products.php');
        exit;
    }
} else {
    $_SESSION['alert_message'] = "ID produk tidak valid";
    $_SESSION['alert_type'] = 'danger';
    header('Location: products.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kelola Stok - <?php echo htmlspecialchars($product['name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
        }
        .sidebar a {
            color: rgba(255,255,255,.75);
            text-decoration: none;
            display: block;
            padding: 10px 15px;
        }
        .sidebar a:hover {
            color: white;
            background-color: rgba(255,255,255,.1);
        }
        .sidebar a.active {
            color: white;
            background-color: #0d6efd;
        }
        .main-content {
            padding: 20px;
        }
        .stock-badge {
            font-size: 1.5rem;
            padding: 8px 15px;
        }
        .stock-low {
            color: #dc3545;
        }
        .history-table tr td:first-child {
            width: 30%;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0 sidebar">
                <div class="p-3 text-center mb-3">
                    <h5>TewuNeed Admin</h5>
                </div>
                <a href="index.php"><i class="fas fa-tachometer-alt me-2"></i> Dashboard</a>
                <a href="products.php" class="active"><i class="fas fa-box me-2"></i> Produk</a>
                <a href="categories.php"><i class="fas fa-folder me-2"></i> Kategori</a>
                <a href="order_management.php"><i class="fas fa-shopping-cart me-2"></i> Pesanan</a>
                <a href="users.php"><i class="fas fa-users me-2"></i> Pengguna</a>
                <a href="logout.php"><i class="fas fa-sign-out-alt me-2"></i> Keluar</a>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <?php if (isset($_SESSION['alert_message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['alert_type']; ?> alert-dismissible fade show" role="alert">
                        <?php 
                            echo $_SESSION['alert_message']; 
                            unset($_SESSION['alert_message']);
                            unset($_SESSION['alert_type']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-cubes me-2"></i>Kelola Stok Produk</h2>
                    <div>
                        <a href="edit-product.php?id=<?php echo $product_id; ?>" class="btn btn-outline-primary me-2">
                            <i class="fas fa-edit me-2"></i>Edit Produk
                        </a>
                        <a href="products.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Kembali
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- Product Info Card -->
                    <div class="col-md-4 mb-4">
                        <div class="card shadow-sm h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informasi Produk</h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <?php if (!empty($product['image'])): ?>
                                        <img src="../uploads/products/<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="img-thumbnail" style="max-height: 150px;">
                                    <?php else: ?>
                                        <div class="bg-light p-3 rounded">
                                            <i class="fas fa-image fa-5x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <h4 class="card-title text-center mb-3"><?php echo htmlspecialchars($product['name']); ?></h4>
                                
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>ID:</strong></td>
                                        <td><?php echo $product['product_id']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Kategori:</strong></td>
                                        <td><?php echo htmlspecialchars($product['category_name'] ?? 'Tidak terkategori'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Harga:</strong></td>
                                        <td>Rp <?php echo number_format($product['price'], 0, ',', '.'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <?php if ($product['is_active']): ?>
                                                <span class="badge bg-success">Aktif</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Nonaktif</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                </table>
                                
                                <div class="text-center mt-3">
                                    <div class="d-flex justify-content-center align-items-center">
                                        <h3>Stok Saat Ini:</h3>
                                        <span class="badge bg-<?php echo $product['stock'] < 10 ? 'danger' : 'info'; ?> ms-3 stock-badge">
                                            <?php echo $product['stock']; ?>
                                        </span>
                                    </div>
                                    
                                    <?php if ($product['stock'] < 10): ?>
                                        <div class="alert alert-warning mt-3">
                                            <i class="fas fa-exclamation-triangle me-2"></i>Stok hampir habis!
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Stock Management Card -->
                    <div class="col-md-8 mb-4">
                        <div class="card shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Perbarui Stok</h5>
                            </div>
                            <div class="card-body">
                                <form action="product_actions.php?action=update_stock" method="post">
                                    <input type="hidden" name="product_id" value="<?php echo $product_id; ?>">
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label class="form-label">Jenis Penyesuaian</label>
                                            <div class="btn-group w-100" role="group">
                                                <input type="radio" class="btn-check" name="adjustment_type" id="set" value="set" checked>
                                                <label class="btn btn-outline-primary" for="set">Set</label>
                                                
                                                <input type="radio" class="btn-check" name="adjustment_type" id="add" value="add">
                                                <label class="btn btn-outline-success" for="add">Tambah</label>
                                                
                                                <input type="radio" class="btn-check" name="adjustment_type" id="subtract" value="subtract">
                                                <label class="btn btn-outline-danger" for="subtract">Kurang</label>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <label for="new_stock" class="form-label">Jumlah</label>
                                            <input type="number" class="form-control form-control-lg" id="new_stock" name="new_stock" min="0" value="<?php echo $product['stock']; ?>" required>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <label class="form-label">Stok Akhir</label>
                                            <div class="form-control form-control-lg bg-light" id="final_stock">
                                                <?php echo $product['stock']; ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">Catatan (Opsional)</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="2" placeholder="Catatan tentang penyesuaian stok ini..."></textarea>
                                    </div>
                                    
                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-save me-2"></i>Perbarui Stok
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Stock History Card -->
                        <div class="card shadow-sm mt-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Riwayat Stok</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($stock_history)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover history-table">
                                            <thead>
                                                <tr>
                                                    <th>Tanggal & Waktu</th>
                                                    <th>Stok Lama</th>
                                                    <th>Stok Baru</th>
                                                    <th>Perubahan</th>
                                                    <th>Admin</th>
                                                    <th>Catatan</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($stock_history as $log): ?>
                                                    <tr>
                                                        <td><?php echo date('d/m/Y H:i', strtotime($log['created_at'])); ?></td>
                                                        <td><?php echo $log['old_stock']; ?></td>
                                                        <td><?php echo $log['new_stock']; ?></td>
                                                        <td>
                                                            <?php 
                                                                $change = $log['adjustment'];
                                                                if ($change > 0) {
                                                                    echo '<span class="text-success">+' . $change . '</span>';
                                                                } elseif ($change < 0) {
                                                                    echo '<span class="text-danger">' . $change . '</span>';
                                                                } else {
                                                                    echo '<span class="text-muted">0</span>';
                                                                }
                                                            ?>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($log['admin_name'] ?? 'System'); ?></td>
                                                        <td><?php echo htmlspecialchars($log['notes'] ?? '-'); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>Belum ada riwayat perubahan stok untuk produk ini.
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Calculate final stock based on adjustment type
        document.addEventListener('DOMContentLoaded', function() {
            const currentStock = <?php echo $product['stock']; ?>;
            const newStockInput = document.getElementById('new_stock');
            const finalStockDisplay = document.getElementById('final_stock');
            const adjustmentTypes = document.querySelectorAll('input[name="adjustment_type"]');
            
            // Function to calculate and display final stock
            function updateFinalStock() {
                const selectedType = document.querySelector('input[name="adjustment_type"]:checked').value;
                const newStockValue = parseInt(newStockInput.value) || 0;
                let finalStock = currentStock;
                
                switch (selectedType) {
                    case 'add':
                        finalStock = currentStock + newStockValue;
                        break;
                    case 'subtract':
                        finalStock = Math.max(0, currentStock - newStockValue);
                        break;
                    case 'set':
                    default:
                        finalStock = newStockValue;
                        break;
                }
                
                finalStockDisplay.textContent = finalStock;
                
                // Change color based on stock level
                if (finalStock < 10) {
                    finalStockDisplay.classList.add('text-danger');
                    finalStockDisplay.classList.remove('text-success');
                } else {
                    finalStockDisplay.classList.add('text-success');
                    finalStockDisplay.classList.remove('text-danger');
                }
            }
            
            // Update final stock when input changes
            newStockInput.addEventListener('input', updateFinalStock);
            
            // Update final stock when adjustment type changes
            adjustmentTypes.forEach(function(radio) {
                radio.addEventListener('change', function() {
                    // Reset stock input value based on adjustment type
                    if (this.value === 'set') {
                        newStockInput.value = currentStock;
                    } else {
                        newStockInput.value = 0;
                    }
                    updateFinalStock();
                });
            });
            
            // Initial calculation
            updateFinalStock();
        });
    </script>
</body>
</html>
