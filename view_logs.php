<?php
/**
 * View PHP Error Logs
 * This helps debug checkout issues
 */

// Security check - only allow in development
if (!in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1', 'localhost:8000'])) {
    die('Access denied');
}

$logFile = ini_get('error_log');
if (!$logFile) {
    // Try common log locations
    $possibleLogs = [
        'C:\xampp\apache\logs\error.log',
        'C:\xampp\php\logs\php_error_log',
        '/var/log/apache2/error.log',
        '/var/log/php_errors.log',
        __DIR__ . '/error.log'
    ];
    
    foreach ($possibleLogs as $log) {
        if (file_exists($log)) {
            $logFile = $log;
            break;
        }
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>PHP Error Logs</title>
    <style>
        body {
            font-family: monospace;
            margin: 20px;
            background: #1e1e1e;
            color: #fff;
        }
        .log-container {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 5px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .error {
            color: #ff6b6b;
        }
        .warning {
            color: #feca57;
        }
        .info {
            color: #48dbfb;
        }
        .checkout {
            background: #ff9ff3;
            color: #000;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            background: #4834d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #3742fa;
        }
    </style>
</head>
<body>
    <h1>🔍 PHP Error Logs Viewer</h1>
    
    <div class="controls">
        <button onclick="location.reload()">🔄 Refresh</button>
        <button onclick="clearLogs()">🗑️ Clear View</button>
        <button onclick="autoRefresh()">⚡ Auto Refresh</button>
        <span id="status"></span>
    </div>
    
    <div class="log-container" id="logContainer">
        <?php
        if ($logFile && file_exists($logFile)) {
            echo "<p><strong>Log file:</strong> $logFile</p>";
            echo "<hr>";
            
            // Read last 100 lines
            $lines = file($logFile);
            if ($lines) {
                $lines = array_slice($lines, -100); // Last 100 lines
                
                foreach ($lines as $line) {
                    $line = htmlspecialchars($line);
                    
                    // Highlight different types of messages
                    if (stripos($line, 'error') !== false) {
                        echo "<div class='error'>$line</div>";
                    } elseif (stripos($line, 'warning') !== false) {
                        echo "<div class='warning'>$line</div>";
                    } elseif (stripos($line, 'checkout') !== false || stripos($line, 'CHECKOUT') !== false) {
                        echo "<div class='checkout'>$line</div>";
                    } else {
                        echo "<div class='info'>$line</div>";
                    }
                }
            } else {
                echo "<p>No log entries found.</p>";
            }
        } else {
            echo "<p><strong>Error:</strong> Could not find PHP error log file.</p>";
            echo "<p>Checked locations:</p>";
            echo "<ul>";
            if (isset($possibleLogs)) {
                foreach ($possibleLogs as $log) {
                    $exists = file_exists($log) ? '✅' : '❌';
                    echo "<li>$exists $log</li>";
                }
            }
            echo "</ul>";
            
            echo "<p><strong>Current error_log setting:</strong> " . (ini_get('error_log') ?: 'Not set') . "</p>";
            echo "<p><strong>Log errors enabled:</strong> " . (ini_get('log_errors') ? 'Yes' : 'No') . "</p>";
            echo "<p><strong>Display errors:</strong> " . (ini_get('display_errors') ? 'Yes' : 'No') . "</p>";
        }
        ?>
    </div>
    
    <div style="margin-top: 20px;">
        <h3>🧪 Test Actions</h3>
        <button onclick="testError()">Test Error Log</button>
        <button onclick="window.open('emergency_checkout.php')">Emergency Checkout</button>
        <button onclick="window.open('debug_checkout.php')">Debug Checkout</button>
    </div>
    
    <script>
        let autoRefreshInterval;
        
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '<p>Logs cleared from view (file not modified)</p>';
        }
        
        function autoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                document.getElementById('status').textContent = '';
            } else {
                autoRefreshInterval = setInterval(() => {
                    location.reload();
                }, 3000);
                document.getElementById('status').textContent = '🔄 Auto-refreshing every 3 seconds';
            }
        }
        
        function testError() {
            fetch('test_error_log.php')
                .then(() => {
                    setTimeout(() => location.reload(), 1000);
                })
                .catch(err => console.error(err));
        }
        
        // Auto-scroll to bottom
        const container = document.getElementById('logContainer');
        container.scrollTop = container.scrollHeight;
    </script>
</body>
</html>
