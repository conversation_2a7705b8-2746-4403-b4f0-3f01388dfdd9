<?php
/**
 * Helper functions for cart stock management
 * Simplified approach without complex reservation system
 */

/**
 * Get user's current cart quantity for a specific product
 */
function getUserCartQuantity($conn, $product_id, $user_id) {
    try {
        // Get user's cart
        $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $cart = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$cart) {
            return 0;
        }
        
        // Get quantity in cart for this product
        $stmt = $conn->prepare("SELECT quantity FROM cart_items WHERE cart_id = ? AND product_id = ?");
        $stmt->execute([$cart['cart_id'], $product_id]);
        $cartItem = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $cartItem ? (int)$cartItem['quantity'] : 0;
    } catch (Exception $e) {
        error_log("Error getting user cart quantity: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get available stock for a product considering user's cart
 */
function getAvailableStockForUser($conn, $product_id, $user_id) {
    try {
        // Get actual product stock
        $stmt = $conn->prepare("SELECT stock FROM products WHERE product_id = ?");
        $stmt->execute([$product_id]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            return 0;
        }
        
        $actualStock = (int)$product['stock'];
        
        // For simplicity, return actual stock
        // In a more complex system, we would subtract reserved quantities from other users
        return $actualStock;
        
    } catch (Exception $e) {
        error_log("Error getting available stock: " . $e->getMessage());
        return 0;
    }
}

/**
 * Check if user can add quantity to cart
 */
function canAddToCart($conn, $product_id, $quantity, $user_id) {
    try {
        $availableStock = getAvailableStockForUser($conn, $product_id, $user_id);
        $currentCartQuantity = getUserCartQuantity($conn, $product_id, $user_id);
        
        $totalRequestedQuantity = $currentCartQuantity + $quantity;
        
        return [
            'can_add' => $totalRequestedQuantity <= $availableStock,
            'available_stock' => $availableStock,
            'current_cart_quantity' => $currentCartQuantity,
            'total_requested' => $totalRequestedQuantity
        ];
    } catch (Exception $e) {
        error_log("Error checking if can add to cart: " . $e->getMessage());
        return [
            'can_add' => false,
            'available_stock' => 0,
            'current_cart_quantity' => 0,
            'total_requested' => $quantity
        ];
    }
}

/**
 * Get total cart items count for user
 */
function getUserCartCount($conn, $user_id) {
    try {
        // Get user's cart
        $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $cart = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$cart) {
            return 0;
        }
        
        // Get total quantity in cart
        $stmt = $conn->prepare("SELECT SUM(quantity) as total_count FROM cart_items WHERE cart_id = ?");
        $stmt->execute([$cart['cart_id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result ? (int)$result['total_count'] : 0;
    } catch (Exception $e) {
        error_log("Error getting user cart count: " . $e->getMessage());
        return 0;
    }
}

/**
 * Clean up empty carts
 */
function cleanupEmptyCarts($conn) {
    try {
        // Delete carts that have no items
        $stmt = $conn->prepare("
            DELETE c FROM carts c 
            LEFT JOIN cart_items ci ON c.cart_id = ci.cart_id 
            WHERE ci.cart_id IS NULL
        ");
        $stmt->execute();
        
        return true;
    } catch (Exception $e) {
        error_log("Error cleaning up empty carts: " . $e->getMessage());
        return false;
    }
}

/**
 * Get cart summary for user
 */
function getCartSummary($conn, $user_id) {
    try {
        // Get user's cart
        $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $cart = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$cart) {
            return [
                'total_items' => 0,
                'total_quantity' => 0,
                'total_amount' => 0
            ];
        }
        
        // Get cart summary
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total_items,
                SUM(ci.quantity) as total_quantity,
                SUM(ci.quantity * p.price) as total_amount
            FROM cart_items ci
            JOIN products p ON ci.product_id = p.product_id
            WHERE ci.cart_id = ?
        ");
        $stmt->execute([$cart['cart_id']]);
        $summary = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'total_items' => (int)($summary['total_items'] ?? 0),
            'total_quantity' => (int)($summary['total_quantity'] ?? 0),
            'total_amount' => (float)($summary['total_amount'] ?? 0)
        ];
    } catch (Exception $e) {
        error_log("Error getting cart summary: " . $e->getMessage());
        return [
            'total_items' => 0,
            'total_quantity' => 0,
            'total_amount' => 0
        ];
    }
}
?>
