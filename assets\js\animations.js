/**
 * Global Animations - Enhances user experience with animations and interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add ripple effect to all buttons
    setupRippleEffect();
    
    // Add hover effects to cards with hover-effect class
    setupCardHoverEffects();
    
    // Add image zoom effect
    setupImageZoomEffects();
    
    // Initialize any elements with animation classes
    initializeAnimationClasses();
});

/**
 * Adds a ripple effect to buttons when clicked
 */
function setupRippleEffect() {
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            // Skip if button already has ripple handling (e.g., from profile-enhancements.js)
            if (this.classList.contains('ripple-handled')) return;
            
            // Create ripple element
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);
            
            // Position the ripple
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = `${size}px`;
            
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;
            
            // Remove ripple after animation completes
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * Adds hover effects to cards with hover-effect class
 */
function setupCardHoverEffects() {
    document.querySelectorAll('.card.hover-effect').forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
            card.style.borderColor = '#007bff';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0)';
            card.style.boxShadow = '';
            card.style.borderColor = '';
        });
    });
}

/**
 * Adds zoom effect to images in containers with img-hover-zoom class
 */
function setupImageZoomEffects() {
    document.querySelectorAll('.img-hover-zoom').forEach(container => {
        const img = container.querySelector('img');
        if (img) {
            container.style.overflow = 'hidden';
            img.style.transition = 'transform 0.5s ease';
            
            container.addEventListener('mouseenter', () => {
                img.style.transform = 'scale(1.1)';
            });
            
            container.addEventListener('mouseleave', () => {
                img.style.transform = 'scale(1)';
            });
        }
    });
}

/**
 * Initializes elements with animation classes
 */
function initializeAnimationClasses() {
    // Add fade-in animation to elements with fade-in class
    document.querySelectorAll('.fade-in').forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(10px)';
        
        // Use Intersection Observer to trigger animation when element is in viewport
        const observer = new IntersectionObserver(entries => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeIn 0.5s ease-in-out forwards';
                    observer.unobserve(entry.target);
                }
            });
        });
        
        observer.observe(element);
    });
    
    // Initialize pulse animation
    document.querySelectorAll('.pulse').forEach(element => {
        element.style.animation = 'pulse 1.5s infinite';
    });
}

/**
 * Utility function to add loading state to buttons
 * @param {HTMLElement} button - The button element
 * @param {boolean} isLoading - Whether to show loading state
 */
function setButtonLoading(button, isLoading) {
    if (!button) return;
    
    if (isLoading) {
        // Store original text
        button.dataset.originalText = button.innerHTML;
        
        // Create spinner
        const spinner = document.createElement('span');
        spinner.className = 'spinner-border spinner-border-sm me-2';
        spinner.setAttribute('role', 'status');
        spinner.setAttribute('aria-hidden', 'true');
        
        // Set loading text with spinner
        button.innerHTML = '';
        button.appendChild(spinner);
        button.appendChild(document.createTextNode(' Loading...'));
        
        // Disable button
        button.disabled = true;
    } else {
        // Restore original text
        if (button.dataset.originalText) {
            button.innerHTML = button.dataset.originalText;
            delete button.dataset.originalText;
        }
        
        // Enable button
        button.disabled = false;
        
        // Add success animation briefly
        button.classList.add('success-pulse');
        setTimeout(() => {
            button.classList.remove('success-pulse');
        }, 1000);
    }
}

/**
 * Shows a toast message
 * @param {string} message - The message to display
 * @param {string} type - The type of message (success, danger, warning, info)
 * @param {number} duration - How long to show the message in milliseconds
 */
function showToast(message, type = 'success', duration = 5000) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toastElement = document.createElement('div');
    toastElement.id = toastId;
    toastElement.className = `toast show animate__animated animate__fadeInRight`;
    toastElement.role = 'alert';
    toastElement.setAttribute('aria-live', 'assertive');
    toastElement.setAttribute('aria-atomic', 'true');
    
    // Add color based on type
    toastElement.classList.add(`bg-${type}`, 'text-white');
    
    // Add icon based on message type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle me-2"></i>';
            break;
        case 'danger':
            icon = '<i class="fas fa-exclamation-circle me-2"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle me-2"></i>';
            break;
        case 'info':
            icon = '<i class="fas fa-info-circle me-2"></i>';
            break;
        default:
            icon = '<i class="fas fa-bell me-2"></i>';
    }
    
    // Set toast content
    toastElement.innerHTML = `
        <div class="toast-header bg-${type} text-white">
            ${icon}
            <strong class="me-auto">Notification</strong>
            <small>Just now</small>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            ${message}
        </div>
    `;
    
    // Add toast to container
    toastContainer.appendChild(toastElement);
    
    // Auto-dismiss after duration
    setTimeout(() => {
        const toast = document.getElementById(toastId);
        if (toast) {
            // Add exit animation
            toast.classList.remove('animate__fadeInRight');
            toast.classList.add('animate__fadeOutRight');
            
            // Remove after animation completes
            setTimeout(() => {
                if (toast && toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 500);
        }
    }, duration);
    
    // Return the toast element for further manipulation if needed
    return toastElement;
}