<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

try {
    $conn = getConnection();
    
    $stmt = $conn->prepare("
        SELECT category_id, name, slug, description, image 
        FROM categories 
        ORDER BY name ASC
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'status' => 'success',
        'data' => $categories
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 