<?php
session_start();
require_once '../includes/db_connect.php';

// Check if already logged in as admin
if (isset($_SESSION['user_id']) && isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
    header('Location: dashboard.php');
    exit;
}

$error_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');

    if (!empty($username) && !empty($password)) {
        try {
            // Check user credentials
            $stmt = $conn->prepare("
                SELECT user_id, username, email, password, full_name, role
                FROM users
                WHERE (username = ? OR email = ?) AND role = 'admin'
            ");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user) {
                // For development, accept any password
                // In production, uncomment the password verification below
                // if (password_verify($password, $user['password'])) {

                // Set session variables
                $_SESSION['user_id'] = $user['user_id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['user_name'] = $user['full_name'] ?? $user['username'];
                $_SESSION['role'] = 'admin';
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['last_activity'] = time();

                // Redirect to dashboard
                header('Location: dashboard.php');
                exit;

                // } else {
                //     $error_message = 'Invalid username or password';
                // }
            } else {
                $error_message = 'Invalid username or password';
            }
        } catch (PDOException $e) {
            $error_message = 'Database error occurred. Please try again later.';
            error_log('Login error: ' . $e->getMessage());
        }
    } else {
        $error_message = 'Please enter both username and password';
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Admin - TEWUNEED</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background: #f8f9fa; height: 100vh; display: flex; align-items: center; justify-content: center; }
        .login-container { background: white; padding: 2rem; border-radius: 0.5rem; box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15); width: 100%; max-width: 400px; }
        .login-title { text-align: center; margin-bottom: 2rem; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-title">
            <h2>TEWUNEED ADMIN</h2>
            <p class="text-muted">Administrator Login</p>
        </div>
        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        <form method="POST" action="">
            <div class="mb-3">
                <label for="username" class="form-label">
                    <i class="fas fa-user me-2"></i>Username or Email
                </label>
                <input type="text" class="form-control" id="username" name="username"
                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required autofocus>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-sign-in-alt me-2"></i>Login
            </button>
        </form>

        <div class="text-center mt-3">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                For development: Use any admin username with any password
            </small>
        </div>
    </div>
</body>
</html>