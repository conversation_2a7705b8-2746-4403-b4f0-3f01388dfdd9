<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php_error.log');
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';
require_once 'auth.php';

$page_title = 'Manajemen Produk';
$active_page = 'products';

// Ambil semua kategori
$categories = $conn->query("SELECT * FROM categories ORDER BY name")->fetchAll(PDO::FETCH_ASSOC);

// Ambil filter
$category_filter = isset($_GET['category']) ? $_GET['category'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Query produk
$where = 'WHERE 1=1';
$params = [];
if ($category_filter) {
    $where .= ' AND p.category_id = ?';
    $params[] = $category_filter;
}
if ($search) {
    $where .= ' AND (p.NAME LIKE ? OR p.description LIKE ?)';
    $params[] = "%$search%";
    $params[] = "%$search%";
}
$stmt = $conn->prepare("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.category_id $where ORDER BY p.product_id DESC");
$stmt->execute($params);
$products = $stmt->fetchAll(PDO::FETCH_ASSOC);

require_once 'includes/header.php';
?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-box me-2"></i>Manajemen Produk</h2>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal"><i class="fas fa-plus me-2"></i>Tambah Produk</button>
    </div>
    <form class="row g-2 mb-3" method="get">
        <div class="col-md-3">
            <select class="form-select" name="category">
                <option value="">Semua Kategori</option>
                <?php foreach ($categories as $cat): ?>
                    <option value="<?= $cat['category_id'] ?>" <?= $category_filter == $cat['category_id'] ? 'selected' : '' ?>><?= htmlspecialchars($cat['NAME']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-4">
            <input type="text" class="form-control" name="search" placeholder="Cari produk..." value="<?= htmlspecialchars($search) ?>">
        </div>
        <div class="col-md-2">
            <button class="btn btn-outline-primary w-100" type="submit"><i class="fas fa-search me-1"></i>Cari</button>
        </div>
    </form>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover align-middle" id="productsTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nama Produk</th>
                            <th>Kategori</th>
                            <th>Harga</th>
                            <th>Stok</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                        <tr data-id="<?= $product['product_id'] ?>">
                            <td><?= $product['product_id'] ?></td>
                            <td><?= htmlspecialchars($product['NAME']) ?></td>
                            <td><?= htmlspecialchars($product['category_name']) ?></td>
                            <td>Rp<?= number_format($product['price'], 0, ',', '.') ?></td>
                            <td><?= $product['stock'] ?></td>
                            <td>
                                <?php if (!empty($product['is_active'])): ?>
                                    <span class="badge bg-success">Aktif</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Tidak Aktif</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-warning" onclick="showEditProductModal(<?= $product['product_id'] ?>)"><i class="fas fa-edit"></i> Edit</button>
                                <button class="btn btn-sm btn-danger" onclick="deleteProduct(<?= $product['product_id'] ?>)"><i class="fas fa-trash"></i> Hapus</button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Produk -->
<div class="modal fade" id="addProductModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Produk</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div class="mb-3">
                        <label class="form-label">Nama Produk</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Kategori</label>
                        <select class="form-select" name="category_id" required>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?= $cat['category_id'] ?>"><?= htmlspecialchars($cat['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Harga</label>
                        <input type="number" class="form-control" name="price" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Stok</label>
                        <input type="number" class="form-control" name="stock" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="is_active">
                            <option value="1">Aktif</option>
                            <option value="0">Tidak Aktif</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Deskripsi</label>
                        <textarea class="form-control" name="description"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Gambar Produk</label>
                        <input type="file" class="form-control" name="image" accept="image/*">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" onclick="saveProduct()">Simpan</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Edit Produk -->
<div class="modal fade" id="editProductModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Produk</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editProductForm">
                    <input type="hidden" name="product_id">
                    <div class="mb-3">
                        <label class="form-label">Nama Produk</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Kategori</label>
                        <select class="form-select" name="category_id" required>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?= $cat['category_id'] ?>"><?= htmlspecialchars($cat['NAME']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Harga</label>
                        <input type="number" class="form-control" name="price" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Stok</label>
                        <input type="number" class="form-control" name="stock" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-select" name="is_active">
                            <option value="1">Aktif</option>
                            <option value="0">Tidak Aktif</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Deskripsi</label>
                        <textarea class="form-control" name="description"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Gambar Produk</label>
                        <input type="file" class="form-control" name="image" accept="image/*">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" onclick="updateProduct()">Simpan Perubahan</button>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
<script>
// Helper: show toast
function showToast(message, type = 'success') {
    Toastify({
        text: message,
        duration: 3000,
        gravity: 'top',
        position: 'right',
        backgroundColor: type === 'success' ? '#28a745' : '#dc3545',
        stopOnFocus: true
    }).showToast();
}

// Tambah produk
async function saveProduct() {
    const form = document.getElementById('addProductForm');
    const formData = new FormData(form);
    formData.append('action', 'add');
    try {
        const res = await fetch('product_operations.php', {
            method: 'POST',
            body: formData
        });
        const result = await res.json();
        if (result.status === 'success') {
            showToast(result.message);
            bootstrap.Modal.getInstance(document.getElementById('addProductModal')).hide();
            form.reset();
            location.reload();
        } else {
            showToast(result.message, 'error');
        }
    } catch (err) {
        showToast('Gagal tambah produk', 'error');
    }
}

// Tampilkan modal edit produk
async function showEditProductModal(id) {
    try {
        const res = await fetch('product_operations.php', {
            method: 'POST',
            body: new URLSearchParams({ action: 'get', id })
        });
        const result = await res.json();
        if (result.status === 'success') {
            const data = result.data;
            const form = document.getElementById('editProductForm');
            form.product_id.value = data.product_id;
            form.name.value = data.NAME;
            form.category_id.value = data.category_id;
            form.price.value = data.price;
            form.stock.value = data.stock;
            form.is_active.value = data.is_active;
            form.description.value = data.description || '';
            // Gambar tidak diisi (hanya upload baru jika ingin ganti)
            new bootstrap.Modal(document.getElementById('editProductModal')).show();
        } else {
            showToast(result.message, 'error');
        }
    } catch (err) {
        showToast('Gagal ambil data produk', 'error');
    }
}

// Update produk
async function updateProduct() {
    const form = document.getElementById('editProductForm');
    const formData = new FormData(form);
    formData.append('action', 'edit');
    try {
        const res = await fetch('product_operations.php', {
            method: 'POST',
            body: formData
        });
        const result = await res.json();
        if (result.status === 'success') {
            showToast(result.message);
            bootstrap.Modal.getInstance(document.getElementById('editProductModal')).hide();
            location.reload();
        } else {
            showToast(result.message, 'error');
        }
    } catch (err) {
        showToast('Gagal update produk', 'error');
    }
}

// Hapus produk
async function deleteProduct(id) {
    if (!confirm('Yakin ingin menghapus produk ini?')) return;
    try {
        const res = await fetch('product_operations.php', {
            method: 'POST',
            body: new URLSearchParams({ action: 'delete', id })
        });
        const result = await res.json();
        if (result.status === 'success') {
            showToast(result.message);
            location.reload();
        } else {
            showToast(result.message, 'error');
        }
    } catch (err) {
        showToast('Gagal hapus produk', 'error');
    }
}
</script>
</body>
</html>
