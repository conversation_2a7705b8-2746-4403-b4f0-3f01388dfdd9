<?php
/**
 * Enhanced Notification Functions
 * Additional functions for real-time notifications and status updates
 */

/**
 * Create real-time notification with enhanced features
 */
if (!function_exists('createRealTimeNotification')) {
function createRealTimeNotification($conn, $user_id, $type, $title, $message, $data = []) {
    try {
        // Check if table exists first
        $stmt = $conn->query("SHOW TABLES LIKE 'order_notifications'");
        if ($stmt->rowCount() == 0) {
            // Table doesn't exist, try to create it
            if (!ensureNotificationTableStructure($conn)) {
                error_log("Failed to create notification table");
                return false;
            }
        }

        $stmt = $conn->prepare("
            INSERT INTO order_notifications (
                user_id, title, message, type,
                order_id, status_logo, old_status, new_status,
                is_read, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, FALSE, NOW())
        ");

        return $stmt->execute([
            $user_id,
            $title,
            $message,
            $type,
            $data['order_id'] ?? null,
            $data['status_logo'] ?? '📦',
            $data['old_status'] ?? null,
            $data['new_status'] ?? null
        ]);

    } catch (Exception $e) {
        error_log("Error creating real-time notification: " . $e->getMessage());
        return false;
    }
}
}

/**
 * Ensure notification table has all required columns
 */
if (!function_exists('ensureNotificationTableStructure')) {
function ensureNotificationTableStructure($conn) {
    try {
        // Check if table exists
        $stmt = $conn->query("SHOW TABLES LIKE 'order_notifications'");
        if ($stmt->rowCount() == 0) {
            // Create table
            $sql = "
            CREATE TABLE order_notifications (
                notification_id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NULL,
                user_id INT NOT NULL,
                title VARCHAR(255) NOT NULL,
                message TEXT,
                status_logo VARCHAR(10) DEFAULT '📦',
                old_status VARCHAR(50) NULL,
                new_status VARCHAR(50) NULL,
                type VARCHAR(50) DEFAULT 'status_update',
                is_read BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_order_id (order_id),
                INDEX idx_is_read (is_read),
                INDEX idx_created_at (created_at),
                INDEX idx_type (type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ";
            $conn->exec($sql);
            return true;
        }
        
        // Check and add missing columns
        $stmt = $conn->query("DESCRIBE order_notifications");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $required_columns = [
            'status_logo' => "ALTER TABLE order_notifications ADD COLUMN status_logo VARCHAR(10) DEFAULT '📦' AFTER message",
            'old_status' => "ALTER TABLE order_notifications ADD COLUMN old_status VARCHAR(50) NULL AFTER status_logo",
            'new_status' => "ALTER TABLE order_notifications ADD COLUMN new_status VARCHAR(50) NULL AFTER old_status",
            'type' => "ALTER TABLE order_notifications ADD COLUMN type VARCHAR(50) DEFAULT 'status_update' AFTER new_status"
        ];
        
        foreach ($required_columns as $column => $sql) {
            if (!in_array($column, $columns)) {
                try {
                    $conn->exec($sql);
                } catch (Exception $e) {
                    error_log("Could not add column {$column}: " . $e->getMessage());
                }
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error ensuring notification table structure: " . $e->getMessage());
        return false;
    }
}
}

/**
 * Get notifications with enhanced filtering
 */
if (!function_exists('getEnhancedNotifications')) {
function getEnhancedNotifications($conn, $user_id, $options = []) {
    $limit = $options['limit'] ?? 10;
    $unread_only = $options['unread_only'] ?? false;
    $type = $options['type'] ?? null;
    $since = $options['since'] ?? null;
    
    $where_conditions = ["user_id = ?"];
    $params = [$user_id];
    
    if ($unread_only) {
        $where_conditions[] = "is_read = FALSE";
    }
    
    if ($type) {
        $where_conditions[] = "type = ?";
        $params[] = $type;
    }
    
    if ($since) {
        $where_conditions[] = "created_at > ?";
        $params[] = $since;
    }
    
    $where_clause = implode(" AND ", $where_conditions);
    $limit = (int)$limit;
    
    $sql = "
        SELECT n.*, o.order_number
        FROM order_notifications n
        LEFT JOIN orders o ON n.order_id = o.order_id
        WHERE {$where_clause}
        ORDER BY n.created_at DESC
        LIMIT {$limit}
    ";
    
    try {
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting enhanced notifications: " . $e->getMessage());
        return [];
    }
}
}

/**
 * Broadcast notification to multiple users
 */
if (!function_exists('broadcastNotification')) {
function broadcastNotification($conn, $user_ids, $title, $message, $type = 'general', $data = []) {
    $success_count = 0;

    foreach ($user_ids as $user_id) {
        if (createRealTimeNotification($conn, $user_id, $type, $title, $message, $data)) {
            $success_count++;
        }
    }

    return $success_count;
}
}

/**
 * Create order status change notification with rich content
 */
if (!function_exists('createOrderStatusNotification')) {
function createOrderStatusNotification($conn, $order_id, $user_id, $old_status, $new_status, $admin_note = '') {
    require_once 'order_status_functions.php';
    
    $statuses = getOrderStatuses();
    $old_status_info = $statuses[$old_status] ?? null;
    $new_status_info = $statuses[$new_status] ?? null;
    
    if (!$new_status_info) {
        return false;
    }
    
    $logo = $new_status_info['logo'] ?? '📦';
    $title = "{$logo} Pesanan #{$order_id} - " . $new_status_info['label'];
    
    // Create rich message
    $message = $new_status_info['description'];
    
    if ($old_status_info && $old_status !== $new_status) {
        $old_logo = $old_status_info['logo'] ?? '⏰';
        $old_label = $old_status_info['label'] ?? ucfirst($old_status);
        $message = "Status pesanan berubah dari {$old_logo} {$old_label} menjadi {$logo} " . $new_status_info['label'] . "\n\n" . $new_status_info['description'];
    }
    
    if ($admin_note) {
        $message .= "\n\n💬 Catatan dari admin: " . $admin_note;
    }
    
    // Add status-specific helpful information
    switch ($new_status) {
        case 'confirmed':
            $message .= "\n\n✅ Pembayaran Anda telah dikonfirmasi dan pesanan akan segera diproses.";
            break;
        case 'processing':
            $message .= "\n\n⚙️ Pesanan Anda sedang disiapkan oleh tim kami.";
            break;
        case 'shipped':
            $message .= "\n\n🚚 Pesanan Anda telah dikirim dan sedang dalam perjalanan.";
            break;
        case 'delivered':
            $message .= "\n\n🎉 Pesanan Anda telah berhasil diterima. Terima kasih telah berbelanja!";
            break;
    }
    
    return createRealTimeNotification($conn, $user_id, 'status_update', $title, $message, [
        'order_id' => $order_id,
        'status_logo' => $logo,
        'old_status' => $old_status,
        'new_status' => $new_status
    ]);
}
}

/**
 * Mark multiple notifications as read
 */
if (!function_exists('markMultipleNotificationsRead')) {
function markMultipleNotificationsRead($conn, $notification_ids, $user_id) {
    if (empty($notification_ids)) {
        return false;
    }
    
    $placeholders = str_repeat('?,', count($notification_ids) - 1) . '?';
    $params = array_merge($notification_ids, [$user_id]);
    
    $sql = "
        UPDATE order_notifications 
        SET is_read = TRUE 
        WHERE notification_id IN ({$placeholders}) AND user_id = ?
    ";
    
    try {
        $stmt = $conn->prepare($sql);
        return $stmt->execute($params);
    } catch (Exception $e) {
        error_log("Error marking multiple notifications as read: " . $e->getMessage());
        return false;
    }
}
}

/**
 * Get notification statistics for user
 */
if (!function_exists('getNotificationStats')) {
function getNotificationStats($conn, $user_id) {
    try {
        $stats = [
            'total' => 0,
            'unread' => 0,
            'today' => 0,
            'this_week' => 0,
            'by_type' => []
        ];
        
        // Total notifications
        $stmt = $conn->prepare("SELECT COUNT(*) FROM order_notifications WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $stats['total'] = $stmt->fetchColumn();
        
        // Unread notifications
        $stmt = $conn->prepare("SELECT COUNT(*) FROM order_notifications WHERE user_id = ? AND is_read = FALSE");
        $stmt->execute([$user_id]);
        $stats['unread'] = $stmt->fetchColumn();
        
        // Today's notifications
        $stmt = $conn->prepare("SELECT COUNT(*) FROM order_notifications WHERE user_id = ? AND DATE(created_at) = CURDATE()");
        $stmt->execute([$user_id]);
        $stats['today'] = $stmt->fetchColumn();
        
        // This week's notifications
        $stmt = $conn->prepare("SELECT COUNT(*) FROM order_notifications WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stmt->execute([$user_id]);
        $stats['this_week'] = $stmt->fetchColumn();
        
        // By type
        $stmt = $conn->prepare("SELECT type, COUNT(*) as count FROM order_notifications WHERE user_id = ? GROUP BY type");
        $stmt->execute([$user_id]);
        $stats['by_type'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Error getting notification stats: " . $e->getMessage());
        return [
            'total' => 0,
            'unread' => 0,
            'today' => 0,
            'this_week' => 0,
            'by_type' => []
        ];
    }
}
}

/**
 * Clean old notifications (older than specified days)
 */
if (!function_exists('cleanOldNotifications')) {
function cleanOldNotifications($conn, $days = 30) {
    try {
        $stmt = $conn->prepare("
            DELETE FROM order_notifications 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            AND is_read = TRUE
        ");
        $stmt->execute([$days]);
        
        return $stmt->rowCount();
        
    } catch (Exception $e) {
        error_log("Error cleaning old notifications: " . $e->getMessage());
        return 0;
    }
}
}

/**
 * Get user notifications (fallback function for compatibility)
 */
if (!function_exists('getUserNotifications')) {
function getUserNotifications($conn, $user_id, $limit = 10) {
    try {
        // Try to get from order_notifications table first
        $stmt = $conn->prepare("
            SELECT
                notification_id,
                title,
                message,
                created_at,
                is_read,
                order_id,
                status_logo,
                type
            FROM order_notifications
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$user_id, $limit]);
        $notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (count($notifications) > 0) {
            return $notifications;
        }

    } catch (Exception $e) {
        // Table might not exist, try fallback
        error_log("getUserNotifications table error: " . $e->getMessage());
    }

    // Fallback: Create notifications from order status changes
    try {
        $stmt = $conn->prepare("
            SELECT
                CONCAT('order_', o.order_id) as notification_id,
                CONCAT('Order #', o.order_id, ' Status Update') as title,
                CONCAT('Your order status: ', COALESCE(o.order_status, o.status, 'pending')) as message,
                o.updated_at as created_at,
                0 as is_read,
                o.order_id,
                '📦' as status_logo,
                'status_update' as type
            FROM orders o
            WHERE o.user_id = ?
            ORDER BY o.updated_at DESC
            LIMIT ?
        ");
        $stmt->execute([$user_id, $limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("getUserNotifications fallback error: " . $e->getMessage());
        return [];
    }
}
}

/**
 * Get unread notifications count (fallback function for compatibility)
 */
if (!function_exists('getUnreadNotificationsCount')) {
function getUnreadNotificationsCount($conn, $user_id) {
    try {
        // Try to get from order_notifications table first
        $stmt = $conn->prepare("
            SELECT COUNT(*)
            FROM order_notifications
            WHERE user_id = ? AND is_read = FALSE
        ");
        $stmt->execute([$user_id]);
        return $stmt->fetchColumn();

    } catch (Exception $e) {
        // Table might not exist, return 0
        return 0;
    }
}
}

/**
 * Mark notification as read (fallback function for compatibility)
 */
if (!function_exists('markNotificationAsRead')) {
function markNotificationAsRead($conn, $notification_id, $user_id) {
    try {
        $stmt = $conn->prepare("
            UPDATE order_notifications
            SET is_read = TRUE
            WHERE notification_id = ? AND user_id = ?
        ");
        return $stmt->execute([$notification_id, $user_id]);

    } catch (Exception $e) {
        error_log("markNotificationAsRead error: " . $e->getMessage());
        return false;
    }
}
}

// Note: storeRealTimeSyncData function is defined in order_status_functions.php to avoid duplication
?>
