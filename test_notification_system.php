<?php
/**
 * TEWUNEED - Notification System Testing
 * Comprehensive testing for real-time notifications
 */

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

// Test configuration
$test_user_id = 999999;
$test_results = [];
$total_tests = 0;
$passed_tests = 0;

/**
 * Test helper function
 */
function runTest($test_name, $test_function) {
    global $test_results, $total_tests, $passed_tests;
    
    $total_tests++;
    $start_time = microtime(true);
    
    try {
        $result = $test_function();
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        
        if ($result['success']) {
            $passed_tests++;
            $test_results[] = [
                'name' => $test_name,
                'status' => 'PASS',
                'message' => $result['message'],
                'time' => $execution_time . 'ms',
                'details' => $result['details'] ?? null
            ];
        } else {
            $test_results[] = [
                'name' => $test_name,
                'status' => 'FAIL',
                'message' => $result['message'],
                'time' => $execution_time . 'ms',
                'error' => $result['error'] ?? null
            ];
        }
    } catch (Exception $e) {
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        $test_results[] = [
            'name' => $test_name,
            'status' => 'ERROR',
            'message' => 'Test execution failed',
            'time' => $execution_time . 'ms',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 1: Notifications Table Structure
 */
function testNotificationsTableStructure() {
    global $conn;
    
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'notifications'");
        $table_exists = $stmt->fetch();
        
        if ($table_exists) {
            $stmt = $conn->query("DESCRIBE notifications");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $required_columns = ['notification_id', 'user_id', 'title', 'message', 'is_read', 'created_at'];
            $existing_columns = array_column($columns, 'Field');
            
            $missing_columns = array_diff($required_columns, $existing_columns);
            
            if (empty($missing_columns)) {
                return [
                    'success' => true,
                    'message' => 'Notifications table structure is valid',
                    'details' => 'All required columns present: ' . implode(', ', $existing_columns)
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Notifications table structure incomplete',
                    'error' => 'Missing columns: ' . implode(', ', $missing_columns)
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Notifications table does not exist',
                'error' => 'Table "notifications" not found in database'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to check notifications table structure',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 2: Create Test Notification
 */
function testCreateNotification() {
    global $conn, $test_user_id;
    
    try {
        // Clean up any existing test notifications
        $stmt = $conn->prepare("DELETE FROM notifications WHERE user_id = ? AND title LIKE 'TEST:%'");
        $stmt->execute([$test_user_id]);
        
        // Create test notification
        $stmt = $conn->prepare("
            INSERT INTO notifications (user_id, title, message, type, is_read, created_at) 
            VALUES (?, 'TEST: Order Update', 'Your test order has been processed', 'order', FALSE, NOW())
        ");
        $result = $stmt->execute([$test_user_id]);
        
        if ($result) {
            $notification_id = $conn->lastInsertId();
            
            // Verify notification was created
            $stmt = $conn->prepare("SELECT * FROM notifications WHERE notification_id = ?");
            $stmt->execute([$notification_id]);
            $notification = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($notification) {
                return [
                    'success' => true,
                    'message' => 'Test notification created successfully',
                    'details' => "Notification ID: $notification_id, Title: {$notification['title']}"
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Notification not found after creation',
                    'error' => 'Verification failed'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Failed to create test notification',
                'error' => 'Insert operation failed'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Create notification test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 3: Mark Notification as Read
 */
function testMarkNotificationRead() {
    global $conn, $test_user_id;
    
    try {
        // Get a test notification
        $stmt = $conn->prepare("SELECT notification_id FROM notifications WHERE user_id = ? AND title LIKE 'TEST:%' LIMIT 1");
        $stmt->execute([$test_user_id]);
        $notification_id = $stmt->fetchColumn();
        
        if ($notification_id) {
            // Mark as read
            $stmt = $conn->prepare("UPDATE notifications SET is_read = TRUE WHERE notification_id = ?");
            $result = $stmt->execute([$notification_id]);
            
            if ($result) {
                // Verify update
                $stmt = $conn->prepare("SELECT is_read FROM notifications WHERE notification_id = ?");
                $stmt->execute([$notification_id]);
                $is_read = $stmt->fetchColumn();
                
                if ($is_read) {
                    return [
                        'success' => true,
                        'message' => 'Notification marked as read successfully',
                        'details' => "Notification ID: $notification_id"
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Notification read status not updated',
                        'error' => 'is_read field still FALSE'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to mark notification as read',
                    'error' => 'Update operation failed'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'No test notification found to mark as read',
                'error' => 'Test notification creation may have failed'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Mark notification read test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 4: Get Unread Notifications Count
 */
function testUnreadNotificationsCount() {
    global $conn, $test_user_id;
    
    try {
        // Create multiple test notifications
        $notifications = [
            ['title' => 'TEST: Unread 1', 'message' => 'Test unread notification 1'],
            ['title' => 'TEST: Unread 2', 'message' => 'Test unread notification 2'],
            ['title' => 'TEST: Unread 3', 'message' => 'Test unread notification 3']
        ];
        
        $created_count = 0;
        foreach ($notifications as $notif) {
            $stmt = $conn->prepare("
                INSERT INTO notifications (user_id, title, message, type, is_read, created_at) 
                VALUES (?, ?, ?, 'info', FALSE, NOW())
            ");
            if ($stmt->execute([$test_user_id, $notif['title'], $notif['message']])) {
                $created_count++;
            }
        }
        
        // Count unread notifications
        $stmt = $conn->prepare("SELECT COUNT(*) FROM notifications WHERE user_id = ? AND is_read = FALSE");
        $stmt->execute([$test_user_id]);
        $unread_count = $stmt->fetchColumn();
        
        if ($unread_count >= $created_count) {
            return [
                'success' => true,
                'message' => 'Unread notifications count working correctly',
                'details' => "Created: $created_count, Total unread: $unread_count"
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Unread notifications count mismatch',
                'error' => "Expected at least $created_count, got $unread_count"
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Unread notifications count test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 5: Notification AJAX Endpoint
 */
function testNotificationAjaxEndpoint() {
    try {
        $ajax_file = 'ajax/get_notifications.php';
        
        if (file_exists($ajax_file)) {
            // Check if file is readable
            if (is_readable($ajax_file)) {
                // Basic file content check
                $content = file_get_contents($ajax_file);
                
                if (strpos($content, 'notifications') !== false && strpos($content, 'json_encode') !== false) {
                    return [
                        'success' => true,
                        'message' => 'Notification AJAX endpoint exists and appears functional',
                        'details' => 'File: ' . $ajax_file
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Notification AJAX endpoint exists but may not be functional',
                        'error' => 'File content does not contain expected notification handling code'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'Notification AJAX endpoint is not readable',
                    'error' => 'File permissions issue'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Notification AJAX endpoint not found',
                'error' => "File $ajax_file does not exist"
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Notification AJAX endpoint test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 6: Real-time JavaScript Files
 */
function testRealtimeJavaScriptFiles() {
    try {
        $js_files = [
            'js/real-time-status-sync.js',
            'js/unified-status-sync.js',
            'js/order-status-sync.js'
        ];
        
        $existing_files = [];
        $missing_files = [];
        
        foreach ($js_files as $file) {
            if (file_exists($file)) {
                $existing_files[] = $file;
            } else {
                $missing_files[] = $file;
            }
        }
        
        if (!empty($existing_files)) {
            return [
                'success' => true,
                'message' => 'Real-time JavaScript files found',
                'details' => 'Existing files: ' . implode(', ', $existing_files) . 
                           (!empty($missing_files) ? ' | Missing: ' . implode(', ', $missing_files) : '')
            ];
        } else {
            return [
                'success' => false,
                'message' => 'No real-time JavaScript files found',
                'error' => 'Missing files: ' . implode(', ', $missing_files)
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Real-time JavaScript files test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 7: Clean Up Test Notifications
 */
function testCleanUpNotifications() {
    global $conn, $test_user_id;
    
    try {
        // Delete all test notifications
        $stmt = $conn->prepare("DELETE FROM notifications WHERE user_id = ? AND title LIKE 'TEST:%'");
        $stmt->execute([$test_user_id]);
        $deleted_count = $stmt->rowCount();
        
        return [
            'success' => true,
            'message' => 'Test notifications cleaned up successfully',
            'details' => "Deleted $deleted_count test notifications"
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Clean up notifications test failed',
            'error' => $e->getMessage()
        ];
    }
}

// Run all tests
runTest('Notifications Table Structure', 'testNotificationsTableStructure');
runTest('Create Test Notification', 'testCreateNotification');
runTest('Mark Notification as Read', 'testMarkNotificationRead');
runTest('Unread Notifications Count', 'testUnreadNotificationsCount');
runTest('Notification AJAX Endpoint', 'testNotificationAjaxEndpoint');
runTest('Real-time JavaScript Files', 'testRealtimeJavaScriptFiles');
runTest('Clean Up Test Notifications', 'testCleanUpNotifications');

// Calculate success rate
$success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEWUNEED - Notification System Test Results</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-error { color: #fd7e14; }
        .test-card { margin-bottom: 1rem; }
        .success-rate {
            font-size: 2rem;
            font-weight: bold;
        }
        .success-high { color: #28a745; }
        .success-medium { color: #ffc107; }
        .success-low { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4">
                        <i class="fas fa-bell me-3"></i>
                        Notification System Test Results
                    </h1>
                    <p class="lead">Comprehensive testing of real-time notification system</p>
                </div>

                <!-- Summary Card -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-md-3">
                                <h5>Total Tests</h5>
                                <span class="display-6"><?php echo $total_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Passed</h5>
                                <span class="display-6 test-pass"><?php echo $passed_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Failed</h5>
                                <span class="display-6 test-fail"><?php echo $total_tests - $passed_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Success Rate</h5>
                                <span class="success-rate <?php 
                                    echo $success_rate >= 80 ? 'success-high' : 
                                         ($success_rate >= 60 ? 'success-medium' : 'success-low'); 
                                ?>"><?php echo $success_rate; ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="row">
                    <?php foreach ($test_results as $test): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card test-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><?php echo htmlspecialchars($test['name']); ?></h6>
                                <span class="badge <?php 
                                    echo $test['status'] === 'PASS' ? 'bg-success' : 
                                         ($test['status'] === 'FAIL' ? 'bg-danger' : 'bg-warning'); 
                                ?>">
                                    <?php echo $test['status']; ?>
                                </span>
                            </div>
                            <div class="card-body">
                                <p class="mb-2">
                                    <i class="fas <?php 
                                        echo $test['status'] === 'PASS' ? 'fa-check-circle test-pass' : 
                                             ($test['status'] === 'FAIL' ? 'fa-times-circle test-fail' : 'fa-exclamation-triangle test-error'); 
                                    ?> me-2"></i>
                                    <?php echo htmlspecialchars($test['message']); ?>
                                </p>
                                
                                <?php if (isset($test['details'])): ?>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <?php echo htmlspecialchars($test['details']); ?>
                                </small>
                                <?php endif; ?>
                                
                                <?php if (isset($test['error'])): ?>
                                <div class="alert alert-danger alert-sm mt-2 mb-0">
                                    <small><strong>Error:</strong> <?php echo htmlspecialchars($test['error']); ?></small>
                                </div>
                                <?php endif; ?>
                                
                                <div class="text-end mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo $test['time']; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary me-2">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                    <a href="admin/dashboard.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                    </a>
                    <button onclick="window.location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-2"></i>Run Tests Again
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
