<?php
// Set error handling to prevent HTML output
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Always set JSON content type
header('Content-Type: application/json');

session_start();
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';
require_once 'auth.php';

// Check if admin is logged in
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    echo json_encode(['status' => 'error', 'message' => 'Unauthorized']);
    exit;
}

// Get JSON data
$data = json_decode(file_get_contents('php://input'), true);
if (!$data) $data = $_POST;

if (!isset($data['action'])) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
    exit();
}

error_log('Action received: ' . $data['action']);

try {
    switch ($data['action']) {
        case 'get':
            if (!isset($data['id'])) {
                throw new Exception('Product ID is required');
            }
            $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ?");
            $stmt->execute([$data['id']]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!$product) {
                throw new Exception('Product not found');
            }
            echo json_encode(['status' => 'success', 'data' => $product]);
            break;

        case 'delete':
            if (!isset($data['id'])) {
                throw new Exception('Product ID is required');
            }

            // Check if product exists
            $stmt = $conn->prepare("SELECT product_id FROM products WHERE product_id = ?");
            $stmt->execute([$data['id']]);
            if (!$stmt->fetch()) {
                throw new Exception('Product not found');
            }

            // Delete product
            $stmt = $conn->prepare("DELETE FROM products WHERE product_id = ?");
            $stmt->execute([$data['id']]);

            echo json_encode(['status' => 'success', 'message' => 'Product deleted successfully']);
            break;

        case 'edit':
            if (!isset($_POST['product_id']) || !isset($_POST['name']) || !isset($_POST['category_id']) || !isset($_POST['price']) || !isset($_POST['stock']) || !isset($_POST['description'])) {
                throw new Exception('Missing required fields');
            }
            $query = "UPDATE products SET NAME = ?, category_id = ?, price = ?, stock = ?, is_active = ?, description = ?";
            $params = [
                $_POST['name'],
                $_POST['category_id'],
                $_POST['price'],
                $_POST['stock'],
                isset($_POST['is_active']) ? $_POST['is_active'] : 1,
                $_POST['description']
            ];
            // Handle image upload if any
            if (isset($_FILES['image']) && $_FILES['image']['error'] == UPLOAD_ERR_OK) {
                $targetDir = __DIR__ . '/../uploads/';
                if (!is_dir($targetDir)) mkdir($targetDir, 0777, true);

                // Validate file type
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                $fileType = $_FILES['image']['type'];
                if (!in_array($fileType, $allowedTypes)) {
                    throw new Exception('Tipe file tidak didukung. Gunakan JPG, PNG, atau GIF.');
                }

                // Validate file size (max 2MB)
                if ($_FILES['image']['size'] > 2 * 1024 * 1024) {
                    throw new Exception('Ukuran file terlalu besar. Maksimal 2MB.');
                }

                $fileName = uniqid() . '_' . basename($_FILES["image"]["name"]);
                $targetFile = $targetDir . $fileName;
                if (move_uploaded_file($_FILES["image"]["tmp_name"], $targetFile)) {
                    $query .= ", image = ?";
                    $params[] = $fileName;
                } else {
                    throw new Exception('Gagal upload gambar');
                }
            }
            $query .= " WHERE product_id = ?";
            $params[] = $_POST['product_id'];
            $stmt = $conn->prepare($query);
            $stmt->execute($params);
            echo json_encode(['status' => 'success', 'message' => 'Product updated successfully']);
            break;

        case 'add':
            if (!isset($_POST['name']) || !isset($_POST['category_id']) || !isset($_POST['price']) || !isset($_POST['stock']) || !isset($_POST['description'])) {
                throw new Exception('Missing required fields');
            }
            $imageFileName = null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] == UPLOAD_ERR_OK) {
                $targetDir = __DIR__ . '/../uploads/';
                if (!is_dir($targetDir)) mkdir($targetDir, 0777, true);

                // Validate file type
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                $fileType = $_FILES['image']['type'];
                if (!in_array($fileType, $allowedTypes)) {
                    throw new Exception('Tipe file tidak didukung. Gunakan JPG, PNG, atau GIF.');
                }

                // Validate file size (max 2MB)
                if ($_FILES['image']['size'] > 2 * 1024 * 1024) {
                    throw new Exception('Ukuran file terlalu besar. Maksimal 2MB.');
                }

                $imageFileName = uniqid() . '_' . basename($_FILES["image"]["name"]);
                $targetFile = $targetDir . $imageFileName;
                if (!move_uploaded_file($_FILES["image"]["tmp_name"], $targetFile)) {
                    throw new Exception('Gagal upload gambar');
                }
            }
            $stmt = $conn->prepare("INSERT INTO products (NAME, category_id, price, stock, is_active, description, image) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                $_POST['name'],
                $_POST['category_id'],
                $_POST['price'],
                $_POST['stock'],
                isset($_POST['is_active']) ? $_POST['is_active'] : 1,
                $_POST['description'],
                $imageFileName
            ]);
            echo json_encode(['status' => 'success', 'message' => 'Product added successfully']);
            break;

        default:
            echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
    }
} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}