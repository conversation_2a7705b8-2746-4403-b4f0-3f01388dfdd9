/* Reset default margin and padding */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  /* CSS styles for the body section */
  body {
    font-family: 'Poppins', Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
    line-height: 1.6;
  }
  
  header {
    background-color: #fff;
    border-radius: 10px;
    margin-bottom: 20px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  #logo {
    height: 240px;
    width: 350px;
    display: block;
    margin: 0 auto;
  }
  
  nav {
    background-color: #006ca5;
    height: 10vh;
    width: 100%;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: 20px 0;
  }
  
  nav a {
    position: relative;
    text-decoration: none;
    color: beige;
    font-size: 15px;
    transition: color 0.3s;
  }
  
  nav a:hover {
    color: #fff;
  }
  
  nav a::after {
    content: "";
    background-color: aliceblue;
    width: 0;
    height: 3px;
    display: block;
    margin: auto;
    transition: width 0.3s;
  }
  
  nav a:hover::after {
    width: 100%;
  }
  
  .section {
    color: #333;
    background-color: #006ca5;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
  }

#dairy{
    background-color: #dfebf6;
    color: #242526;
    text-align: center;
}

.flex-item img{
    height: 240px;
    width: 240px;
}

.flex-item{
    margin: 8px;
    width: 250px;
    border-style: solid;
    border-radius: 10px;
    border-width: 1px;
    border-color: #dfebf6;
}

.flex-item:hover{
    transform: scale(1.05);
    transition: 0.3s;
    border-color: #003152;
}

.flex-container{
    display:flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 25px;
    padding: 20px;
}

h2{
    text-align: center;
    font-size: 16px;
    padding: 20px;
}

.desc{
    border-style:none;
    border-radius: 10px;
    border-width: 1px;
    padding: 10px;
    background-color: #dfebf6;
    font-size: small;
    color:#152238;
    text-align: center;
}

.ordernow{
    text-align: center;
    margin-top: 10px;
}

.ordernow a{
    color: green;
    font-size: small;
    font-weight: 600;
}

.ordernow a:hover{
    font-weight: bolder;
    transition: 0.3s;
}

/*CSS Styles for the footer section*/

footer{
    background-color:#dfebf6;
    border-radius: 10px;
    color: #242526;
    font-size: 14px;
}

.footer{
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    margin: auto;
}

.footer_column{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.footer_column a{
    color: #006ca5;
    font-size: 13px;
    transition: 0.2s;
}

.footer_column a:hover{
    font-weight: bold;
}

.footer_title p{
    display: flex;
    justify-content:center;
    text-align:center;
    align-items:center;
    font-size: 14px;
    font-weight:bold;
}

.icon_sm{
    transition: 0.2s;
    display:inline-block;
}

.icon_sm:hover{
    color: rgb(72, 134, 192);
}

/*Mobile view*/

@media screen and (max-width:600px){
    body{
        margin: 6px;
    }
    
    #logo{
        width: 100%;
        height: auto;
    }

    nav{
        flex-direction: column;
        align-items: center;
        height: 50vh;
    }

    .flex-container{
        justify-content: center;
    }
    
    .flex-item{
        flex-direction: column;
    }

    .ordernow a{
        font-weight: bolder;
    }

    .ordernow a:hover{
        font-weight: initial;
    }

    .footer{
        flex-direction: column;
        align-items: center;
    }
}