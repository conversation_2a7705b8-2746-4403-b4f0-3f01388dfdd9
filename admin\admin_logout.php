<?php
/**
 * Admin Logout Page
 * Handles secure logout functionality
 */

require_once '../includes/auth_helper.php';

// Ensure session is started with secure parameters
ensureSession();

// Get user ID before logout for activity logging
$userId = $_SESSION['user_id'] ?? 0;
$allDevices = isset($_GET['all']) && $_GET['all'] == '1';

// Perform secure logout
performAdminLogout($userId, $allDevices);

// Set a message for the login page
$_SESSION['login_message'] = 'Anda telah berhasil logout.';

// Redirect to admin login page
header('Location: admin_login.php');
exit();