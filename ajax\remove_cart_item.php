<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';
require_once '../includes/StockReservation.php';

// Set response header to JSON
header('Content-Type: application/json');

// Verify that the user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Silakan login untuk menghapus produk dari keranjang'
    ]);
    exit;
}

// Validate product_id
if (!isset($_POST['product_id']) || !is_numeric($_POST['product_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'ID produk tidak valid'
    ]);
    exit;
}

$product_id = (int)$_POST['product_id'];

try {
    global $conn;

    // Begin transaction for data consistency
    $conn->beginTransaction();

    // Get user's cart id
    $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$cart) {
        throw new Exception('Keranjang belanja tidak ditemukan');
    }

    $cart_id = $cart['cart_id'];

    // Get product info and current cart item quantity for logging and reservation cancellation
    $stmt = $conn->prepare("
        SELECT p.NAME, ci.quantity
        FROM products p
        JOIN cart_items ci ON p.product_id = ci.product_id
        WHERE p.product_id = ? AND ci.cart_id = ?
    ");
    $stmt->execute([$product_id, $cart_id]);
    $productInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    $productName = $productInfo ? $productInfo['NAME'] : 'Unknown product';
    $cartQuantity = $productInfo ? $productInfo['quantity'] : 0;

    // Initialize stock reservation system and cancel reservation
    $stockReservation = new StockReservation($conn);
    $cancelResult = $stockReservation->cancelReservation($product_id, $_SESSION['user_id'], null, $cartQuantity);
    if (!$cancelResult['success']) {
        error_log("Warning: Could not cancel reservation for product {$product_id}: " . $cancelResult['message']);
        // Continue anyway, as the cart item should still be removed
    }

    // Remove item from cart
    $stmt = $conn->prepare("DELETE FROM cart_items WHERE cart_id = ? AND product_id = ?");
    $stmt->execute([$cart_id, $product_id]);

    if ($stmt->rowCount() === 0) {
        throw new Exception('Item tidak ditemukan di keranjang');
    }

    // Commit transaction
    $conn->commit();

    // Log activity
    logActivity($_SESSION['user_id'], 'cart_remove', "Removed product ID: {$product_id} ({$productName}) from cart");

    echo json_encode([
        'success' => true,
        'message' => 'Produk berhasil dihapus dari keranjang',
        'product_id' => $product_id
    ]);

} catch (Exception $e) {
    // Rollback transaction in case of error
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
