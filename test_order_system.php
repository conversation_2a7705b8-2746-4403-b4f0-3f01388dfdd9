<?php
/**
 * TEWUNEED - Order System Testing
 * Comprehensive testing for order processing and tracking
 */

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/order_status_functions.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

// Test configuration
$test_user_id = 999999;
$test_product_id = 1;
$test_order_id = null;
$test_results = [];
$total_tests = 0;
$passed_tests = 0;

/**
 * Test helper function
 */
function runTest($test_name, $test_function) {
    global $test_results, $total_tests, $passed_tests;
    
    $total_tests++;
    $start_time = microtime(true);
    
    try {
        $result = $test_function();
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        
        if ($result['success']) {
            $passed_tests++;
            $test_results[] = [
                'name' => $test_name,
                'status' => 'PASS',
                'message' => $result['message'],
                'time' => $execution_time . 'ms',
                'details' => $result['details'] ?? null
            ];
        } else {
            $test_results[] = [
                'name' => $test_name,
                'status' => 'FAIL',
                'message' => $result['message'],
                'time' => $execution_time . 'ms',
                'error' => $result['error'] ?? null
            ];
        }
    } catch (Exception $e) {
        $execution_time = round((microtime(true) - $start_time) * 1000, 2);
        $test_results[] = [
            'name' => $test_name,
            'status' => 'ERROR',
            'message' => 'Test execution failed',
            'time' => $execution_time . 'ms',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 1: Orders Table Structure
 */
function testOrdersTableStructure() {
    global $conn;
    
    try {
        $stmt = $conn->query("DESCRIBE orders");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $required_columns = ['order_id', 'user_id', 'total_amount', 'order_status', 'created_at'];
        $existing_columns = array_column($columns, 'Field');
        
        $missing_columns = array_diff($required_columns, $existing_columns);
        
        if (empty($missing_columns)) {
            return [
                'success' => true,
                'message' => 'Orders table structure is valid',
                'details' => 'All required columns present: ' . implode(', ', $existing_columns)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Orders table structure incomplete',
                'error' => 'Missing columns: ' . implode(', ', $missing_columns)
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to check orders table structure',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 2: Create Test Order
 */
function testCreateOrder() {
    global $conn, $test_user_id, $test_product_id, $test_order_id;
    
    try {
        // Clean up any existing test orders
        $stmt = $conn->prepare("DELETE FROM orders WHERE user_id = ? AND total_amount = 99999.99");
        $stmt->execute([$test_user_id]);
        
        // Create test order
        $stmt = $conn->prepare("
            INSERT INTO orders (user_id, total_amount, shipping_address, payment_method, order_status, created_at) 
            VALUES (?, 99999.99, 'Test Address 123', 'bank_transfer', 'pending', NOW())
        ");
        $result = $stmt->execute([$test_user_id]);
        
        if ($result) {
            $test_order_id = $conn->lastInsertId();
            
            // Add order items
            $stmt = $conn->prepare("
                INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) 
                VALUES (?, ?, 1, 99999.99, 99999.99)
            ");
            $stmt->execute([$test_order_id, $test_product_id]);
            
            return [
                'success' => true,
                'message' => 'Test order created successfully',
                'details' => "Order ID: $test_order_id"
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to create test order',
                'error' => 'Insert operation failed'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Create order test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 3: Order Status Update
 */
function testOrderStatusUpdate() {
    global $conn, $test_order_id;
    
    if (!$test_order_id) {
        return [
            'success' => false,
            'message' => 'No test order available for status update',
            'error' => 'Test order creation failed'
        ];
    }
    
    try {
        // Update order status
        $new_status = 'processing';
        $stmt = $conn->prepare("UPDATE orders SET order_status = ? WHERE order_id = ?");
        $result = $stmt->execute([$new_status, $test_order_id]);
        
        if ($result) {
            // Verify update
            $stmt = $conn->prepare("SELECT order_status FROM orders WHERE order_id = ?");
            $stmt->execute([$test_order_id]);
            $current_status = $stmt->fetchColumn();
            
            if ($current_status === $new_status) {
                return [
                    'success' => true,
                    'message' => 'Order status updated successfully',
                    'details' => "Status changed to: $current_status"
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Status update verification failed',
                    'error' => "Expected: $new_status, Got: $current_status"
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Failed to update order status',
                'error' => 'Update operation failed'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Order status update test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 4: Order Status History
 */
function testOrderStatusHistory() {
    global $conn, $test_order_id;
    
    if (!$test_order_id) {
        return [
            'success' => false,
            'message' => 'No test order available for history check',
            'error' => 'Test order creation failed'
        ];
    }
    
    try {
        // Check if order_status_history table exists
        $stmt = $conn->query("SHOW TABLES LIKE 'order_status_history'");
        $table_exists = $stmt->fetch();
        
        if ($table_exists) {
            // Check for status history entries
            $stmt = $conn->prepare("SELECT COUNT(*) FROM order_status_history WHERE order_id = ?");
            $stmt->execute([$test_order_id]);
            $history_count = $stmt->fetchColumn();
            
            return [
                'success' => true,
                'message' => 'Order status history tracking working',
                'details' => "History entries: $history_count"
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Order status history table not found',
                'error' => 'order_status_history table does not exist'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Order status history test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 5: Order Items Relationship
 */
function testOrderItemsRelationship() {
    global $conn, $test_order_id, $test_product_id;
    
    if (!$test_order_id) {
        return [
            'success' => false,
            'message' => 'No test order available for items check',
            'error' => 'Test order creation failed'
        ];
    }
    
    try {
        // Check order items
        $stmt = $conn->prepare("
            SELECT oi.*, p.name as product_name 
            FROM order_items oi 
            LEFT JOIN products p ON oi.product_id = p.product_id 
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$test_order_id]);
        $order_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($order_items)) {
            $item_count = count($order_items);
            $total_value = array_sum(array_column($order_items, 'total_price'));
            
            return [
                'success' => true,
                'message' => 'Order items relationship working correctly',
                'details' => "Items: $item_count, Total value: " . number_format($total_value, 2)
            ];
        } else {
            return [
                'success' => false,
                'message' => 'No order items found',
                'error' => 'Order items relationship failed'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Order items relationship test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 6: Order Status Functions
 */
function testOrderStatusFunctions() {
    try {
        // Test getOrderStatuses function
        if (function_exists('getOrderStatuses')) {
            $statuses = getOrderStatuses();
            
            if (is_array($statuses) && !empty($statuses)) {
                $status_count = count($statuses);
                $status_keys = array_keys($statuses);
                
                return [
                    'success' => true,
                    'message' => 'Order status functions working correctly',
                    'details' => "Available statuses: " . implode(', ', $status_keys)
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Order status functions returned empty result',
                    'error' => 'getOrderStatuses() returned invalid data'
                ];
            }
        } else {
            return [
                'success' => false,
                'message' => 'Order status functions not found',
                'error' => 'getOrderStatuses() function does not exist'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Order status functions test failed',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Test 7: Clean Up Test Data
 */
function testCleanUp() {
    global $conn, $test_user_id, $test_order_id;
    
    try {
        $deleted_items = 0;
        $deleted_orders = 0;
        
        // Delete order items
        if ($test_order_id) {
            $stmt = $conn->prepare("DELETE FROM order_items WHERE order_id = ?");
            $stmt->execute([$test_order_id]);
            $deleted_items = $stmt->rowCount();
        }
        
        // Delete test orders
        $stmt = $conn->prepare("DELETE FROM orders WHERE user_id = ? AND total_amount = 99999.99");
        $stmt->execute([$test_user_id]);
        $deleted_orders = $stmt->rowCount();
        
        return [
            'success' => true,
            'message' => 'Test data cleaned up successfully',
            'details' => "Deleted: $deleted_orders orders, $deleted_items order items"
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Clean up test failed',
            'error' => $e->getMessage()
        ];
    }
}

// Run all tests
runTest('Orders Table Structure', 'testOrdersTableStructure');
runTest('Create Test Order', 'testCreateOrder');
runTest('Order Status Update', 'testOrderStatusUpdate');
runTest('Order Status History', 'testOrderStatusHistory');
runTest('Order Items Relationship', 'testOrderItemsRelationship');
runTest('Order Status Functions', 'testOrderStatusFunctions');
runTest('Clean Up Test Data', 'testCleanUp');

// Calculate success rate
$success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEWUNEED - Order System Test Results</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-error { color: #fd7e14; }
        .test-card { margin-bottom: 1rem; }
        .success-rate {
            font-size: 2rem;
            font-weight: bold;
        }
        .success-high { color: #28a745; }
        .success-medium { color: #ffc107; }
        .success-low { color: #dc3545; }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h1 class="display-4">
                        <i class="fas fa-shopping-bag me-3"></i>
                        Order System Test Results
                    </h1>
                    <p class="lead">Comprehensive testing of order processing and tracking</p>
                </div>

                <!-- Summary Card -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <div class="row">
                            <div class="col-md-3">
                                <h5>Total Tests</h5>
                                <span class="display-6"><?php echo $total_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Passed</h5>
                                <span class="display-6 test-pass"><?php echo $passed_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Failed</h5>
                                <span class="display-6 test-fail"><?php echo $total_tests - $passed_tests; ?></span>
                            </div>
                            <div class="col-md-3">
                                <h5>Success Rate</h5>
                                <span class="success-rate <?php 
                                    echo $success_rate >= 80 ? 'success-high' : 
                                         ($success_rate >= 60 ? 'success-medium' : 'success-low'); 
                                ?>"><?php echo $success_rate; ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="row">
                    <?php foreach ($test_results as $test): ?>
                    <div class="col-md-6 mb-3">
                        <div class="card test-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><?php echo htmlspecialchars($test['name']); ?></h6>
                                <span class="badge <?php 
                                    echo $test['status'] === 'PASS' ? 'bg-success' : 
                                         ($test['status'] === 'FAIL' ? 'bg-danger' : 'bg-warning'); 
                                ?>">
                                    <?php echo $test['status']; ?>
                                </span>
                            </div>
                            <div class="card-body">
                                <p class="mb-2">
                                    <i class="fas <?php 
                                        echo $test['status'] === 'PASS' ? 'fa-check-circle test-pass' : 
                                             ($test['status'] === 'FAIL' ? 'fa-times-circle test-fail' : 'fa-exclamation-triangle test-error'); 
                                    ?> me-2"></i>
                                    <?php echo htmlspecialchars($test['message']); ?>
                                </p>
                                
                                <?php if (isset($test['details'])): ?>
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <?php echo htmlspecialchars($test['details']); ?>
                                </small>
                                <?php endif; ?>
                                
                                <?php if (isset($test['error'])): ?>
                                <div class="alert alert-danger alert-sm mt-2 mb-0">
                                    <small><strong>Error:</strong> <?php echo htmlspecialchars($test['error']); ?></small>
                                </div>
                                <?php endif; ?>
                                
                                <div class="text-end mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        <?php echo $test['time']; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Actions -->
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-primary me-2">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                    <a href="admin/dashboard.php" class="btn btn-outline-primary me-2">
                        <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                    </a>
                    <button onclick="window.location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-2"></i>Run Tests Again
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
