<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/StockReservation.php';

session_start();

$action = $_POST['action'] ?? $_GET['action'] ?? '';

switch($action) {
    case 'reserve':
        // Reserve stock for cart
        $productId = $_POST['product_id'] ?? 0;
        $quantity = $_POST['quantity'] ?? 1;
        $userId = $_SESSION['user_id'] ?? null;
        $sessionId = session_id();
        
        try {
            $stockReservation = new StockReservation($conn);
            $result = $stockReservation->reserveStock($productId, $quantity, $userId, $sessionId);
            
            if ($result['success']) {
                // Get available stock after reservation
                $availableStock = $stockReservation->getAvailableStock($productId);
                echo json_encode([
                    'success' => true,
                    'message' => $result['message'],
                    'available_stock' => $availableStock
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error reserving stock: ' . $e->getMessage()
            ]);
        }
        break;
        
    case 'cancel':
        // Cancel stock reservation
        $productId = $_POST['product_id'] ?? 0;
        $quantity = $_POST['quantity'] ?? 1;
        $userId = $_SESSION['user_id'] ?? null;
        $sessionId = session_id();
        
        try {
            $stockReservation = new StockReservation($conn);
            $result = $stockReservation->cancelReservation($productId, $userId, $sessionId, $quantity);
            
            echo json_encode([
                'success' => $result['success'],
                'message' => $result['message']
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error cancelling reservation: ' . $e->getMessage()
            ]);
        }
        break;
        
    case 'check':
        // Check available stock
        $productId = $_GET['product_id'] ?? 0;
        
        try {
            $stockReservation = new StockReservation($conn);
            $availableStock = $stockReservation->getAvailableStock($productId);
            
            echo json_encode([
                'success' => true,
                'available_stock' => $availableStock
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error checking stock: ' . $e->getMessage()
            ]);
        }
        break;
        
    default:
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
        break;
}
?>
