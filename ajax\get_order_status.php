<?php
session_start();
header('Content-Type: application/json');

try {
    require_once '../includes/db_connect.php';
    require_once '../includes/order_status_functions.php';
    
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => 'Not authenticated']);
        exit;
    }

    $user_id = $_SESSION['user_id'];
    $user_role = $_SESSION['user_role'] ?? 'user';
    
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    $last_update = $input['last_update'] ?? 0;
    $last_update_time = date('Y-m-d H:i:s', $last_update / 1000);

    $response = [
        'success' => true,
        'updates' => [],
        'notifications' => [],
        'timestamp' => time() * 1000
    ];

    // Get order updates based on user role
    if ($user_role === 'admin') {
        // Admin can see all order updates
        $stmt = $conn->prepare("
            SELECT o.order_id, o.current_status as new_status, o.tracking_number, 
                   o.updated_at, u.full_name as customer_name
            FROM orders o
            LEFT JOIN users u ON o.user_id = u.user_id
            WHERE o.updated_at > ?
            ORDER BY o.updated_at DESC
            LIMIT 20
        ");
        $stmt->execute([$last_update_time]);
    } else {
        // Regular users only see their own orders
        $stmt = $conn->prepare("
            SELECT order_id, current_status as new_status, tracking_number, updated_at
            FROM orders 
            WHERE user_id = ? AND updated_at > ?
            ORDER BY updated_at DESC
            LIMIT 10
        ");
        $stmt->execute([$user_id, $last_update_time]);
    }
    
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $statuses = getOrderStatuses();

    // Process order updates
    foreach ($orders as $order) {
        $status_info = $statuses[$order['new_status']] ?? $statuses['pending'];
        
        $update = [
            'order_id' => $order['order_id'],
            'new_status' => $order['new_status'],
            'tracking_number' => $order['tracking_number'],
            'updated_at' => $order['updated_at'],
            'status_info' => $status_info
        ];
        
        if (isset($order['customer_name'])) {
            $update['customer_name'] = $order['customer_name'];
        }
        
        $response['updates'][] = $update;
    }

    // Get new notifications for the user
    $notifications = getUserNotifications($conn, $user_id, 5, true); // Only unread
    
    // Filter notifications that are newer than last update
    $new_notifications = array_filter($notifications, function($notification) use ($last_update_time) {
        return strtotime($notification['created_at']) > strtotime($last_update_time);
    });

    $response['notifications'] = array_values($new_notifications);
    
    // Get unread notification count
    $response['unread_count'] = getUnreadNotificationsCount($conn, $user_id);

    echo json_encode($response);

} catch (Exception $e) {
    error_log("Error in get_order_status.php: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
