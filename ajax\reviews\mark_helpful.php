<?php
session_start();
require_once "../../includes/db_connect.php";

header("Content-Type: application/json");

if (!isset($_SESSION["user_id"])) {
    echo json_encode(["success" => false, "message" => "Please login first"]);
    exit;
}

$review_id = $_POST["review_id"] ?? 0;
$is_helpful = $_POST["is_helpful"] ?? 1;

try {
    // Check if user already voted
    $stmt = $conn->prepare("
        SELECT helpfulness_id FROM review_helpfulness 
        WHERE review_id = ? AND user_id = ?
    ");
    $stmt->execute([$review_id, $_SESSION["user_id"]]);
    $existing_vote = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing_vote) {
        // Update existing vote
        $stmt = $conn->prepare("
            UPDATE review_helpfulness 
            SET is_helpful = ?, updated_at = NOW() 
            WHERE helpfulness_id = ?
        ");
        $stmt->execute([$is_helpful, $existing_vote["helpfulness_id"]]);
    } else {
        // Create new vote
        $stmt = $conn->prepare("
            INSERT INTO review_helpfulness (review_id, user_id, is_helpful, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$review_id, $_SESSION["user_id"], $is_helpful]);
    }
    
    // Get updated counts
    $stmt = $conn->prepare("
        SELECT 
            SUM(CASE WHEN is_helpful = 1 THEN 1 ELSE 0 END) as helpful_count,
            SUM(CASE WHEN is_helpful = 0 THEN 1 ELSE 0 END) as not_helpful_count
        FROM review_helpfulness 
        WHERE review_id = ?
    ");
    $stmt->execute([$review_id]);
    $counts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        "success" => true,
        "helpful_count" => $counts["helpful_count"] ?? 0,
        "not_helpful_count" => $counts["not_helpful_count"] ?? 0
    ]);
} catch (Exception $e) {
    echo json_encode(["success" => false, "message" => $e->getMessage()]);
}
?>