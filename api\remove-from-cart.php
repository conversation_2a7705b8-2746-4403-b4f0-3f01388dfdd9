<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set response header to JSON
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Parse JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Log request data for debugging
error_log('Remove from Cart Request: ' . $input);

// Check if data is valid
if (!isset($data['product_id'])) {
    $response['message'] = 'Missing product ID';
    echo json_encode($response);
    exit;
}

$product_id = intval($data['product_id']);

try {
    // Check if cart exists
    if (!isset($_SESSION['cart']) || !is_array($_SESSION['cart'])) {
        $response['message'] = 'Cart is empty';
        $response['success'] = true; // Not an error, just empty
        echo json_encode($response);
        exit;
    }

    // Find and remove product from cart
    $found = false;
    $new_cart = [];
    
    foreach ($_SESSION['cart'] as $item) {
        if ($item['product_id'] == $product_id) {
            $found = true;
            continue; // Skip this item (remove it)
        }
        $new_cart[] = $item;
    }
    
    if (!$found) {
        $response['message'] = 'Product not found in cart';
    } else {
        // Update cart
        $_SESSION['cart'] = $new_cart;
        
        $response['success'] = true;
        $response['message'] = 'Product removed from cart';
        
        // Get updated cart count
        $cart_count = 0;
        foreach ($_SESSION['cart'] as $item) {
            $cart_count += isset($item['quantity']) ? $item['quantity'] : 1;
        }
        
        $response['data'] = [
            'cart_count' => $cart_count
        ];
    }
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    error_log('Remove from cart error: ' . $e->getMessage());
}

// Return JSON response
echo json_encode($response);
