<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login to update cart']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    $cart_item_id = $_POST['cart_item_id'] ?? 0;
    $quantity = (int)($_POST['quantity'] ?? 1);
    
    // Validate inputs
    if (!$cart_item_id || $quantity < 1) {
        echo json_encode(['success' => false, 'message' => 'Invalid cart item or quantity']);
        exit;
    }
    
    // Check if cart item belongs to user
    $stmt = $conn->prepare("
        SELECT ci.*, p.stock, p.NAME as product_name, p.price
        FROM cart_items ci
        JOIN carts c ON ci.cart_id = c.cart_id
        JOIN products p ON ci.product_id = p.product_id
        WHERE ci.cart_item_id = ? AND c.user_id = ?
    ");
    $stmt->execute([$cart_item_id, $user_id]);
    $cart_item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$cart_item) {
        echo json_encode(['success' => false, 'message' => 'Cart item not found']);
        exit;
    }
    
    // Check stock availability
    if ($quantity > $cart_item['stock']) {
        echo json_encode([
            'success' => false, 
            'message' => "Only {$cart_item['stock']} items available in stock"
        ]);
        exit;
    }
    
    // Update cart item quantity
    $stmt = $conn->prepare("
        UPDATE cart_items 
        SET quantity = ?, updated_at = NOW() 
        WHERE cart_item_id = ?
    ");
    $result = $stmt->execute([$quantity, $cart_item_id]);
    
    if ($result) {
        // Calculate new totals
        $stmt = $conn->prepare("
            SELECT 
                SUM(ci.quantity) as total_items,
                SUM(ci.quantity * p.price) as total_amount
            FROM cart_items ci
            JOIN carts c ON ci.cart_id = c.cart_id
            JOIN products p ON ci.product_id = p.product_id
            WHERE c.user_id = ?
        ");
        $stmt->execute([$user_id]);
        $totals = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Log activity
        $activity_stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, description, created_at) 
            VALUES (?, 'cart_update', ?, NOW())
        ");
        $activity_stmt->execute([
            $user_id,
            "Updated cart item: {$cart_item['product_name']} (Qty: {$quantity})"
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Cart updated successfully',
            'data' => [
                'cart_item_id' => $cart_item_id,
                'new_quantity' => $quantity,
                'item_total' => $quantity * $cart_item['price'],
                'cart_total_items' => $totals['total_items'] ?? 0,
                'cart_total_amount' => $totals['total_amount'] ?? 0
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update cart']);
    }
    
} catch (Exception $e) {
    error_log("Update cart error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while updating cart']);
}
?>
