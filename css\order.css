/* General reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    
  }
  
  body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
  }
  
  
  .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  /* Header Styles */
  header {
    width: 100%;
  }
  
  /* Logo styling */
  .logo-text {
    font-size: 24px;
    font-weight: 700;
    text-decoration: none;
    letter-spacing: 0.5px;
  }
  
  .text-blue-600 {
    color: #2563eb;
  }
  
  .text-yellow-500 {
    color: #f59e0b;
  }
  
  /* Search bar styles */
  .search-bar {
    background-color: white;
    height: 40px;
    border-radius: 9999px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    font-size: 14px;
  }
  
  .search-bar:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
  
  /* Navigation styles */
  .nav-link {
    font-size: 15px;
    font-weight: 500;
    text-decoration: none;
    padding: 8px 0;
    position: relative;
    transition: color 0.3s;
  }
  
  .nav-link.active {
    color: #2563eb;
    font-weight: 600;
  }
  
  .nav-link.active:after,
  .nav-link:hover:after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #2563eb;
    transform: scaleX(1);
  }
  
  .nav-link:after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #2563eb;
    transform: scaleX(0);
    transition: transform 0.3s ease-in-out;
  }
  
  .nav-link:hover:after {
    transform: scaleX(1);
  }
  
  /* Utility classes */
  .flex {
    display: flex;
  }
  
  .items-center {
    align-items: center;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  
  .justify-center {
    justify-content: center;
  }
  
  .space-x-4 > * + * {
    margin-left: 1rem;
  }
  
  .space-x-6 > * + * {
    margin-left: 1.5rem;
  }
  
  .space-x-8 > * + * {
    margin-left: 2rem;
  }
  
  .hidden {
    display: none;
  }
  
  .text-gray-700 {
    color: #374151;
  }
  
  .text-gray-400 {
    color: #9ca3af;
  }
  
  .hover\:text-blue-600:hover {
    color: #2563eb;
  }
  
  .bg-white {
    background-color: white;
  }
  
  .bg-gray-50 {
    background-color: #f9fafb;
  }
  
  .shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .shadow-inner {
    box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  }
  
  .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  .py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }
  
  .pl-10 {
    padding-left: 2.5rem;
  }
  
  .pr-4 {
    padding-right: 1rem;
  }
  
  .text-xl {
    font-size: 1.25rem;
  }
  
  .rounded-full {
    border-radius: 9999px;
  }
  
  .border {
    border-width: 1px;
  }
  
  .border-gray-300 {
    border-color: #d1d5db;
  }
  
  .focus\:outline-none:focus {
    outline: none;
  }
  
  .w-64 {
    width: 16rem;
  }
  
  .transform {
    transform: translateY(-50%);
  }
  
  .absolute {
    position: absolute;
  }
  
  .relative {
    position: relative;
  }
  
  .left-4 {
    left: 1rem;
  }
  
  .top-1\/2 {
    top: 50%;
  }
  
  .transform {
    transform: translateY(-50%);
  }
  
  /* Media Queries */
  @media (min-width: 768px) {
    .md\:flex {
      display: flex;
    }
    
    .md\:block {
      display: block;
    }
    
    .md\:hidden {
      display: none;
    }
    
    .md\:justify-start {
      justify-content: flex-start;
    }
  }
  
  @media (max-width: 767px) {
    .nav-link {
      font-size: 14px;
      padding: 6px 0;
    }
    
    .space-x-8 > * + * {
      margin-left: 1rem;
    }
  }
  
  @media (max-width: 640px) {
    .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }
    
    .logo-text {
      font-size: 20px;
    }
  }
  .auth-links {
    display: flex;
    align-items: center;
  }
  
  .auth-links a {
    margin-left: 20px;
    text-decoration: none;
    color: var(--dark-text);
  }
  
  .checkout-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.product-img {
  width: 80px;
  height: 80px;
  object-fit: contain;
}
.quantity-control {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Logo Keranjang */
.cart-icon-container {
  position: relative;
  display: inline-flex;
  cursor: pointer;
  margin-right: 15px;
}

.fa-shopping-cart {
  font-size: 20px;
  color: #333;
}

.cart-counter {
  position: absolute; 
  top: -10px;
  right: -10px;
  background-color: #ff3366;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
}

.step {
    text-align: center;
    flex: 1;
    position: relative;
}
.step-number {
    width: 30px;
    height: 30px;
    background: #ddd;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}
.step.active .step-number {
    background: #0d6efd;
    color: white;
}
.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 60%;
    right: 0;
    height: 2px;
    background: #ddd;
    z-index: -1;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    #dairy h1 {
        font-size: 2rem;
    }

    #dairy .lead {
        font-size: 1rem;
    }

    .container {
        padding: 0 10px;
    }
}
  
/*CSS Styles for the footer section*/
  
/* Footer Styles */
footer {
    background-color: #3498db; /* Vibrant blue background */
    color: white; /* White text for better contrast */
    padding: 30px 0;
}

footer .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

footer h4 {
    color: white;
    margin-bottom: 15px;
    font-weight: bold;
}

footer .social-icons a {
    color: white;
    font-size: 24px;
    margin-right: 15px;
    transition: color 0.3s ease;
}

footer .social-icons a:hover {
    color: #f1f1f1;
    text-decoration: none;
}

footer ul.list-unstyled {
    padding: 0;
}

footer ul.list-unstyled li {
    margin-bottom: 10px;
}

footer ul.list-unstyled a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

footer ul.list-unstyled a:hover {
    color: #f1f1f1;
    text-decoration: underline;
}

footer .text-center {
    margin-top: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 15px;
}