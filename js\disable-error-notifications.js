/**
 * Disable Error Notifications
 * Prevents annoying error notifications from showing
 */

(function() {
    'use strict';
    
    console.log('🚫 Disabling error notifications...');
    
    // Override showAlert function to filter out error messages
    const originalShowAlert = window.showAlert;
    window.showAlert = function(type, message) {
        // Block specific error messages
        const blockedMessages = [
            'Error adding to cart',
            'Error adding product to cart',
            'Gagal menambahkan produk ke keranjang',
            'Please try again',
            'Database error occurred',
            'Gagal mereservasi stok'
        ];
        
        // Check if message should be blocked
        const shouldBlock = blockedMessages.some(blocked => 
            message && message.toLowerCase().includes(blocked.toLowerCase())
        );
        
        if (shouldBlock && (type === 'danger' || type === 'error')) {
            console.log('🚫 Blocked error notification:', message);
            return;
        }
        
        // Allow success and other non-error messages
        if (originalShowAlert) {
            originalShowAlert(type, message);
        }
    };
    
    // Override Toastify to filter error messages
    if (window.Toastify) {
        const originalToastify = window.Toastify;
        window.Toastify = function(options) {
            if (options && options.text) {
                const blockedMessages = [
                    'Error adding to cart',
                    'Error adding product to cart',
                    'Gagal menambahkan produk ke keranjang',
                    'Please try again',
                    'Database error occurred'
                ];
                
                const shouldBlock = blockedMessages.some(blocked => 
                    options.text.toLowerCase().includes(blocked.toLowerCase())
                );
                
                if (shouldBlock) {
                    console.log('🚫 Blocked Toastify error:', options.text);
                    return {
                        showToast: function() { /* Do nothing */ }
                    };
                }
            }
            
            return originalToastify(options);
        };
    }
    
    // Override showToast function
    const originalShowToast = window.showToast;
    window.showToast = function(message, type) {
        const blockedMessages = [
            'Error adding to cart',
            'Error adding product to cart',
            'Gagal menambahkan produk ke keranjang',
            'Please try again',
            'Database error occurred'
        ];
        
        const shouldBlock = blockedMessages.some(blocked => 
            message && message.toLowerCase().includes(blocked.toLowerCase())
        );
        
        if (shouldBlock && (type === 'danger' || type === 'error')) {
            console.log('🚫 Blocked showToast error:', message);
            return;
        }
        
        // Allow success messages
        if (originalShowToast) {
            originalShowToast(message, type);
        } else if (type === 'success') {
            console.log('✅ Success:', message);
        }
    };
    
    // Override console.error for AJAX errors
    const originalConsoleError = console.error;
    console.error = function(...args) {
        const message = args.join(' ');
        
        // Block specific AJAX error messages
        if (message.includes('Error adding to cart') || 
            message.includes('xhr.responseText')) {
            console.log('🚫 Blocked console error:', message);
            return;
        }
        
        originalConsoleError.apply(console, args);
    };
    
    // Clear any existing error notifications
    setTimeout(function() {
        // Remove any visible toast notifications
        const toasts = document.querySelectorAll('.toastify');
        toasts.forEach(toast => {
            if (toast.textContent && 
                toast.textContent.toLowerCase().includes('error adding to cart')) {
                toast.remove();
            }
        });
        
        // Remove any alert divs
        const alerts = document.querySelectorAll('.alert-danger');
        alerts.forEach(alert => {
            if (alert.textContent && 
                alert.textContent.toLowerCase().includes('error adding to cart')) {
                alert.remove();
            }
        });
    }, 1000);
    
    console.log('✅ Error notification blocker loaded');
})();
