<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

$user_id = $_SESSION['user_id'];
$action = $_POST['action'] ?? $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_addresses':
            // Get all addresses for the user
            $stmt = $conn->prepare("
                SELECT * FROM user_addresses 
                WHERE user_id = ? 
                ORDER BY is_default DESC, created_at DESC
            ");
            $stmt->execute([$user_id]);
            $addresses = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'addresses' => $addresses
            ]);
            break;
            
        case 'add_address':
            // Validate required fields
            $required_fields = ['label', 'recipient_name', 'phone', 'address', 'city', 'province', 'postal_code'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    echo json_encode(['success' => false, 'message' => "Field $field is required"]);
                    exit;
                }
            }
            
            // Check if this should be the default address
            $is_default = isset($_POST['is_default']) ? 1 : 0;
            
            // If setting as default, remove default from other addresses
            if ($is_default) {
                $stmt = $conn->prepare("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?");
                $stmt->execute([$user_id]);
            }
            
            // Insert new address
            $stmt = $conn->prepare("
                INSERT INTO user_addresses (
                    user_id, label, recipient_name, phone, address, 
                    city, province, postal_code, is_default, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $result = $stmt->execute([
                $user_id,
                $_POST['label'],
                $_POST['recipient_name'],
                $_POST['phone'],
                $_POST['address'],
                $_POST['city'],
                $_POST['province'],
                $_POST['postal_code'],
                $is_default
            ]);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Address added successfully',
                    'address_id' => $conn->lastInsertId()
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to add address']);
            }
            break;
            
        case 'update_address':
            $address_id = $_POST['address_id'] ?? '';
            if (empty($address_id)) {
                echo json_encode(['success' => false, 'message' => 'Address ID is required']);
                exit;
            }
            
            // Verify address belongs to user
            $stmt = $conn->prepare("SELECT address_id FROM user_addresses WHERE address_id = ? AND user_id = ?");
            $stmt->execute([$address_id, $user_id]);
            if (!$stmt->fetch()) {
                echo json_encode(['success' => false, 'message' => 'Address not found']);
                exit;
            }
            
            // Validate required fields
            $required_fields = ['label', 'recipient_name', 'phone', 'address', 'city', 'province', 'postal_code'];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    echo json_encode(['success' => false, 'message' => "Field $field is required"]);
                    exit;
                }
            }
            
            // Check if this should be the default address
            $is_default = isset($_POST['is_default']) ? 1 : 0;
            
            // If setting as default, remove default from other addresses
            if ($is_default) {
                $stmt = $conn->prepare("UPDATE user_addresses SET is_default = 0 WHERE user_id = ? AND address_id != ?");
                $stmt->execute([$user_id, $address_id]);
            }
            
            // Update address
            $stmt = $conn->prepare("
                UPDATE user_addresses SET
                    label = ?, recipient_name = ?, phone = ?, address = ?,
                    city = ?, province = ?, postal_code = ?, is_default = ?, updated_at = NOW()
                WHERE address_id = ? AND user_id = ?
            ");
            
            $result = $stmt->execute([
                $_POST['label'],
                $_POST['recipient_name'],
                $_POST['phone'],
                $_POST['address'],
                $_POST['city'],
                $_POST['province'],
                $_POST['postal_code'],
                $is_default,
                $address_id,
                $user_id
            ]);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Address updated successfully'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update address']);
            }
            break;
            
        case 'delete_address':
            $address_id = $_POST['address_id'] ?? '';
            if (empty($address_id)) {
                echo json_encode(['success' => false, 'message' => 'Address ID is required']);
                exit;
            }
            
            // Verify address belongs to user
            $stmt = $conn->prepare("SELECT address_id, is_default FROM user_addresses WHERE address_id = ? AND user_id = ?");
            $stmt->execute([$address_id, $user_id]);
            $address = $stmt->fetch();
            
            if (!$address) {
                echo json_encode(['success' => false, 'message' => 'Address not found']);
                exit;
            }
            
            // Delete address
            $stmt = $conn->prepare("DELETE FROM user_addresses WHERE address_id = ? AND user_id = ?");
            $result = $stmt->execute([$address_id, $user_id]);
            
            if ($result) {
                // If deleted address was default, set another address as default
                if ($address['is_default']) {
                    $stmt = $conn->prepare("
                        UPDATE user_addresses 
                        SET is_default = 1 
                        WHERE user_id = ? 
                        ORDER BY created_at DESC 
                        LIMIT 1
                    ");
                    $stmt->execute([$user_id]);
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Address deleted successfully'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to delete address']);
            }
            break;
            
        case 'set_default':
            $address_id = $_POST['address_id'] ?? '';
            if (empty($address_id)) {
                echo json_encode(['success' => false, 'message' => 'Address ID is required']);
                exit;
            }
            
            // Verify address belongs to user
            $stmt = $conn->prepare("SELECT address_id FROM user_addresses WHERE address_id = ? AND user_id = ?");
            $stmt->execute([$address_id, $user_id]);
            if (!$stmt->fetch()) {
                echo json_encode(['success' => false, 'message' => 'Address not found']);
                exit;
            }
            
            // Remove default from all addresses
            $stmt = $conn->prepare("UPDATE user_addresses SET is_default = 0 WHERE user_id = ?");
            $stmt->execute([$user_id]);
            
            // Set new default
            $stmt = $conn->prepare("UPDATE user_addresses SET is_default = 1 WHERE address_id = ? AND user_id = ?");
            $result = $stmt->execute([$address_id, $user_id]);
            
            if ($result) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Default address updated successfully'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update default address']);
            }
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Address management error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while managing addresses']);
}
?>
