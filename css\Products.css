/* General reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #f8f9fa;
  line-height: 1.6;
}

/* Base Styles */
:root {
  --primary-color: #4361ee;
  --secondary-color: #f9f9f9;
  --dark-text: #333333;
  --light-bg: #ffffff;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Styles */
header {
  width: 100%;
}

/* Logo styling */
.logo-text {
  font-size: 24px;
  font-weight: 700;
  text-decoration: none;
  letter-spacing: 0.5px;
}

.text-blue-600 {
  color: #2563eb;
}

.text-yellow-500 {
  color: #f59e0b;
}

/* Search bar styles */
.search-bar {
  background-color: white;
  height: 40px;
  border-radius: 9999px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  font-size: 14px;
}

.search-bar:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Navigation styles */
.nav-link {
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  padding: 8px 12px;
  position: relative;
  transition: color 0.3s;
}

.nav-link.active {
  color: #2563eb;
  font-weight: 600;
}

.nav-link.active:after,
.nav-link:hover:after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2563eb;
  transform: scaleX(1);
}

.nav-link:after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2563eb;
  transform: scaleX(0);
  transition: transform 0.3s ease-in-out;
}

.nav-link:hover:after {
  transform: scaleX(1);
}

/* Cart icon styles */
.cart-icon-container {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Auth links styles */
.auth-links {
  display: flex;
  align-items: center;
}

.auth-links a {
  margin-left: 20px;
  text-decoration: none;
  color: var(--dark-text);
}

/* Utility classes */
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-x-6 > * + * {
  margin-left: 1.5rem;
}

.space-x-8 > * + * {
  margin-left: 2rem;
}

.hidden {
  display: none;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-400 {
  color: #9ca3af;
}

.hover\:text-blue-600:hover {
  color: #2563eb;
}

.bg-white {
  background-color: white;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow-inner {
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.pl-10 {
  padding-left: 2.5rem;
}

.pr-4 {
  padding-right: 1rem;
}

.text-xl {
  font-size: 1.25rem;
}

.rounded-full {
  border-radius: 9999px;
}

.border {
  border-width: 1px;
}

.border-gray-300 {
  border-color: #d1d5db;
}

.focus\:outline-none:focus {
  outline: none;
}

.w-64 {
  width: 16rem;
}

.transform {
  transform: translateY(-50%);
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.left-4 {
  left: 1rem;
}

.top-1\/2 {
  top: 50%;
}

.transform {
  transform: translateY(-50%);
}

/* Media Queries */
@media (min-width: 768px) {
  .md\:flex {
      display: flex;
  }
  
  .md\:block {
      display: block;
  }
  
  .md\:hidden {
      display: none;
  }
  
  .md\:justify-start {
      justify-content: flex-start;
  }
}

@media (max-width: 767px) {
  .nav-link {
      font-size: 14px;
      padding: 6px 0;
  }
  
  .space-x-8 > * + * {
      margin-left: 1rem;
  }
}

@media (max-width: 640px) {
  .container {
      padding-left: 1rem;
      padding-right: 1rem;
  }
  
  .logo-text {
      font-size: 20px;
  }
}

/* Product Grid Styles */
.featured-section {
  padding: 40px 0;
  margin-bottom: 40px;
}

.row.g-3 {
  margin-right: -8px;
  margin-left: -8px;
}

.row.g-3 > [class*="col-"] {
  padding-right: 8px;
  padding-left: 8px;
}

/* Small Product Card Styles */
.product-card.small-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #f0f0f0;
}

.product-card.small-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-card.small-card .product-img {
  height: 120px;
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.product-card.small-card .product-img img {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}

.product-card.small-card .product-info {
  padding: 0 5px;
}

.product-card.small-card .product-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 6px;
  color: var(--dark-text);
  line-height: 1.3;
  height: 36px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-card.small-card .product-price {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 15px;
  margin-bottom: 8px;
}

.product-card.small-card .product-rating {
  color: #FFC107;
  font-size: 12px;
  margin-bottom: 10px;
}

.product-card.small-card .product-rating .fa-star {
  font-size: 12px;
}

.product-card.small-card .product-rating span {
  font-size: 11px;
  color: #777;
}

.product-card.small-card .btn {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.2s;
}

.product-card.small-card .btn-primary {
  background-color: var(--primary-color);
  color: white;
  margin-bottom: 6px;
}

.product-card.small-card .btn-outline-secondary {
  border: 1px solid #ddd;
  color: #555;
}

.product-card.small-card .btn:hover {
  transform: translateY(-2px);
}

/* Modal Styles */
.product-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  padding: 20px;
  border-radius: 10px;
  max-width: 800px;
  width: 90%;
}

.close-modal {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.quantity-selector {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.quantity-selector .qty-btn {
  width: 30px;
  height: 30px;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  cursor: pointer;
}

.quantity-selector .qty-input {
  width: 50px;
  height: 30px;
  text-align: center;
  margin: 0 5px;
  border: 1px solid #ddd;
}

/* Responsive Styles */
@media (max-width: 767px) {
  .logo-text {
    font-size: 20px;
  }
  
  .search-bar {
    width: 100%;
    margin: 10px 0;
  }
  
  .product-card.small-card {
    padding: 10px;
  }
  
  .product-card.small-card .product-img {
    height: 100px;
  }
  
  .product-card.small-card .product-title {
    font-size: 13px;
    height: 32px;
  }
  
  .product-card.small-card .product-price {
    font-size: 14px;
  }
  
  .product-card.small-card .btn {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .modal-content {
    margin: 10% auto;
    width: 95%;
  }
}

@media (max-width: 575px) {
  .col-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
  
  .product-card.small-card .product-img {
    height: 90px;
  }
  
  .product-card.small-card .product-title {
    font-size: 12px;
    height: 30px;
  }
}
  
  /* Logo Keranjang */
  .cart-icon-container {
    position: relative;
    display: inline-flex;
    cursor: pointer;
    margin-right: 15px;
  }
  
  .fa-shopping-cart {
    font-size: 20px;
    color: #333;
  }
  
  .cart-counter {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: #ff3366;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
  }

  /* Modal Styles */
.product-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 25px;
    border-radius: 8px;
    width: 90%;
    max-width: 900px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    animation: modalFadeIn 0.3s;
  }
  
  @keyframes modalFadeIn {
    from {opacity: 0; transform: translateY(-20px);}
    to {opacity: 1; transform: translateY(0);}
  }
  
  .close-modal {
    color: #888;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s;
  }
  
  .close-modal:hover {
    color: #000;
  }
  
  .modal-product-img img {
    width: 100%;
    border-radius: 6px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  }
  
  .modal-product-title {
    font-size: 24px;
    margin-bottom: 10px;
    color: #333;
  }
  
  .modal-product-rating {
    margin-bottom: 12px;
  }
  
  .modal-product-rating .fas {
    color: #ffc107;
  }
  
  .modal-product-price {
    font-size: 24px;
    font-weight: bold;
    color: #e91e63;
    margin-bottom: 15px;
  }
  
  .modal-product-description {
    margin-bottom: 20px;
  }
  
  .modal-product-description ul {
    padding-left: 20px;
    margin-top: 10px;
  }
  
  .modal-product-description li {
    margin-bottom: 5px;
  }
  
  .modal-product-nutrition {
    margin-bottom: 20px;
  }
  
  .modal-product-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
  }
  
  .quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .qty-btn {
    background: #f5f5f5;
    border: none;
    padding: 8px 15px;
    cursor: pointer;
    font-size: 16px;
  }
  
  .qty-input {
    width: 50px;
    text-align: center;
    border: none;
    padding: 8px 0;
  }
  
  .btn-add-modal {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    flex-grow: 1;
    font-weight: 500;
    transition: background-color 0.2s;
  }
  
  .btn-add-modal:hover {
    background-color: #3d8b40;
  }
  
  /* Make modal responsive */
  @media (max-width: 768px) {
    .modal-content {
      width: 95%;
      margin: 10% auto;
      padding: 15px;
    }
    
    .modal-product-actions {
      flex-direction: column;
      align-items: stretch;
    }
    
    .quantity-selector {
      margin-bottom: 10px;
    }
  }

 /* Footer Styles */
footer {
  background-color: #3498db; /* Vibrant blue background */
  color: white; /* White text for better contrast */
  padding: 30px 0;
}

footer .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

footer h4 {
  color: white;
  margin-bottom: 15px;
  font-weight: bold;
}

footer .social-icons a {
  color: white;
  font-size: 24px;
  margin-right: 15px;
  transition: color 0.3s ease;
}

footer .social-icons a:hover {
  color: #f1f1f1;
  text-decoration: none;
}

footer ul.list-unstyled {
  padding: 0;
}

footer ul.list-unstyled li {
  margin-bottom: 10px;
}

footer ul.list-unstyled a {
  color: white;
  text-decoration: none;
  transition: color 0.3s ease;
}

footer ul.list-unstyled a:hover {
  color: #f1f1f1;
  text-decoration: underline;
}

footer .text-center {
  margin-top: 20px;
  border-top: 1px solid rgba(255,255,255,0.1);
  padding-top: 15px;
}