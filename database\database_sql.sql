CREATE DATABASE db_tewuneed;
USE db_tewuneed;

-- Table for user accounts
CREATE TABLE users (
    user_id      INT AUTO_INCREMENT PRIMARY KEY,
    username     VARCHAR(50) NOT NULL UNIQUE,
    PASSWORD     VARCHAR(255) NOT NULL,
    email        VARCHAR(100) NOT NULL UNIQUE,
    full_name    VARCHAR(100) NOT NULL,
    phone_number VARCHAR(20),
    address      TEXT,
    role         ENUM('admin', 'customer') DEFAULT 'customer',
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for admin accounts (extends users table)
CREATE TABLE admin_accounts (
    admin_id     INT PRIMARY KEY,
    department   VARCHAR(100),
    POSITION     VARCHAR(100),
    access_level ENUM('low', 'medium', 'high', 'super') DEFAULT 'low',
    last_login   TIMESTAMP,
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Table for admin permissions
CREATE TABLE admin_permissions (
    permission_id   INT AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    description     TEXT,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table for role-permission relationships
CREATE TABLE admin_role_permissions (
    id            INT AUTO_INCREMENT PRIMARY KEY,
    admin_id      INT NOT NULL,
    permission_id INT NOT NULL,
    granted_by    INT,
    granted_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admin_accounts(admin_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES admin_permissions(permission_id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES admin_accounts(admin_id),
    UNIQUE KEY (admin_id, permission_id)
);

-- Table for admin dashboard widgets/preferences
CREATE TABLE admin_dashboard (
    widget_id    INT AUTO_INCREMENT PRIMARY KEY,
    admin_id     INT NOT NULL,
    widget_name  VARCHAR(50) NOT NULL,
    widget_config JSON,
    POSITION     INT,
    is_visible   BOOLEAN DEFAULT TRUE,
    UNIQUE KEY (admin_id, widget_name),
    FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Table for product reviews (managed by admin)
CREATE TABLE product_reviews (
    review_id    INT AUTO_INCREMENT PRIMARY KEY,
    product_id   INT NOT NULL,
    user_id      INT NOT NULL,
    rating       TINYINT NOT NULL CHECK (rating BETWEEN 1 AND 5),
    review_text  TEXT,
    is_approved  BOOLEAN DEFAULT FALSE,
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Table for shipping methods (managed by admin)
CREATE TABLE shipping_methods (
    method_id          INT AUTO_INCREMENT PRIMARY KEY,
    NAME               VARCHAR(50) NOT NULL,
    description        TEXT,
    cost               DECIMAL(10,2) NOT NULL,
    is_active          BOOLEAN DEFAULT TRUE,
    estimated_delivery VARCHAR(50),
    created_at         TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at         TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for order-shipping relationships
CREATE TABLE order_shipping (
    order_shipping_id INT AUTO_INCREMENT PRIMARY KEY,
    order_id          INT NOT NULL UNIQUE,
    method_id         INT NOT NULL,
    tracking_number   VARCHAR(100),
    shipping_date     DATETIME,
    estimated_arrival DATETIME,
    actual_arrival    DATETIME,
    shipping_cost     DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (method_id) REFERENCES shipping_methods(method_id)
);

-- Insert default admin permissions
INSERT INTO admin_permissions (permission_name, description) VALUES
    ('manage_users', 'Create, edit, and delete user accounts'),
    ('manage_products', 'Create, edit, and delete products'),
    ('manage_categories', 'Create, edit, and delete product categories'),
    ('manage_orders', 'View and update order status'),
    ('manage_payments', 'Manage and process payments'),
    ('view_reports', 'Access to sales and inventory reports'),
    ('manage_promotions', 'Create and manage product promotions'),
    ('system_settings', 'Edit system configuration'),
    ('manage_admins', 'Add and remove admin privileges'),
    ('manage_reviews', 'Approve and manage product reviews'),
    ('manage_shipping', 'Manage shipping methods and tracking');

-- Insert initial admin user (password: admin123 - harus di-hash di aplikasi nyata)
INSERT INTO users (username, PASSWORD, email, full_name, role) 
VALUES ('admin', 'admin123', '<EMAIL>', 'Admin Tewuneed', 'admin');

-- Create trigger to automatically create admin account when user with role 'admin' is created
DELIMITER //
CREATE TRIGGER after_admin_user_insert AFTER INSERT ON users
FOR EACH ROW
BEGIN
    IF NEW.role = 'admin' THEN
        INSERT INTO admin_accounts (admin_id, access_level) 
        VALUES (NEW.user_id, 'low');
    END IF;
END//
DELIMITER ;

-- Create trigger to automatically create admin account when user role is updated to 'admin'
DELIMITER //
CREATE TRIGGER after_user_role_update AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    IF NEW.role = 'admin' AND (OLD.role != 'admin' OR OLD.role IS NULL) THEN
        -- Only create if not exists
        IF NOT EXISTS (SELECT 1 FROM admin_accounts WHERE admin_id = NEW.user_id) THEN
            INSERT INTO admin_accounts (admin_id, access_level) 
            VALUES (NEW.user_id, 'low');
        END IF;
    END IF;
END//
DELIMITER ;

-- Trigger for order status changes
DELIMITER //
CREATE TRIGGER after_order_status_update_1
AFTER UPDATE ON orders
FOR EACH ROW
BEGIN
    IF NEW.order_status != OLD.order_status THEN
        INSERT INTO order_status_history (order_id, admin_id, old_status, new_status)
        VALUES (NEW.order_id, NULL, OLD.order_status, NEW.order_status);
    END IF;
END//
DELIMITER ;

-- Trigger for inventory changes
DELIMITER //
CREATE TRIGGER after_product_stock_update_1
AFTER UPDATE ON products
FOR EACH ROW
BEGIN
    IF NEW.stock != OLD.stock THEN
        INSERT INTO inventory_logs (product_id, admin_id, old_stock, new_stock, adjustment, notes)
        VALUES (NEW.product_id, NULL, OLD.stock, NEW.stock, NEW.stock - OLD.stock, 'Automatic system update');
    END IF;
END//
DELIMITER ;

-- Insert system settings
INSERT INTO system_settings (setting_key, setting_value, description, is_public) VALUES 
    ('store_name', 'Tewuneed', 'Nama toko/toserba', TRUE),
    ('store_email', '<EMAIL>', 'Email kontak toko', TRUE),
    ('maintenance_mode', '0', 'Mode maintenance (1=aktif, 0=nonaktif)', FALSE);

-- Insert shipping methods
INSERT INTO shipping_methods (NAME, description, cost, estimated_delivery) VALUES 
    ('Regular', 'Pengiriman reguler 3-5 hari kerja', 15000, '3-5 hari kerja'),
    ('Express', 'Pengiriman cepat 1-2 hari kerja', 30000, '1-2 hari kerja'),
    ('Same Day', 'Pengiriman hari yang sama', 50000, 'Hari yang sama');

CREATE TABLE IF NOT EXISTS `cart` (
    `cart_id`    INT(11) NOT NULL AUTO_INCREMENT,
    `user_id`    INT(11) NULL,
    `session_id` VARCHAR(255) NULL,
    `product_id` INT(11) NOT NULL,
    `quantity`   INT(11) NOT NULL DEFAULT 1,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`cart_id`),
    KEY `user_id` (`user_id`),
    KEY `session_id` (`session_id`),
    KEY `product_id` (`product_id`)
);

-- Table for activity logs
CREATE TABLE IF NOT EXISTS `activity_logs` (
    `id`          INT(11) NOT NULL AUTO_INCREMENT,
    `user_id`     INT(11) NOT NULL,
    `action`      VARCHAR(255) NOT NULL,
    `details`     TEXT,
    `ip_address`  VARCHAR(45) NOT NULL,
    `created_at`  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `activity_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

-- Table for product categories
CREATE TABLE categories (
    category_id  INT AUTO_INCREMENT PRIMARY KEY,
    NAME         VARCHAR(50) NOT NULL,
    slug         VARCHAR(50) NOT NULL UNIQUE,
    description  TEXT,
    image        VARCHAR(255),
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table for products
CREATE TABLE products (
    product_id     INT AUTO_INCREMENT PRIMARY KEY,
    category_id    INT NOT NULL,
    NAME           VARCHAR(100) NOT NULL,
    slug           VARCHAR(100) NOT NULL UNIQUE,
    description    TEXT,
    price          DECIMAL(10,2) NOT NULL,
    cost_price     DECIMAL(10,2) NOT NULL COMMENT 'Modal/harga beli produk',
    stock          INT NOT NULL DEFAULT 0,
    reserved_stock INT NOT NULL DEFAULT 0 COMMENT 'Stock yang direservasi untuk cart',
    image          VARCHAR(255),
    weight         DECIMAL(10,2) COMMENT 'Berat produk dalam gram',
    is_active      BOOLEAN DEFAULT TRUE,
    created_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(category_id)
);

-- Table for product images (additional images)
CREATE TABLE product_images (
    image_id     INT AUTO_INCREMENT PRIMARY KEY,
    product_id   INT NOT NULL,
    image_url    VARCHAR(255) NOT NULL,
    is_primary   BOOLEAN DEFAULT FALSE,
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE
);

-- Table for product features
CREATE TABLE product_features (
    id            INT AUTO_INCREMENT PRIMARY KEY,
    product_id    INT NOT NULL,
    feature_text  TEXT NOT NULL,
    feature_order INT NOT NULL DEFAULT 0,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE
);

-- Table for product specifications
CREATE TABLE product_specifications (
    id         INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    spec_name  VARCHAR(100) NOT NULL,
    spec_value VARCHAR(255) NOT NULL,
    spec_order INT NOT NULL DEFAULT 0,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE
);

-- Table for product nutrition information
CREATE TABLE product_nutrition (
    id             INT AUTO_INCREMENT PRIMARY KEY,
    product_id     INT NOT NULL,
    nutrient_name  VARCHAR(100) NOT NULL,
    nutrient_value VARCHAR(255) NOT NULL,
    nutrient_order INT NOT NULL DEFAULT 0,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE
);

-- Table for shopping carts
CREATE TABLE carts (
    cart_id     INT AUTO_INCREMENT PRIMARY KEY,
    user_id     INT NOT NULL,
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Table for cart items
CREATE TABLE cart_items (
    cart_item_id INT AUTO_INCREMENT PRIMARY KEY,
    cart_id      INT NOT NULL,
    product_id   INT NOT NULL,
    quantity     INT NOT NULL DEFAULT 1,
    added_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cart_id) REFERENCES carts(cart_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);

-- Table for orders
CREATE TABLE orders (
    order_id          INT AUTO_INCREMENT PRIMARY KEY,
    user_id           INT NOT NULL,
    order_date        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    total_amount      DECIMAL(10,2) NOT NULL,
    shipping_address  TEXT NOT NULL,
    billing_address   TEXT,
    payment_method    ENUM('credit_card', 'bank_transfer', 'cash_on_delivery', 'e_wallet') NOT NULL,
    payment_status    ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    order_status      ENUM('dibuat', 'diproses', 'dikirim', 'terkirim', 'dibatalkan') DEFAULT 'dibuat',
    tracking_number   VARCHAR(100),
    notes             TEXT,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Remove this ALTER statement as the orders table already has order_status column defined correctly

-- Table for order items
CREATE TABLE order_items (
    order_item_id INT AUTO_INCREMENT PRIMARY KEY,
    order_id      INT NOT NULL,
    product_id    INT NOT NULL,
    quantity      INT NOT NULL,
    unit_price    DECIMAL(10,2) NOT NULL,
    total_price   DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);

-- Table for payments
CREATE TABLE payments (
    payment_id     INT AUTO_INCREMENT PRIMARY KEY,
    order_id       INT NOT NULL,
    amount        DECIMAL(10,2) NOT NULL,
    payment_date   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    payment_method ENUM('credit_card', 'bank_transfer', 'cash_on_delivery', 'e_wallet') NOT NULL,
    transaction_id VARCHAR(100),
    STATUS         ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    notes         TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);

-- Table for product search index (for full-text search)
CREATE TABLE product_search (
    product_id    INT PRIMARY KEY,
    NAME          TEXT NOT NULL,
    description   TEXT,
    category_name TEXT,
    FULLTEXT(NAME, description, category_name),
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE
);

-- Create stock reservations table for cart system
CREATE TABLE IF NOT EXISTS `stock_reservations` (
  `reservation_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `reserved_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL 30 MINUTE),
  `status` enum('active','expired','confirmed','cancelled') DEFAULT 'active',
  PRIMARY KEY (`reservation_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial categories
INSERT INTO categories (NAME, slug, description) VALUES
    ('Cosmetics',    'cosmetics',      'Berbagai macam produk kosmetik dan perawatan kulit'),
    ('Milk Products','milk-products',  'Produk susu dan olahannya untuk kebutuhan sehari-hari'),
    ('Medicine',     'medicine',       'Obat-obatan dan produk kesehatan'),
    ('Sports',       'sports',         'Perlengkapan olahraga'),
    ('Vegetables',   'vegetables',     'Sayuran segar dan organik'),
    ('Electronics',  'electronics',    'Peralatan elektronik untuk kebutuhan sehari-hari'),
    ('Fashion',      'fashion',        'Pakaian dan aksesoris fashion'),
    ('Home & Garden','home-garden',    'Perlengkapan rumah dan taman'),
    ('Books',        'books',          'Buku dan alat tulis'),
    ('Toys',         'toys',           'Mainan dan permainan anak');

-- Trigger to update product search index	
DELIMITER //
CREATE TRIGGER after_product_insert AFTER INSERT ON products
FOR EACH ROW
BEGIN
    INSERT INTO product_search (product_id, NAME, description, category_name)
    SELECT NEW.product_id, NEW.NAME, NEW.description, c.name
    FROM categories c WHERE c.category_id = NEW.category_id;
END//
DELIMITER ;

-- Trigger to update product search index when product is updated
DELIMITER //
CREATE TRIGGER after_product_update AFTER UPDATE ON products
FOR EACH ROW
BEGIN
    UPDATE product_search ps
    JOIN categories c ON c.category_id = NEW.category_id
    SET ps.NAME = NEW.NAME, 
        ps.description = NEW.description,
        ps.category_name = c.name
    WHERE ps.product_id = NEW.product_id;
END//
DELIMITER ;

-- Trigger to update product search index when category name is updated
DELIMITER //
CREATE TRIGGER after_category_update AFTER UPDATE ON categories
FOR EACH ROW
BEGIN
    UPDATE product_search ps
    JOIN products p ON p.product_id = ps.product_id
    SET ps.category_name = NEW.name
    WHERE p.category_id = NEW.category_id;
END//
DELIMITER ;

-- Function to get available stock (actual stock minus reservations)
DELIMITER //
CREATE FUNCTION GetAvailableStock(p_product_id INT)
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE actual_stock INT DEFAULT 0;
    DECLARE reserved_stock INT DEFAULT 0;
    DECLARE available_stock INT DEFAULT 0;

    -- Get actual stock
    SELECT stock INTO actual_stock
    FROM products
    WHERE product_id = p_product_id;

    -- Get total active reservations
    SELECT COALESCE(SUM(quantity), 0) INTO reserved_stock
    FROM stock_reservations
    WHERE product_id = p_product_id
    AND status = 'active'
    AND expires_at > NOW();

    -- Calculate available stock
    SET available_stock = actual_stock - reserved_stock;

    -- Ensure it's not negative
    IF available_stock < 0 THEN
        SET available_stock = 0;
    END IF;

    RETURN available_stock;
END//
DELIMITER ;

-- Create procedure to reserve stock
DELIMITER //
CREATE PROCEDURE ReserveStock(
    IN p_product_id INT,
    IN p_user_id INT,
    IN p_session_id VARCHAR(255),
    IN p_quantity INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE available_stock INT DEFAULT 0;
    DECLARE existing_reservation INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Database error occurred';
    END;

    START TRANSACTION;

    -- Clean up expired reservations first
    DELETE FROM stock_reservations
    WHERE expires_at < NOW() OR status = 'expired';

    -- Get available stock
    SET available_stock = GetAvailableStock(p_product_id);

    -- Check if there's enough stock
    IF available_stock < p_quantity THEN
        SET p_success = FALSE;
        SET p_message = CONCAT('Insufficient stock. Available: ', available_stock);
        ROLLBACK;
    ELSE
        -- Check if user already has a reservation for this product
        SELECT COALESCE(SUM(quantity), 0) INTO existing_reservation
        FROM stock_reservations
        WHERE product_id = p_product_id
        AND (user_id = p_user_id OR session_id = p_session_id)
        AND status = 'active'
        AND expires_at > NOW();

        -- Create or update reservation
        IF existing_reservation > 0 THEN
            -- Replace existing reservation with new quantity (not add to it)
            UPDATE stock_reservations
            SET quantity = p_quantity,
                expires_at = CURRENT_TIMESTAMP + INTERVAL 30 MINUTE
            WHERE product_id = p_product_id
            AND (user_id = p_user_id OR session_id = p_session_id)
            AND status = 'active'
            AND expires_at > NOW()
            LIMIT 1;
        ELSE
            -- Create new reservation
            INSERT INTO stock_reservations (product_id, user_id, session_id, quantity, expires_at)
            VALUES (p_product_id, p_user_id, p_session_id, p_quantity, CURRENT_TIMESTAMP + INTERVAL 30 MINUTE);
        END IF;

        -- Update reserved_stock in products table
        UPDATE products
        SET reserved_stock = (
            SELECT COALESCE(SUM(quantity), 0)
            FROM stock_reservations
            WHERE product_id = p_product_id
            AND status = 'active'
            AND expires_at > NOW()
        )
        WHERE product_id = p_product_id;

        SET p_success = TRUE;
        SET p_message = 'Stock reserved successfully';
        COMMIT;
    END IF;
END//
DELIMITER ;

-- Table for admin activities/logs
CREATE TABLE admin_logs (
    log_id      INT AUTO_INCREMENT PRIMARY KEY,
    user_id     INT NOT NULL,
    activity    VARCHAR(255) NOT NULL,
    ip_address  VARCHAR(45) NOT NULL,
    user_agent  TEXT,
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- Create procedure to confirm reservation (when order is placed)
DELIMITER //
CREATE PROCEDURE ConfirmReservation(
    IN p_product_id INT,
    IN p_user_id INT,
    IN p_session_id VARCHAR(255),
    IN p_quantity INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE available_reservation INT DEFAULT 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Database error occurred';
    END;

    START TRANSACTION;

    -- Check if user has active reservation
    SELECT COALESCE(SUM(quantity), 0) INTO available_reservation
    FROM stock_reservations
    WHERE product_id = p_product_id
    AND (user_id = p_user_id OR session_id = p_session_id)
    AND status = 'active'
    AND expires_at > NOW();

    IF available_reservation >= p_quantity THEN
        -- Mark reservation as confirmed and reduce actual stock
        UPDATE stock_reservations
        SET status = 'confirmed'
        WHERE product_id = p_product_id
        AND (user_id = p_user_id OR session_id = p_session_id)
        AND status = 'active'
        AND expires_at > NOW();

        -- Reduce actual stock
        UPDATE products
        SET stock = stock - p_quantity,
            reserved_stock = (
                SELECT COALESCE(SUM(quantity), 0)
                FROM stock_reservations
                WHERE product_id = p_product_id
                AND status = 'active'
                AND expires_at > NOW()
            )
        WHERE product_id = p_product_id;

        SET p_success = TRUE;
        SET p_message = 'Reservation confirmed and stock updated';
        COMMIT;
    ELSE
        SET p_success = FALSE;
        SET p_message = CONCAT('Insufficient reservation. Available: ', available_reservation);
        ROLLBACK;
    END IF;
END//
DELIMITER ;

-- Create procedure to cancel reservation
DELIMITER //
CREATE PROCEDURE CancelReservation(
    IN p_product_id INT,
    IN p_user_id INT,
    IN p_session_id VARCHAR(255),
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Database error occurred';
    END;

    START TRANSACTION;

    -- Cancel user's reservations for this product
    UPDATE stock_reservations
    SET status = 'cancelled'
    WHERE product_id = p_product_id
    AND (user_id = p_user_id OR session_id = p_session_id)
    AND status = 'active';

    -- Update reserved_stock in products table
    UPDATE products
    SET reserved_stock = (
        SELECT COALESCE(SUM(quantity), 0)
        FROM stock_reservations
        WHERE product_id = p_product_id
        AND status = 'active'
        AND expires_at > NOW()
    )
    WHERE product_id = p_product_id;

    SET p_success = TRUE;
    SET p_message = 'Reservation cancelled successfully';
    COMMIT;
END//
DELIMITER ;

-- Table for system settings (configurable by admin)
CREATE TABLE system_settings (
    setting_id      INT AUTO_INCREMENT PRIMARY KEY,
    setting_key     VARCHAR(50) NOT NULL UNIQUE,
    setting_value   TEXT NOT NULL,
    description     TEXT,
    is_public       BOOLEAN DEFAULT FALSE,
    updated_by      INT,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(user_id)
);

-- Table for product discounts/promotions
CREATE TABLE promotions (
    promotion_id    INT AUTO_INCREMENT PRIMARY KEY,
    NAME            VARCHAR(100) NOT NULL,
    description     TEXT,
    discount_type   ENUM('percentage', 'fixed_amount') NOT NULL,
    discount_value  DECIMAL(10,2) NOT NULL,
    start_date      DATETIME NOT NULL,
    end_date        DATETIME NOT NULL,
    is_active       BOOLEAN DEFAULT TRUE,
    created_by      INT NOT NULL,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(user_id)
);

-- Table for product-promotion relationships
CREATE TABLE product_promotions (
    product_promotion_id INT AUTO_INCREMENT PRIMARY KEY,
    product_id           INT NOT NULL,
    promotion_id         INT NOT NULL,
    created_at           TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    FOREIGN KEY (promotion_id) REFERENCES promotions(promotion_id) ON DELETE CASCADE,
    UNIQUE KEY (product_id, promotion_id)
);

-- Table for inventory management (stock adjustments)
CREATE TABLE inventory_logs (
    log_id      INT AUTO_INCREMENT PRIMARY KEY,
    product_id  INT NOT NULL,
    admin_id    INT NULL COMMENT 'NULL for system updates',
    old_stock   INT NOT NULL,
    new_stock   INT NOT NULL,
    adjustment  INT NOT NULL COMMENT 'Positive for addition, negative for reduction',
    notes       TEXT,
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (admin_id) REFERENCES users(user_id)
);

-- Table for customer management (admin notes)
CREATE TABLE customer_notes (
    note_id         INT AUTO_INCREMENT PRIMARY KEY,
    customer_id     INT NOT NULL,
    admin_id        INT NOT NULL,
    note            TEXT NOT NULL,
    is_important    BOOLEAN DEFAULT FALSE,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(user_id)
);

-- Table for order status changes (admin actions)
CREATE TABLE order_status_history (
    history_id  INT AUTO_INCREMENT PRIMARY KEY,
    order_id    INT NOT NULL,
    admin_id    INT NULL COMMENT 'NULL for system updates',
    old_status  ENUM('dibuat', 'diproses', 'dikirim', 'terkirim', 'dibatalkan'),
    new_status  ENUM('dibuat', 'diproses', 'dikirim', 'terkirim', 'dibatalkan') NOT NULL,
    notes       TEXT,
    created_at  TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(user_id)
);

-- Table for admin dashboard widgets/preferences
CREATE TABLE admin_dashboard (
    widget_id       INT AUTO_INCREMENT PRIMARY KEY,
    admin_id        INT NOT NULL,
    widget_name     VARCHAR(50) NOT NULL,
    widget_config   JSON,
    POSITION        INT,
    is_visible      BOOLEAN DEFAULT TRUE,
    UNIQUE KEY (admin_id, widget_name),
    FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Duplicate product_reviews table removed

-- Duplicate shipping_methods table removed

-- Duplicate order_shipping table removed

-- Note: Duplicate triggers removed - using the ones defined earlier

-- Duplicate admin user insertion removed

-- Duplicate system settings and shipping methods insertions removed

USE db_tewuneed;

-- 1. Manajemen Inventori Lengkap dengan Tracking Stok
CREATE TABLE stock_mutations (
    mutation_id     INT AUTO_INCREMENT PRIMARY KEY,
    product_id      INT NOT NULL,
    quantity        INT NOT NULL COMMENT 'Positif untuk penambahan, negatif untuk pengurangan',
    mutation_type   ENUM('purchase', 'sale', 'adjustment', 'return', 'initial') NOT NULL,
    reference_id    INT COMMENT 'ID dari order/purchase yang terkait',
    reference_type  ENUM('order', 'purchase', 'manual', 'system') NOT NULL,
    notes           TEXT,
    created_by      INT COMMENT 'Admin/user yang melakukan',
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id)
);

-- Trigger untuk update stok otomatis ketika ada mutasi
DELIMITER //
CREATE TRIGGER after_stock_mutation_insert
AFTER INSERT ON stock_mutations
FOR EACH ROW
BEGIN
    UPDATE products 
    SET stock = stock + NEW.quantity 
    WHERE product_id = NEW.product_id;
END//
DELIMITER ;

-- 2. Fitur Wishlist untuk Pelanggan
CREATE TABLE wishlists (
    wishlist_id     INT AUTO_INCREMENT PRIMARY KEY,
    user_id         INT NOT NULL,
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY (user_id)
);

CREATE TABLE wishlist_items (
    wishlist_item_id    INT AUTO_INCREMENT PRIMARY KEY,
    wishlist_id         INT NOT NULL,
    product_id          INT NOT NULL,
    added_at            TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (wishlist_id) REFERENCES wishlists(wishlist_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    UNIQUE KEY (wishlist_id, product_id)
);

-- 3. Kemampuan Analitik yang Lebih Baik
CREATE TABLE product_views (
    view_id     INT AUTO_INCREMENT PRIMARY KEY,
    product_id  INT NOT NULL,
    user_id     INT,
    ip_address  VARCHAR(45) NOT NULL,
    viewed_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

CREATE TABLE sales_analytics (
    analytics_id        INT AUTO_INCREMENT PRIMARY KEY,
    DATE                DATE NOT NULL UNIQUE,
    total_sales         DECIMAL(12,2) NOT NULL DEFAULT 0,
    total_orders        INT NOT NULL DEFAULT 0,
    total_customers     INT NOT NULL DEFAULT 0,
    avg_order_value     DECIMAL(10,2) NOT NULL DEFAULT 0,
    products_sold       INT NOT NULL DEFAULT 0,
    created_at          TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. Riwayat Perubahan Status Order yang Lebih Rinci
ALTER TABLE orders ADD COLUMN cancelled_reason TEXT AFTER notes;

-- Note: order_status_history table already defined above

-- 5. Optimasi Performa dengan Index Tambahan
CREATE INDEX idx_products_category_active   ON products(category_id, is_active);
CREATE INDEX idx_products_price_stock       ON products(price, stock);
CREATE INDEX idx_orders_user_status         ON orders(user_id, order_status);
CREATE INDEX idx_orders_date_status         ON orders(order_date, order_status);
CREATE INDEX idx_order_items_product        ON order_items(product_id);
CREATE INDEX idx_cart_items_user_product    ON cart_items(cart_id, product_id);
CREATE INDEX idx_payments_status_date       ON payments(STATUS, payment_date);
CREATE INDEX idx_stock_mutations_product_date ON stock_mutations(product_id, created_at);

-- Contoh data wishlist
INSERT INTO wishlists (user_id) VALUES (1);
INSERT INTO wishlist_items (wishlist_id, product_id) VALUES (1, 1);

-- Contoh data stock mutation
INSERT INTO stock_mutations (product_id, quantity, mutation_type, reference_type, notes)
VALUES (1, 100, 'initial', 'system', 'Stok awal produk');

-- Contoh data product view
INSERT INTO product_views (product_id, user_id, ip_address) 
VALUES (1, 1, '***********');

-- Contoh data sales analytics
INSERT INTO sales_analytics (DATE, total_sales, total_orders, total_customers, avg_order_value, products_sold)
VALUES (CURDATE(), 500000, 10, 8, 50000, 25);

USE db_tewuneed;

-- 1. Tambahkan tabel untuk product ratings/reviews
CREATE TABLE product_ratings (
    rating_id     INT AUTO_INCREMENT PRIMARY KEY,
    product_id    INT NOT NULL,
    user_id       INT NOT NULL,
    rating        TINYINT NOT NULL CHECK (rating BETWEEN 1 AND 5),
    review_title  VARCHAR(100),
    review_text   TEXT,
    is_approved   BOOLEAN DEFAULT FALSE COMMENT 'Untuk moderasi oleh admin',
    created_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id)    REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY (product_id, user_id) COMMENT 'Satu user hanya bisa memberi satu rating per produk'
);

-- 2. Tambahkan kolom average rating dan total reviews ke tabel products
ALTER TABLE products ADD COLUMN average_rating DECIMAL(3,2) DEFAULT 0.00;
ALTER TABLE products ADD COLUMN total_ratings INT DEFAULT 0;

-- 3. Buat view untuk melihat rating produk
CREATE VIEW product_rating_summary AS
SELECT 
    p.product_id,
    p.NAME,
    p.average_rating,
    p.total_ratings,
    COUNT(pr.rating_id) AS total_reviews,
    SUM(CASE WHEN pr.rating = 5 THEN 1 ELSE 0 END) AS five_stars,
    SUM(CASE WHEN pr.rating = 4 THEN 1 ELSE 0 END) AS four_stars,
    SUM(CASE WHEN pr.rating = 3 THEN 1 ELSE 0 END) AS three_stars,
    SUM(CASE WHEN pr.rating = 2 THEN 1 ELSE 0 END) AS two_stars,
    SUM(CASE WHEN pr.rating = 1 THEN 1 ELSE 0 END) AS one_stars
FROM products p
LEFT JOIN product_ratings pr ON p.product_id = pr.product_id AND pr.is_approved = TRUE
GROUP BY p.product_id;

-- 4. Buat trigger untuk update average rating otomatis
DELIMITER //
CREATE TRIGGER after_rating_insert
AFTER INSERT ON product_ratings
FOR EACH ROW
BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    DECLARE total_count INT;
    -- Hitung hanya rating yang approved
    SELECT AVG(rating), COUNT(*) INTO avg_rating, total_count
    FROM product_ratings
    WHERE product_id = NEW.product_id AND is_approved = TRUE;
    UPDATE products
    SET average_rating = IFNULL(avg_rating, 0),
        total_ratings  = IFNULL(total_count, 0)
    WHERE product_id = NEW.product_id;
END//
DELIMITER ;

-- 5. Buat trigger untuk update rating ketika ada perubahan
DELIMITER //
CREATE TRIGGER after_rating_update
AFTER UPDATE ON product_ratings
FOR EACH ROW
BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    DECLARE total_count INT;
    IF NEW.rating <> OLD.rating OR NEW.is_approved <> OLD.is_approved THEN
        -- Hitung hanya rating yang approved
        SELECT AVG(rating), COUNT(*) INTO avg_rating, total_count
        FROM product_ratings
        WHERE product_id = NEW.product_id AND is_approved = TRUE;
        UPDATE products
        SET average_rating = IFNULL(avg_rating, 0),
            total_ratings  = IFNULL(total_count, 0)
        WHERE product_id = NEW.product_id;
    END IF;
END//
DELIMITER ;

-- 6. Buat trigger untuk update rating ketika dihapus
DELIMITER //
CREATE TRIGGER after_rating_delete
AFTER DELETE ON product_ratings
FOR EACH ROW
BEGIN
    DECLARE avg_rating DECIMAL(3,2);
    DECLARE total_count INT;
    -- Hitung hanya rating yang approved
    SELECT AVG(rating), COUNT(*) INTO avg_rating, total_count
    FROM product_ratings
    WHERE product_id = OLD.product_id AND is_approved = TRUE;
    UPDATE products
    SET average_rating = IFNULL(avg_rating, 0),
        total_ratings  = IFNULL(total_count, 0)
    WHERE product_id = OLD.product_id;
END//
DELIMITER ;

-- 7. Tambahkan index untuk performa
CREATE INDEX idx_product_ratings_product   ON product_ratings(product_id);
CREATE INDEX idx_product_ratings_user      ON product_ratings(user_id);
CREATE INDEX idx_product_ratings_approved  ON product_ratings(is_approved);

-- User memberi rating
INSERT INTO product_ratings (product_id, user_id, rating, review_title, review_text, is_approved)
VALUES (1, 1, 5, 'Produk bagus!', 'Sangat puas dengan produk ini', TRUE);

-- Admin melihat rating yang perlu di-approve
SELECT * FROM product_ratings WHERE is_approved = FALSE;

-- Approve rating
UPDATE product_ratings SET is_approved = TRUE WHERE rating_id = 1;

-- Melihat ringkasan rating produk
SELECT * FROM product_rating_summary WHERE product_id = 1;

-- SAMPLE DATA INSERTION
-- *********************************************

-- Insert initial categories
INSERT INTO categories (NAME, slug, description) VALUES 
    ('Cosmetics',      'cosmetics',      'Berbagai macam produk kosmetik dan perawatan kulit'),
    ('Milk Products',  'milk-products',  'Produk susu dan olahannya untuk kebutuhan sehari-hari'),
    ('Medicine',       'medicine',       'Obat-obatan dan produk kesehatan'),
    ('Sports',         'sports',         'Perlengkapan olahraga'),
    ('Vegetables',     'vegetables',     'Sayuran segar dan organik');

-- Insert products for Milk Products category
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active)
VALUES 
    (2, 'Susu UHT Full Cream 1L',      'susu-uht-full-cream-1l',      'Susu UHT full cream dengan kemasan 1 liter, kaya kalsium dan protein.', 16500, 12000, 200, TRUE),
    (2, 'Yogurt Greek Style',          'yogurt-greek-style',          'Yogurt kental bergaya Yunani dengan kandungan protein tinggi dan rendah lemak.', 22000, 15000, 75, TRUE),
    (2, 'Keju Cheddar Slice 10pcs',    'keju-cheddar-slice-10pcs',    'Keju cheddar dalam kemasan praktis, sudah dalam bentuk slice siap pakai.', 28500, 19000, 100, TRUE),
    (2, 'Susu Bubuk Kalsium Tinggi',   'susu-bubuk-kalsium',          'Susu bubuk dengan kalsium tinggi untuk kesehatan tulang, cocok untuk semua usia.', 75000, 50000, 120, TRUE),
    (2, 'Susu Kental Manis 370g',      'susu-kental-manis',           'Susu kental manis dalam kemasan kaleng, cocok untuk minuman dan kue.', 15000, 10000, 150, TRUE),
    (2, 'Mentega Tawar 227g',          'mentega-tawar',               'Mentega tawar berkualitas premium untuk memasak dan memanggang.', 45000, 30000, 80, TRUE),
    (2, 'Susu Segar Pasteurisasi',     'susu-segar-pasteurisasi',     'Susu segar yang dipasteurisasi, tanpa pengawet, rasa original.', 25000, 18000, 100, TRUE),
    (2, 'Cream Cheese 250g',           'cream-cheese',                'Cream cheese lembut untuk olesan roti atau bahan kue.', 35000, 25000, 60, TRUE),
    (2, 'Susu Cokelat UHT 250ml',      'susu-cokelat-uht',            'Susu cokelat UHT dalam kemasan praktis, cocok untuk bekal.', 8000, 5000, 200, TRUE),
    (2, 'Yogurt Drink Probiotik',      'yogurt-drink-probiotik',      'Minuman yogurt dengan probiotik untuk kesehatan pencernaan.', 12000, 8000, 150, TRUE);
    
-- Add features for Vitamin C 1000mg
-- NOTE: Adjust @product_id_vitamin value as needed based on your actual insertion order
SET @product_id_vitamin = 3; -- Assuming this is the ID for Vitamin C...
INSERT INTO product_features (product_id, feature_text, feature_order) VALUES
    (3, 'Dosis 1000mg per tablet', 1),
    (3, 'Dengan teknologi time-release', 2),
    (3, 'Membantu menjaga daya tahan tubuh', 3),
    (3, 'Membantu produksi kolagen', 4),
    (3, 'Bebas gula tambahan', 5);

-- Add specifications for Vitamin C
INSERT INTO product_specifications (product_id, spec_name, spec_value, spec_order) VALUES
    (3, 'Dosis', '1000mg', 1),
    (3, 'Bentuk', 'Tablet', 2),
    (3, 'Jumlah', '30 tablet', 3),
    (3, 'Masa Simpan', '2 tahun', 4),
    (3, 'Aturan Pakai', '1 tablet sehari', 5);

-- Add features for Lipstik Matte
SET @product_id_lipstik = 1; -- Assuming this is the ID for Lipstik

INSERT INTO product_features (product_id, feature_text, feature_order) VALUES 
    (1, 'Formula matte tahan lama hingga 10 jam', 1),
    (1, 'Tidak membuat bibir kering', 2),
    (1, 'Pigmentasi tinggi', 3),
    (1, 'Tersedia dalam 10 warna', 4),
    (1, 'Dermatologically tested', 5);

-- Add specifications for Lipstik
INSERT INTO product_specifications (product_id, spec_name, spec_value, spec_order) VALUES
    (1, 'Jenis', 'Matte', 1),
    (1, 'Berat Bersih', '3.5g', 2),
    (1, 'Ketahanan', 'Hingga 10 jam', 3),
    (1, 'Transfer-proof', 'Ya', 4),
    (1, 'Cruelty-free', 'Ya', 5);

-- Add features for Susu UHT
SET @product_id_susu = 4; -- Assuming this is the ID for Susu UHT

INSERT INTO product_features (product_id, feature_text, feature_order) VALUES 
    (2, 'Kemasan Tetra Pack 1 liter', 1),
    (2, 'Kandungan protein dan kalsium tinggi', 2),
    (2, 'Diolah dengan teknologi UHT', 3),
    (2, 'Tanpa bahan pengawet', 4),
    (2, 'Rasa yang kaya dan creamy', 5);

-- Add specifications for Susu UHT
INSERT INTO product_specifications (product_id, spec_name, spec_value, spec_order) VALUES
    (2, 'Volume', '1 liter', 1),
    (2, 'Jenis', 'Full Cream', 2),
    (2, 'Masa Simpan', '6 bulan sebelum dibuka', 3),
    (2, 'Penyimpanan', 'Suhu ruangan, di tempat sejuk', 4),
    (2, 'Setelah Dibuka', 'Simpan di kulkas, habiskan dalam 3 hari', 5);

-- Add nutrition information for Susu UHT
INSERT INTO product_nutrition (product_id, nutrient_name, nutrient_value, nutrient_order) VALUES
    (2, 'Energi', '65 kkal/100ml', 1),
    (2, 'Protein', '3.3g/100ml', 2),
    (2, 'Lemak', '3.5g/100ml', 3),
    (2, 'Karbohidrat', '4.7g/100ml', 4),
    (2, 'Kalsium', '120mg/100ml', 5);

INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active) VALUES
    (1, 'Serum Vitamin C 20%', 'serum-vitamin-c-20', 'Serum brightening dengan 20% vitamin C stabil, alpha arbutin, dan hyaluronic acid untuk mencerahkan dan menghidrasi kulit. Tekstur ringan cepat meresap.', 175000, 110000, 90, TRUE),
    (1, 'BB Cream SPF 50 PA+++', 'bb-cream-spf50', 'BB cream multi-fungsi dengan coverage medium dan perlindungan UV tinggi. Mengandung niacinamide dan ekstrak licorice untuk meratakan warna kulit.', 120000, 75000, 110, TRUE),
    (1, 'Micellar Water 400ml', 'micellar-water-400ml', 'Pembersih wajah tanpa bilas dengan micelle technology untuk mengangkat makeup dan kotoran. Cocok untuk semua jenis kulit termasuk sensitif.', 85000, 50000, 150, TRUE),
    (1, 'Masker Sheet Hydrating', 'masker-sheet-hydrating', 'Masker wajah sheet dengan ekstrak hyaluronic acid dan aloe vera untuk hidrasi intens. Bahan katun premium yang menempel sempurna di wajah.', 25000, 15000, 200, TRUE),
    (1, 'Eyeliner Waterproof', 'eyeliner-waterproof', 'Eyeliner pena dengan ujung brush tip presisi untuk garis tajam. Formula waterproof yang tahan hingga 24 jam tanpa smudge.', 65000, 40000, 130, TRUE),
    (1, 'Highlighter Palette', 'highlighter-palette', 'Palette highlighter dengan 4 shade yang bisa dipadukan. Tekstur buttery dengan finish dari natural sampai intense glow.', 145000, 90000, 70, TRUE),
    (1, 'Mascara Volume X5', 'mascara-volume-x5', 'Mascara dengan brush conical untuk volume dan panjang maksimal. Formula smudge-proof dan clump-free dengan ekstrak argan oil.', 95000, 60000, 100, TRUE),
    (1, 'Cleansing Balm 100g', 'cleansing-balm-100g', 'Pembersih makeup berbentuk balm yang meleleh menjadi oil ketika dipijat. Efektif mengangkat makeup waterproof tanpa meninggalkan rasa berminyak.', 135000, 85000, 85, TRUE),
    (1, 'Toner AHA/BHA 150ml', 'toner-aha-bha', 'Toner eksfoliasi dengan kombinasi AHA dan BHA untuk mengangkat sel kulit mati. pH balanced dan mengandung centella asiatica untuk menenangkan.', 110000, 70000, 95, TRUE),
    (1, 'Lip Scrub Brown Sugar', 'lip-scrub-brown-sugar', 'Scrub bibir dengan butiran brown sugar dan ekstrak vanilla. Melembabkan sekaligus mengangkat kulit bibir kering dengan lembut.', 45000, 28000, 120, TRUE),
    (1, 'Setting Spray Matte', 'setting-spray-matte', 'Setting spray dengan finish matte untuk membuat makeup tahan lama hingga 16 jam. Mengontrol minyak tanpa membuat kulit kering.', 115000, 75000, 80, TRUE),
    (1, 'Concealer Full Coverage', 'concealer-full-coverage', 'Concealer creamy dengan coverage tinggi yang bisa menutup dark circle dan imperfeksi tanpa creasing. Tersedia dalam 8 shade.', 105000, 65000, 90, TRUE),
    (1, 'Blush On Cream', 'blush-on-cream', 'Blush cream dengan finish natural dewy yang mudah blend. Pigmen tinggi dengan warna yang bisa build up.', 75000, 45000, 110, TRUE),
    (1, 'Makeup Brush Set', 'makeup-brush-set', 'Set 12 kuas makeup premium dengan bulu sintetis lembut. Handle ergonomis dan tahan lama untuk aplikasi makeup yang presisi.', 225000, 150000, 60, TRUE),
    (1, 'Night Cream Regenerating', 'night-cream-regenerating', 'Krim malam dengan retinol 0.5% dan peptide untuk regenerasi kulit. Melembabkan dalam sekaligus mengurangi garis halus saat tidur.', 185000, 120000, 75, TRUE);

-- Duplicate product_features table removed

-- Insert additional medicine products
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active) VALUES
    (3, 'Paracetamol 500mg (20 tablets)', 'paracetamol-500mg', 'Obat pereda nyeri dan penurun demam dengan dosis 500mg per tablet. Aman dikonsumsi untuk dewasa dan anak di atas 12 tahun.', 15000, 8000, 200, TRUE),
    (3, 'Vitamin B Complex', 'vitamin-b-complex', 'Suplemen vitamin B kompleks untuk menjaga kesehatan sistem saraf dan meningkatkan energi. Isi 30 tablet.', 45000, 28000, 150, TRUE),
    (3, 'First Aid Kit Complete', 'first-aid-kit-complete', 'Kit P3K lengkap berisi perban, plester, antiseptik, gunting, dan perlengkapan pertolongan pertama lainnya.', 185000, 120000, 50, TRUE),
    (3, 'Antiseptic Spray 100ml', 'antiseptic-spray-100ml', 'Spray antiseptik untuk membersihkan dan melindungi luka ringan. Tidak perih dan aman untuk anak-anak.', 35000, 20000, 120, TRUE),
    (3, 'Multivitamin Daily (30 caps)', 'multivitamin-daily', 'Multivitamin harian lengkap dengan mineral esensial untuk menjaga daya tahan tubuh. Konsumsi 1 kapsul per hari.', 95000, 60000, 100, TRUE),
    (3, 'Muscle Pain Relief Cream', 'muscle-pain-relief', 'Krim pereda nyeri otot dengan formula hangat yang meresap cepat. Dengan ekstrak jahe dan eucalyptus.', 48000, 30000, 80, TRUE),
    (3, 'Digital Thermometer', 'digital-thermometer', 'Termometer digital dengan akurasi tinggi dan hasil pengukuran cepat dalam 10 detik. Dilengkapi alarm demam.', 75000, 45000, 60, TRUE),
    (3, 'Wound Care Set', 'wound-care-set', 'Set perawatan luka steril berisi kasa, plester, antiseptik, dan sarung tangan. Ideal untuk pertolongan pertama.', 65000, 40000, 70, TRUE),
    (3, 'Calcium + D3 (60 tabs)', 'calcium-d3', 'Suplemen kalsium dengan vitamin D3 untuk kesehatan tulang. Cocok untuk dewasa dan lansia.', 78000, 48000, 90, TRUE),
    (3, 'Flu & Cold Relief (10 sachets)', 'flu-cold-relief', 'Obat flu dengan formula lengkap untuk meredakan gejala flu, pilek, dan batuk. Rasa jeruk.', 35000, 22000, 150, TRUE);

-- Add features for each medicine product
INSERT INTO product_features (product_id, feature_text, feature_order) VALUES
    -- Paracetamol features
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Dosis tepat 500mg per tablet', 1),
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Meredakan nyeri ringan hingga sedang', 2),
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Menurunkan demam dengan efektif', 3),
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Aman dikonsumsi setiap 4-6 jam', 4),
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Kemasan praktis strip aluminium', 5),

    -- Vitamin B Complex features
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Kombinasi lengkap vitamin B1, B2, B6, dan B12', 1),
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Meningkatkan metabolisme energi', 2),
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Menjaga kesehatan sistem saraf', 3),
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Membantu pembentukan sel darah merah', 4),
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Formula time-release untuk penyerapan optimal', 5),

    -- First Aid Kit features
    ((SELECT product_id FROM products WHERE slug = 'first-aid-kit-complete'), 'Tas P3K tahan air dengan ukuran kompak', 1),
    ((SELECT product_id FROM products WHERE slug = 'first-aid-kit-complete'), 'Isi lengkap 42 item pertolongan pertama', 2),
    ((SELECT product_id FROM products WHERE slug = 'first-aid-kit-complete'), 'Termasuk panduan pertolongan pertama', 3),
    ((SELECT product_id FROM products WHERE slug = 'first-aid-kit-complete'), 'Ideal untuk rumah dan perjalanan', 4),
    ((SELECT product_id FROM products WHERE slug = 'first-aid-kit-complete'), 'Peralatan medis berkualitas tinggi', 5),

    -- Antiseptic Spray features
    ((SELECT product_id FROM products WHERE slug = 'antiseptic-spray-100ml'), 'Formula non-perih untuk semua usia', 1),
    ((SELECT product_id FROM products WHERE slug = 'antiseptic-spray-100ml'), 'Membunuh 99.9% kuman', 2),
    ((SELECT product_id FROM products WHERE slug = 'antiseptic-spray-100ml'), 'Praktis dengan aplikator spray', 3),
    ((SELECT product_id FROM products WHERE slug = 'antiseptic-spray-100ml'), 'Tidak meninggalkan noda', 4),
    ((SELECT product_id FROM products WHERE slug = 'antiseptic-spray-100ml'), 'Aman untuk luka ringan', 5),

    -- Multivitamin features
    ((SELECT product_id FROM products WHERE slug = 'multivitamin-daily'), '23 vitamin dan mineral esensial', 1),
    ((SELECT product_id FROM products WHERE slug = 'multivitamin-daily'), 'Dosis harian yang tepat', 2),
    ((SELECT product_id FROM products WHERE slug = 'multivitamin-daily'), 'Mendukung sistem imun', 3),
    ((SELECT product_id FROM products WHERE slug = 'multivitamin-daily'), 'Antioksidan tinggi', 4),
    ((SELECT product_id FROM products WHERE slug = 'multivitamin-daily'), 'Kapsul lunak mudah ditelan', 5);

-- Add product specifications
INSERT INTO product_specifications (product_id, spec_name, spec_value, spec_order) VALUES
    -- Paracetamol specifications
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Dosis', '500mg', 1),
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Bentuk', 'Tablet', 2),
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Isi', '20 tablet', 3),
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Aturan Pakai', 'Setiap 4-6 jam', 4),
    ((SELECT product_id FROM products WHERE slug = 'paracetamol-500mg'), 'Indikasi', 'Demam dan nyeri', 5),

    -- Vitamin B Complex specifications
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Komposisi', 'B1, B2, B6, B12', 1),
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Bentuk', 'Tablet', 2),
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Isi', '30 tablet', 3),
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Dosis', '1 tablet sehari', 4),
    ((SELECT product_id FROM products WHERE slug = 'vitamin-b-complex'), 'Penyimpanan', 'Suhu ruang', 5);

-- Insert sports products
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active) VALUES
    (4, 'Bola Basket Spalding NBA', 'bola-basket-spalding-nba', 'Bola basket profesional dengan grip superior dan durabilitas tinggi. Cocok untuk indoor dan outdoor.', 450000, 300000, 50, TRUE),
    (4, 'Raket Badminton Yonex Astrox', 'raket-badminton-yonex-astrox', 'Raket badminton premium dengan teknologi isometric untuk kontrol dan power maksimal. Termasuk tas raket.', 1200000, 800000, 30, TRUE),
    (4, 'Sepatu Lari Nike Air Zoom', 'sepatu-lari-nike-air', 'Sepatu lari dengan teknologi Air Zoom untuk kenyamanan maksimal dan responsif. Breathable mesh upper.', 1500000, 900000, 45, TRUE),
    (4, 'Matras Yoga Premium 6mm', 'matras-yoga-premium', 'Matras yoga anti slip dengan ketebalan 6mm. Nyaman digunakan dan mudah dibersihkan.', 250000, 150000, 100, TRUE),
    (4, 'Dumbbell Set 10kg', 'dumbbell-set-10kg', 'Set dumbbell vinyl coated dengan total berat 10kg (5kg x 2). Ideal untuk latihan di rumah.', 380000, 250000, 40, TRUE),
    (4, 'Jersey Bola Original', 'jersey-bola-original', 'Jersey sepakbola berkualitas tinggi dengan teknologi dry-fit. Tersedia berbagai ukuran.', 350000, 200000, 75, TRUE),
    (4, 'Tali Skipping Pro', 'tali-skipping-pro', 'Tali skipping profesional dengan ball bearing dan panjang adjustable. Cocok untuk CrossFit.', 120000, 70000, 100, TRUE),
    (4, 'Bola Voli Mikasa', 'bola-voli-mikasa', 'Bola voli kompetisi dengan desain aerodinamis dan grip superior. Standar FIVB.', 500000, 350000, 35, TRUE),
    (4, 'Tas Olahraga Multifungsi', 'tas-olahraga-multifungsi', 'Tas olahraga dengan kompartemen sepatu terpisah dan banyak kantong. Kapasitas 45L.', 280000, 180000, 60, TRUE),
    (4, 'Resistance Band Set', 'resistance-band-set', 'Set 5 resistance band dengan tingkat ketahanan berbeda. Includes carrying bag dan panduan latihan.', 175000, 100000, 85, TRUE);

-- Add features for each sports product
INSERT INTO product_features (product_id, feature_text, feature_order) VALUES
    -- Bola Basket features
    ((SELECT product_id FROM products WHERE slug = 'bola-basket-spalding-nba'), 'Official NBA size and weight', 1),
    ((SELECT product_id FROM products WHERE slug = 'bola-basket-spalding-nba'), 'Premium composite leather cover', 2),
    ((SELECT product_id FROM products WHERE slug = 'bola-basket-spalding-nba'), 'Deep channel design for better grip', 3),
    ((SELECT product_id FROM products WHERE slug = 'bola-basket-spalding-nba'), 'Indoor/outdoor use', 4),
    ((SELECT product_id FROM products WHERE slug = 'bola-basket-spalding-nba'), '2 years warranty', 5),

    -- Raket Badminton features
    ((SELECT product_id FROM products WHERE slug = 'raket-badminton-yonex-astrox'), 'Isometric head shape technology', 1),
    ((SELECT product_id FROM products WHERE slug = 'raket-badminton-yonex-astrox'), 'High modulus graphite frame', 2),
    ((SELECT product_id FROM products WHERE slug = 'raket-badminton-yonex-astrox'), 'Included premium racket bag', 3),
    ((SELECT product_id FROM products WHERE slug = 'raket-badminton-yonex-astrox'), 'Pre-strung with premium string', 4),
    ((SELECT product_id FROM products WHERE slug = 'raket-badminton-yonex-astrox'), 'Weight: 85-89g', 5),

    -- Sepatu Lari features
    ((SELECT product_id FROM products WHERE slug = 'sepatu-lari-nike-air'), 'Air Zoom cushioning technology', 1),
    ((SELECT product_id FROM products WHERE slug = 'sepatu-lari-nike-air'), 'Breathable mesh upper', 2),
    ((SELECT product_id FROM products WHERE slug = 'sepatu-lari-nike-air'), 'Responsive foam midsole', 3),
    ((SELECT product_id FROM products WHERE slug = 'sepatu-lari-nike-air'), 'Durable rubber outsole', 4),
    ((SELECT product_id FROM products WHERE slug = 'sepatu-lari-nike-air'), 'Available in various sizes', 5),

    -- Matras Yoga features
    ((SELECT product_id FROM products WHERE slug = 'matras-yoga-premium'), '6mm thickness for comfort', 1),
    ((SELECT product_id FROM products WHERE slug = 'matras-yoga-premium'), 'Non-slip surface', 2),
    ((SELECT product_id FROM products WHERE slug = 'matras-yoga-premium'), 'Easy to clean material', 3),
    ((SELECT product_id FROM products WHERE slug = 'matras-yoga-premium'), 'Includes carrying strap', 4),
    ((SELECT product_id FROM products WHERE slug = 'matras-yoga-premium'), 'Eco-friendly TPE material', 5),

    -- Dumbbell Set features
    ((SELECT product_id FROM products WHERE slug = 'dumbbell-set-10kg'), 'Vinyl coated for durability', 1),
    ((SELECT product_id FROM products WHERE slug = 'dumbbell-set-10kg'), 'Anti-slip grip', 2),
    ((SELECT product_id FROM products WHERE slug = 'dumbbell-set-10kg'), 'Hexagonal design prevents rolling', 3),
    ((SELECT product_id FROM products WHERE slug = 'dumbbell-set-10kg'), 'Set includes 2x5kg weights', 4),
    ((SELECT product_id FROM products WHERE slug = 'dumbbell-set-10kg'), 'Ideal for home workouts', 5);

CREATE TABLE `reviews` (
    `review_id`   INT(11)      NOT NULL AUTO_INCREMENT,
    `product_id`  INT(11)      NOT NULL,
    `user_id`     INT(11)      NOT NULL,
    `rating`      DECIMAL(3,1) NOT NULL DEFAULT 0.0,
    `review_text` TEXT,
    `created_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`  TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`review_id`),
    KEY `product_id` (`product_id`),
    KEY `user_id` (`user_id`),
    CONSTRAINT `reviews_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`) ON DELETE CASCADE,
    CONSTRAINT `reviews_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    order_id        INT PRIMARY KEY AUTO_INCREMENT,
    order_number    VARCHAR(50) UNIQUE NOT NULL,
    user_id         INT,
    NAME            VARCHAR(255) NOT NULL,
    email           VARCHAR(255) NOT NULL,
    phone           VARCHAR(20)  NOT NULL,
    address         TEXT         NOT NULL,
    subtotal        DECIMAL(10,2) NOT NULL,
    shipping_cost   DECIMAL(10,2) NOT NULL,
    total_amount    DECIMAL(10,2) NOT NULL,
    payment_method  ENUM('bank_transfer', 'cod') NOT NULL,
    STATUS          ENUM('pending', 'paid', 'processing', 'shipped', 'delivered', 'cancelled') NOT NULL DEFAULT 'pending',
    created_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

-- Order items table
CREATE TABLE IF NOT EXISTS order_items (
    order_item_id   INT PRIMARY KEY AUTO_INCREMENT,
    order_id        INT NOT NULL,
    product_id      INT NOT NULL,
    quantity        INT NOT NULL,
    price           DECIMAL(10,2) NOT NULL,
    subtotal        DECIMAL(10,2) NOT NULL,
    created_at      TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE RESTRICT
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

-- Order status history table
CREATE TABLE IF NOT EXISTS order_status_history (
    history_id  INT PRIMARY KEY AUTO_INCREMENT,
    order_id    INT NOT NULL,
    STATUS      ENUM('pending', 'paid', 'processing', 'shipped', 'delivered', 'cancelled') NOT NULL,
    notes       TEXT,
    created_at  TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

ALTER TABLE orders ADD COLUMN order_number VARCHAR(50) NOT NULL UNIQUE AFTER order_id;

CREATE TABLE orders (
    order_id        INT AUTO_INCREMENT PRIMARY KEY,
    order_number    VARCHAR(50) NOT NULL UNIQUE,
    user_id         INT,
    NAME            VARCHAR(100),
    email           VARCHAR(100),
    phone           VARCHAR(20),
    address         TEXT,
    city            VARCHAR(100),
    postal_code     VARCHAR(20),
    subtotal        DECIMAL(10, 2),
    shipping_cost   DECIMAL(10, 2),
    total_amount    DECIMAL(10, 2),
    shipping_method VARCHAR(50),
    payment_method  VARCHAR(50),
    STATUS          ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled') DEFAULT 'pending',
    created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

ALTER TABLE orders 
    ADD COLUMN STATUS VARCHAR(20) DEFAULT 'pending' AFTER payment_method;

ALTER TABLE orders 
    ADD COLUMN shipping_name  VARCHAR(100) NOT NULL AFTER user_id,
    ADD COLUMN shipping_email VARCHAR(100) NOT NULL AFTER shipping_name,
    ADD COLUMN shipping_phone VARCHAR(20)  NOT NULL AFTER shipping_email;

ALTER TABLE order_items 
    ADD COLUMN price DECIMAL(10, 2) NOT NULL AFTER product_id;

ALTER TABLE orders CHANGE STATUS STATUS VARCHAR(50) DEFAULT 'pending';
ALTER TABLE orders CHANGE order_status STATUS VARCHAR(50) DEFAULT 'pending';

-- 1. Pastikan tabel orders memiliki kolom status yang diperlukan
ALTER TABLE orders 
    ADD COLUMN STATUS VARCHAR(50) DEFAULT 'pending' 
    COMMENT 'Status pesanan: pending, processing, shipped, delivered, cancelled';

-- 2. Buat atau perbaiki tabel order_status_history untuk tracking perubahan status
CREATE TABLE IF NOT EXISTS order_status_history (
    id        INT AUTO_INCREMENT PRIMARY KEY,
    order_id  INT NOT NULL,
    admin_id  INT NULL COMMENT 'Admin yang mengubah status, NULL jika sistem',
    STATUS    VARCHAR(50) NOT NULL COMMENT 'Status baru pesanan',
    note      TEXT NULL COMMENT 'Catatan tentang perubahan status',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

-- 3. Tambahkan trigger untuk mencatat perubahan status pesanan secara otomatis
DELIMITER //
CREATE TRIGGER after_order_status_update
AFTER UPDATE ON orders
FOR EACH ROW
BEGIN
    IF NEW.status != OLD.status OR (OLD.status IS NULL AND NEW.status IS NOT NULL) THEN
        INSERT INTO order_status_history (order_id, admin_id, STATUS, note)
        VALUES (NEW.order_id, NULL, NEW.status, 'Status diperbarui oleh sistem');
    END IF;
END//
DELIMITER ;

-- 4. Tambahkan tabel admin_permissions untuk manajemen hak akses admin
CREATE TABLE IF NOT EXISTS admin_permissions (
    id         INT AUTO_INCREMENT PRIMARY KEY,
    user_id    INT NOT NULL,
    module     VARCHAR(50) NOT NULL COMMENT 'Nama modul: orders, products, users, dll',
    can_view   BOOLEAN DEFAULT FALSE,
    can_add    BOOLEAN DEFAULT FALSE,
    can_edit   BOOLEAN DEFAULT FALSE,
    can_delete BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY (user_id, module)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

-- 5. Tambahkan tabel untuk notifikasi admin
CREATE TABLE IF NOT EXISTS admin_notifications (
    id        INT AUTO_INCREMENT PRIMARY KEY,
    user_id   INT NULL COMMENT 'NULL untuk semua admin',
    title     VARCHAR(100) NOT NULL,
    message   TEXT NOT NULL,
    is_read   BOOLEAN DEFAULT FALSE,
    TYPE      VARCHAR(20) DEFAULT 'info' COMMENT 'info, warning, danger, success',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

-- 6. Tambahkan kolom shipping_tracking ke tabel orders
ALTER TABLE orders
    ADD COLUMN shipping_tracking VARCHAR(100) NULL COMMENT 'Nomor resi pengiriman';

-- 7. Insert data permission untuk admin default
INSERT INTO admin_permissions (user_id, module, can_view, can_add, can_edit, can_delete)
VALUES 
    (1, 'orders',     TRUE, TRUE, TRUE, TRUE),
    (1, 'products',   TRUE, TRUE, TRUE, TRUE),
    (1, 'users',      TRUE, TRUE, TRUE, TRUE),
    (1, 'categories', TRUE, TRUE, TRUE, TRUE),
    (1, 'reports',    TRUE, TRUE, TRUE, TRUE);

-- 8. Tambahkan tabel untuk catatan pengiriman
CREATE TABLE IF NOT EXISTS order_shipments (
    id                INT AUTO_INCREMENT PRIMARY KEY,
    order_id          INT NOT NULL,
    courier           VARCHAR(50) NOT NULL COMMENT 'Nama kurir: JNE, TIKI, dll',
    tracking_number   VARCHAR(100) NULL,
    shipping_cost     DECIMAL(10,2) NOT NULL DEFAULT 0,
    estimated_delivery DATE NULL,
    actual_delivery   DATE NULL,
    created_by        INT NULL COMMENT 'Admin yang membuat pengiriman',
    created_at        TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at        TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

-- 9. Tambahkan tabel untuk notifikasi pelanggan terkait status pesanan
CREATE TABLE IF NOT EXISTS order_notifications (
    id        INT AUTO_INCREMENT PRIMARY KEY,
    order_id  INT NOT NULL,
    user_id   INT NOT NULL,
    message   TEXT NOT NULL,
    is_read   BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4;

-- 10. Trigger untuk menambahkan notifikasi ketika status pesanan berubah
DELIMITER //
CREATE TRIGGER after_order_status_history_insert
AFTER INSERT ON order_status_history
FOR EACH ROW
BEGIN
    DECLARE order_number VARCHAR(50);
    DECLARE user_id INT;

    -- Ambil nomor pesanan dan user_id
    SELECT o.order_number, o.user_id INTO order_number, user_id
    FROM orders o WHERE o.order_id = NEW.order_id;

    -- Buat pesan notifikasi berdasarkan status baru
    CASE NEW.status
        WHEN 'diproses' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' sedang diproses.'));
        WHEN 'dikirim' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' telah dikirim.'));
        WHEN 'terkirim' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' telah diterima.'));
        WHEN 'dibatalkan' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' telah dibatalkan.'));
    END CASE;
END//
DELIMITER ;

-- Fix the order notification trigger with proper CASE statement including ELSE clause
DELIMITER //

DROP TRIGGER IF EXISTS order_notification_trigger//

CREATE TRIGGER order_notification_trigger
AFTER UPDATE ON orders
FOR EACH ROW
BEGIN
    CASE NEW.status
        WHEN 'pending' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, NEW.user_id, CONCAT('Pesanan #', NEW.order_number, ' sedang pending.'));
        WHEN 'dibuat' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, NEW.user_id, CONCAT('Pesanan #', NEW.order_number, ' telah dibuat.'));
        WHEN 'diproses' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, NEW.user_id, CONCAT('Pesanan #', NEW.order_number, ' sedang diproses.'));
        WHEN 'dikirim' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, NEW.user_id, CONCAT('Pesanan #', NEW.order_number, ' telah dikirim.'));
        WHEN 'terkirim' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, NEW.user_id, CONCAT('Pesanan #', NEW.order_number, ' telah diterima.'));
        WHEN 'dibatalkan' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, NEW.user_id, CONCAT('Pesanan #', NEW.order_number, ' telah dibatalkan.'));
        ELSE
            -- Handle any other status values to prevent CASE not found error
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, NEW.user_id, CONCAT('Status pesanan #', NEW.order_number, ' telah diubah menjadi ', NEW.status));
    END CASE;
END//

DELIMITER ;

-- Fix the after_order_status_history_insert trigger with proper CASE statement
DELIMITER //

DROP TRIGGER IF EXISTS after_order_status_history_insert//

CREATE TRIGGER after_order_status_history_insert
AFTER INSERT ON order_status_history
FOR EACH ROW
BEGIN
    DECLARE order_number VARCHAR(50);
    DECLARE user_id INT;

    -- Ambil nomor pesanan dan user_id
    SELECT o.order_number, o.user_id INTO order_number, user_id
    FROM orders o WHERE o.order_id = NEW.order_id;

    -- Buat pesan notifikasi berdasarkan status baru
    CASE NEW.status
        WHEN 'pending' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' sedang pending.'));
        WHEN 'dibuat' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' telah dibuat.'));
        WHEN 'diproses' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' sedang diproses.'));
        WHEN 'dikirim' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' telah dikirim.'));
        WHEN 'terkirim' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' telah diterima.'));
        WHEN 'dibatalkan' THEN
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Pesanan #', order_number, ' telah dibatalkan.'));
        ELSE
            -- Handle any other status values
            INSERT INTO order_notifications (order_id, user_id, message)
            VALUES (NEW.order_id, user_id, CONCAT('Status pesanan #', order_number, ' telah diubah menjadi ', NEW.status));
    END CASE;
END//

DELIMITER ;

DESCRIBE order_status_history;
SHOW COLUMNS FROM order_status_history;

ALTER TABLE order_items ADD COLUMN updated_at VARCHAR(255);
ALTER TABLE orders ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

SHOW COLUMNS FROM order_status_history;
ALTER TABLE order_items ADD COLUMN updated_at VARCHAR(255);
ALTER TABLE orders ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

ALTER TABLE categories ADD COLUMN is_active BOOLEAN;

-- Add is_featured column to products table if not exists
ALTER TABLE products ADD COLUMN is_featured BOOLEAN DEFAULT FALSE;

-- Add more products to reach 200 total
-- Electronics Category Products (category_id = 6)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (6, 'Smartphone Samsung Galaxy A54', 'smartphone-samsung-a54', 'Smartphone dengan layar 6.4 inch Super AMOLED, kamera 50MP, RAM 8GB, storage 256GB. Performa tinggi untuk kebutuhan sehari-hari.', 4500000, 3800000, 25, TRUE, TRUE),
    (6, 'Laptop ASUS VivoBook 14', 'laptop-asus-vivobook-14', 'Laptop ringan dengan prosesor Intel Core i5, RAM 8GB, SSD 512GB, layar 14 inch FHD. Cocok untuk kerja dan kuliah.', 8500000, 7200000, 15, TRUE, TRUE),
    (6, 'Headphone Sony WH-1000XM4', 'headphone-sony-wh1000xm4', 'Headphone wireless dengan noise cancelling terbaik di kelasnya. Battery life hingga 30 jam dengan quick charge.', 4200000, 3500000, 30, TRUE, FALSE),
    (6, 'Smart TV LG 43 inch 4K', 'smart-tv-lg-43-4k', 'Smart TV 43 inch dengan resolusi 4K UHD, WebOS, HDR10, dan built-in WiFi. Pengalaman menonton yang memukau.', 5800000, 4900000, 20, TRUE, TRUE),
    (6, 'Tablet iPad Air 10.9', 'tablet-ipad-air-109', 'iPad Air dengan chip M1, layar Liquid Retina 10.9 inch, storage 64GB. Performa desktop dalam bentuk tablet.', 8900000, 7500000, 18, TRUE, FALSE),
    (6, 'Smartwatch Apple Watch SE', 'smartwatch-apple-watch-se', 'Apple Watch SE dengan GPS, monitor kesehatan, tahan air, dan battery life seharian. Cocok untuk gaya hidup aktif.', 3200000, 2700000, 35, TRUE, TRUE),
    (6, 'Speaker Bluetooth JBL Charge 5', 'speaker-jbl-charge-5', 'Speaker portable dengan suara bass yang powerful, tahan air IP67, dan dapat menjadi power bank untuk device lain.', 1800000, 1400000, 50, TRUE, FALSE),
    (6, 'Kamera Mirrorless Sony A6000', 'kamera-sony-a6000', 'Kamera mirrorless dengan sensor APS-C 24.3MP, autofocus cepat, dan video 1080p. Ideal untuk fotografi pemula hingga menengah.', 6500000, 5500000, 12, TRUE, TRUE),
    (6, 'Power Bank Anker 20000mAh', 'powerbank-anker-20000', 'Power bank kapasitas besar dengan fast charging, 2 port USB, dan LED indicator. Dapat mengisi smartphone hingga 5 kali.', 650000, 450000, 80, TRUE, FALSE),
    (6, 'Wireless Charger Samsung', 'wireless-charger-samsung', 'Wireless charger dengan fast charging 15W, kompatibel dengan semua device Qi-enabled. Desain slim dan elegant.', 450000, 300000, 60, TRUE, FALSE),
    (6, 'Gaming Mouse Logitech G502', 'gaming-mouse-logitech-g502', 'Gaming mouse dengan sensor HERO 25K, 11 tombol programmable, dan RGB lighting. Presisi tinggi untuk gaming kompetitif.', 850000, 600000, 40, TRUE, FALSE),
    (6, 'Mechanical Keyboard Keychron K2', 'keyboard-keychron-k2', 'Mechanical keyboard wireless dengan switch Gateron, backlight RGB, dan kompatibel Mac/Windows. Typing experience premium.', 1200000, 900000, 25, TRUE, TRUE),
    (6, 'Webcam Logitech C920', 'webcam-logitech-c920', 'Webcam HD 1080p dengan autofocus, mikrofon stereo, dan kompatibilitas universal. Perfect untuk video call dan streaming.', 1100000, 800000, 35, TRUE, FALSE),
    (6, 'SSD External Samsung T7 1TB', 'ssd-samsung-t7-1tb', 'SSD eksternal dengan kecepatan transfer hingga 1050MB/s, USB 3.2, dan enkripsi hardware. Portable dan secure.', 1800000, 1400000, 30, TRUE, FALSE),
    (6, 'Router WiFi 6 TP-Link Archer AX50', 'router-tplink-ax50', 'Router WiFi 6 dengan kecepatan hingga 3Gbps, 4 antena, dan teknologi OFDMA. Coverage area luas untuk rumah besar.', 1500000, 1100000, 20, TRUE, FALSE);

-- Fashion Category Products (category_id = 7)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (7, 'Kemeja Pria Formal Putih', 'kemeja-pria-formal-putih', 'Kemeja formal pria warna putih dengan bahan katun premium, slim fit, dan kualitas jahitan rapi. Cocok untuk acara formal dan kantor.', 185000, 120000, 100, TRUE, TRUE),
    (7, 'Dress Wanita Casual Floral', 'dress-wanita-casual-floral', 'Dress casual dengan motif floral yang cantik, bahan rayon adem, dan model A-line yang flattering untuk semua bentuk tubuh.', 220000, 150000, 75, TRUE, TRUE),
    (7, 'Jeans Pria Slim Fit Dark Blue', 'jeans-pria-slim-fit', 'Jeans pria dengan cut slim fit, warna dark blue yang versatile, bahan denim berkualitas tinggi dan tahan lama.', 320000, 220000, 60, TRUE, FALSE),
    (7, 'Blouse Wanita Satin Elegant', 'blouse-wanita-satin', 'Blouse wanita dengan bahan satin yang mewah, desain elegant dengan detail kancing mutiara, cocok untuk acara formal.', 275000, 180000, 50, TRUE, TRUE),
    (7, 'Celana Chino Pria Khaki', 'celana-chino-pria-khaki', 'Celana chino pria warna khaki dengan bahan cotton twill, model regular fit yang nyaman untuk daily wear.', 195000, 130000, 80, TRUE, FALSE),
    (7, 'Rok Midi Wanita Pleated', 'rok-midi-pleated', 'Rok midi dengan model pleated yang feminine, bahan polyester berkualitas, dan warna navy yang mudah dipadukan.', 165000, 110000, 70, TRUE, FALSE),
    (7, 'Jaket Bomber Pria Black', 'jaket-bomber-pria-black', 'Jaket bomber pria warna hitam dengan bahan polyester, desain klasik dengan zipper dan ribbing detail.', 385000, 260000, 45, TRUE, TRUE),
    (7, 'Cardigan Wanita Knit Cream', 'cardigan-wanita-knit', 'Cardigan rajut wanita warna cream dengan bahan wool blend, model oversized yang trendy dan hangat.', 295000, 200000, 55, TRUE, FALSE),
    (7, 'Polo Shirt Pria Navy', 'polo-shirt-pria-navy', 'Polo shirt pria warna navy dengan bahan cotton pique, logo bordir, dan kualitas premium untuk casual wear.', 145000, 95000, 90, TRUE, FALSE),
    (7, 'Jumpsuit Wanita Denim', 'jumpsuit-wanita-denim', 'Jumpsuit denim wanita dengan model overall, adjustable strap, dan multiple pockets. Trendy dan praktis.', 350000, 240000, 40, TRUE, TRUE),
    (7, 'Hoodie Unisex Gray', 'hoodie-unisex-gray', 'Hoodie unisex warna abu-abu dengan bahan fleece yang soft, kangaroo pocket, dan drawstring hood.', 225000, 150000, 85, TRUE, FALSE),
    (7, 'Maxi Dress Bohemian', 'maxi-dress-bohemian', 'Maxi dress dengan style bohemian, print ethnic yang unik, bahan chiffon yang ringan dan flowy.', 320000, 220000, 35, TRUE, TRUE),
    (7, 'Blazer Pria Formal Navy', 'blazer-pria-formal-navy', 'Blazer formal pria warna navy dengan bahan wool blend, lapel notch, dan lining berkualitas tinggi.', 650000, 450000, 25, TRUE, FALSE),
    (7, 'Skirt Wanita A-Line Black', 'skirt-wanita-aline-black', 'Skirt A-line warna hitam dengan bahan crepe, high waist design, dan panjang knee-length yang elegant.', 185000, 125000, 65, TRUE, FALSE),
    (7, 'T-Shirt Pria Basic White', 'tshirt-pria-basic-white', 'T-shirt basic pria warna putih dengan bahan cotton combed 30s, jahitan rapi, dan fit yang nyaman.', 85000, 55000, 120, TRUE, FALSE);

-- Home & Garden Category Products (category_id = 8)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (8, 'Set Peralatan Dapur Stainless', 'set-peralatan-dapur-stainless', 'Set lengkap peralatan dapur dari stainless steel berkualitas tinggi, terdiri dari panci, wajan, dan spatula.', 850000, 600000, 30, TRUE, TRUE),
    (8, 'Vacuum Cleaner Philips', 'vacuum-cleaner-philips', 'Vacuum cleaner dengan daya hisap kuat, filter HEPA, dan desain compact. Mudah digunakan untuk membersihkan rumah.', 1200000, 900000, 20, TRUE, TRUE),
    (8, 'Lampu Hias LED Strip 5M', 'lampu-hias-led-strip', 'LED strip 5 meter dengan remote control, 16 juta warna, dan dapat dipotong sesuai kebutuhan. Perfect untuk dekorasi.', 185000, 120000, 80, TRUE, FALSE),
    (8, 'Tanaman Hias Monstera Deliciosa', 'tanaman-monstera-deliciosa', 'Tanaman hias indoor populer dengan daun berlubang unik, mudah perawatan, dan dapat mempercantik ruangan.', 125000, 80000, 50, TRUE, TRUE),
    (8, 'Dispenser Air Hot & Cold', 'dispenser-air-hot-cold', 'Dispenser air dengan fitur hot dan cold, kapasitas galon 19L, hemat listrik, dan desain modern.', 1500000, 1100000, 15, TRUE, FALSE),
    (8, 'Set Sprei Katun Jepang Queen', 'set-sprei-katun-jepang', 'Set sprei dan sarung bantal dari katun Jepang premium, motif minimalis, ukuran queen 160x200cm.', 320000, 220000, 60, TRUE, TRUE),
    (8, 'Rice Cooker Digital Miyako', 'rice-cooker-miyako-digital', 'Rice cooker digital dengan kapasitas 1.8L, fitur keep warm, dan inner pot anti lengket. Cocok untuk keluarga kecil.', 450000, 320000, 40, TRUE, FALSE),
    (8, 'Kursi Gaming Ergonomis', 'kursi-gaming-ergonomis', 'Kursi gaming dengan desain ergonomis, bahan kulit sintetis, adjustable height, dan lumbar support untuk kenyamanan maksimal.', 1800000, 1300000, 25, TRUE, TRUE),
    (8, 'Rak Buku Minimalis 5 Tingkat', 'rak-buku-minimalis-5tingkat', 'Rak buku dengan desain minimalis 5 tingkat, bahan kayu engineered, dan mudah dirakit. Cocok untuk ruang terbatas.', 650000, 450000, 35, TRUE, FALSE),
    (8, 'Blender Philips 2L', 'blender-philips-2l', 'Blender dengan kapasitas 2L, motor 600W, 6 mata pisau stainless steel, dan jar kaca yang tahan panas.', 750000, 550000, 45, TRUE, FALSE),
    (8, 'Cermin Hias Bulat Vintage', 'cermin-hias-bulat-vintage', 'Cermin hias dengan frame vintage berbahan metal, diameter 60cm, dan desain yang elegan untuk dekorasi dinding.', 385000, 260000, 30, TRUE, FALSE),
    (8, 'Set Gorden Blackout 2 Panel', 'set-gorden-blackout-2panel', 'Set gorden blackout 2 panel dengan bahan polyester tebal, menghalangi cahaya 99%, dan tersedia berbagai warna.', 295000, 200000, 55, TRUE, TRUE),
    (8, 'Kipas Angin Tower Denpoo', 'kipas-angin-tower-denpoo', 'Kipas angin tower dengan remote control, 3 kecepatan, timer, dan oscillating function. Hemat tempat dan energy.', 850000, 600000, 25, TRUE, FALSE),
    (8, 'Set Piring Keramik 12 Pcs', 'set-piring-keramik-12pcs', 'Set piring keramik 12 pieces dengan desain elegant, tahan microwave dan dishwasher, cocok untuk daily use.', 420000, 280000, 40, TRUE, FALSE),
    (8, 'Humidifier Ultrasonic 3L', 'humidifier-ultrasonic-3l', 'Humidifier ultrasonic dengan kapasitas 3L, LED mood light, auto shut-off, dan dapat bekerja hingga 12 jam.', 320000, 220000, 50, TRUE, TRUE);

-- Books Category Products (category_id = 9)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (9, 'Novel "Laskar Pelangi" - Andrea Hirata', 'novel-laskar-pelangi', 'Novel bestseller Indonesia yang mengisahkan perjuangan anak-anak Belitung menempuh pendidikan. Inspiratif dan mengharukan.', 85000, 60000, 100, TRUE, TRUE),
    (9, 'Buku Motivasi "Atomic Habits"', 'buku-atomic-habits', 'Buku self-improvement tentang membangun kebiasaan baik dan menghilangkan kebiasaan buruk. Panduan praktis yang mudah diterapkan.', 120000, 85000, 80, TRUE, TRUE),
    (9, 'Kamus Bahasa Inggris Oxford', 'kamus-oxford-english', 'Kamus Bahasa Inggris-Indonesia Oxford dengan lebih dari 50.000 entri, pronunciation guide, dan contoh kalimat.', 195000, 140000, 60, TRUE, FALSE),
    (9, 'Buku Resep Masakan Indonesia', 'buku-resep-masakan-indonesia', 'Kumpulan resep masakan tradisional Indonesia lengkap dengan foto step-by-step dan tips memasak dari chef profesional.', 145000, 100000, 70, TRUE, TRUE),
    (9, 'Komik "One Piece" Volume 1-10', 'komik-one-piece-vol1-10', 'Set komik One Piece volume 1-10 dalam bahasa Indonesia, kondisi baru dengan kualitas cetak yang bagus.', 350000, 250000, 40, TRUE, FALSE),
    (9, 'Buku Anak "Dongeng Nusantara"', 'buku-dongeng-nusantara', 'Kumpulan dongeng tradisional Nusantara dengan ilustrasi menarik, cocok untuk anak usia 5-12 tahun.', 75000, 50000, 90, TRUE, TRUE),
    (9, 'Novel Romance "Dilan 1990"', 'novel-dilan-1990', 'Novel romance populer karya Pidi Baiq yang mengisahkan cinta remaja di tahun 90an. Sudah difilmkan dan sangat populer.', 68000, 45000, 120, TRUE, FALSE),
    (9, 'Buku Pelajaran Matematika SMA', 'buku-matematika-sma', 'Buku pelajaran Matematika untuk SMA kelas 10-12, sesuai kurikulum terbaru dengan pembahasan soal yang lengkap.', 185000, 130000, 50, TRUE, FALSE),
    (9, 'Atlas Dunia Terbaru 2024', 'atlas-dunia-2024', 'Atlas dunia edisi terbaru 2024 dengan peta detail, informasi negara, dan data statistik terkini. Hard cover berkualitas.', 225000, 160000, 35, TRUE, TRUE),
    (9, 'Buku Bisnis "Rich Dad Poor Dad"', 'buku-rich-dad-poor-dad', 'Buku klasik tentang literasi keuangan dan investasi yang telah mengubah mindset jutaan orang di dunia.', 95000, 65000, 85, TRUE, FALSE),
    (9, 'Novel Fantasi "Harry Potter Set"', 'novel-harry-potter-set', 'Set lengkap novel Harry Potter 7 buku dalam bahasa Indonesia, cover baru dengan kualitas kertas premium.', 650000, 480000, 20, TRUE, TRUE),
    (9, 'Buku Sejarah Indonesia Lengkap', 'buku-sejarah-indonesia', 'Buku sejarah Indonesia dari masa prasejarah hingga modern, dilengkapi foto dan ilustrasi yang menarik.', 165000, 115000, 45, TRUE, FALSE),
    (9, 'Notebook Premium A5 Dotted', 'notebook-premium-a5', 'Notebook premium ukuran A5 dengan kertas dotted 120gsm, hard cover, dan elastic band. Perfect untuk bullet journal.', 125000, 85000, 75, TRUE, FALSE),
    (9, 'Set Pensil Warna Faber Castell 48', 'pensil-warna-faber-48', 'Set pensil warna Faber Castell 48 warna dalam tin case, kualitas artist grade dengan pigmen yang tahan lama.', 285000, 200000, 30, TRUE, TRUE),
    (9, 'Buku Panduan Fotografi Digital', 'buku-panduan-fotografi', 'Panduan lengkap fotografi digital dari basic hingga advanced, teknik editing, dan tips dari fotografer profesional.', 155000, 110000, 55, TRUE, FALSE);

-- Toys Category Products (category_id = 10)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (10, 'LEGO Classic Creative Bricks', 'lego-classic-creative', 'Set LEGO Classic dengan 1000+ pieces untuk kreativitas tanpa batas. Cocok untuk anak usia 4+ dan dewasa.', 650000, 450000, 40, TRUE, TRUE),
    (10, 'Boneka Teddy Bear Jumbo', 'boneka-teddy-bear-jumbo', 'Boneka teddy bear ukuran jumbo 80cm dengan bahan plush yang lembut dan aman untuk anak. Warna coklat klasik.', 285000, 200000, 25, TRUE, TRUE),
    (10, 'Puzzle 1000 Pieces Pemandangan', 'puzzle-1000-pieces', 'Puzzle 1000 pieces dengan gambar pemandangan alam yang indah. Melatih kesabaran dan konsentrasi.', 125000, 85000, 60, TRUE, FALSE),
    (10, 'Remote Control Car Racing', 'rc-car-racing', 'Mobil remote control dengan kecepatan tinggi, battery rechargeable, dan desain racing yang keren. Skala 1:18.', 450000, 320000, 35, TRUE, TRUE),
    (10, 'Set Mainan Masak-masakan', 'mainan-masak-masakan', 'Set mainan masak-masakan lengkap dengan kompor, panci, dan makanan plastik. Edukatif untuk anak perempuan.', 185000, 125000, 50, TRUE, FALSE),
    (10, 'Action Figure Superhero Set', 'action-figure-superhero', 'Set action figure superhero dengan 6 karakter berbeda, articulated joints, dan aksesoris lengkap.', 320000, 220000, 30, TRUE, TRUE),
    (10, 'Board Game Monopoly Indonesia', 'monopoly-indonesia', 'Board game Monopoly edisi Indonesia dengan properti kota-kota di Indonesia. Fun untuk dimainkan keluarga.', 195000, 135000, 45, TRUE, FALSE),
    (10, 'Slime Kit DIY Complete', 'slime-kit-diy', 'Kit lengkap untuk membuat slime dengan berbagai warna dan tekstur. Includes glue, activator, dan glitter.', 95000, 65000, 80, TRUE, FALSE),
    (10, 'Drone Mini untuk Anak', 'drone-mini-anak', 'Drone mini yang aman untuk anak dengan auto-hover, LED lights, dan remote control yang mudah digunakan.', 385000, 270000, 20, TRUE, TRUE),
    (10, 'Set Balok Kayu Edukatif', 'balok-kayu-edukatif', 'Set balok kayu dengan berbagai bentuk dan warna untuk mengembangkan motorik halus dan kreativitas anak.', 165000, 115000, 55, TRUE, FALSE),
    (10, 'Mainan Robot Transformers', 'robot-transformers', 'Robot yang bisa berubah menjadi mobil dengan mekanisme transformasi yang mudah. Tinggi 20cm.', 225000, 155000, 40, TRUE, TRUE),
    (10, 'Playdough Set 12 Colors', 'playdough-set-12colors', 'Set playdough 12 warna dengan tools dan cetakan berbagai bentuk. Non-toxic dan aman untuk anak.', 145000, 100000, 70, TRUE, FALSE),
    (10, 'Mainan Kereta Api Thomas', 'kereta-api-thomas', 'Set kereta api Thomas dengan track, station, dan 3 gerbong. Battery operated dengan suara dan lampu.', 485000, 340000, 25, TRUE, TRUE),
    (10, 'Bola Basket Mini Indoor', 'bola-basket-mini-indoor', 'Set bola basket mini untuk indoor dengan ring adjustable height, bola, dan pompa. Cocok untuk anak.', 275000, 190000, 35, TRUE, FALSE),
    (10, 'Mainan Dokter-dokteran Set', 'mainan-dokter-set', 'Set mainan dokter lengkap dengan stetoskop, syringe, dan medical tools lainnya. Edukatif dan fun.', 125000, 85000, 60, TRUE, FALSE);

-- Vegetables Category Products (category_id = 5)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (5, 'Sayur Bayam Segar 500g', 'sayur-bayam-segar', 'Bayam segar pilihan dengan daun hijau yang renyah, kaya zat besi dan vitamin. Langsung dari petani lokal.', 8500, 5000, 200, TRUE, TRUE),
    (5, 'Wortel Baby 1kg', 'wortel-baby-1kg', 'Wortel baby ukuran kecil yang manis dan renyah, perfect untuk salad atau dimakan langsung. Organik dan segar.', 18000, 12000, 150, TRUE, TRUE),
    (5, 'Brokoli Hijau Fresh 1 buah', 'brokoli-hijau-fresh', 'Brokoli segar dengan kuntum yang padat dan hijau, kaya vitamin C dan serat. Ideal untuk menu diet sehat.', 15000, 10000, 100, TRUE, FALSE),
    (5, 'Tomat Cherry 250g', 'tomat-cherry-250g', 'Tomat cherry manis dan segar, perfect untuk salad atau snack sehat. Ukuran mini yang lucu dan bergizi.', 12000, 8000, 180, TRUE, TRUE),
    (5, 'Selada Keriting 1 ikat', 'selada-keriting-1ikat', 'Selada keriting segar dengan tekstur renyah, cocok untuk salad dan burger. Dicuci bersih dan siap konsumsi.', 9500, 6000, 120, TRUE, FALSE),
    (5, 'Kentang Granola 1kg', 'kentang-granola-1kg', 'Kentang granola berkualitas premium, cocok untuk digoreng, direbus, atau dipanggang. Tekstur pulen dan rasa enak.', 22000, 15000, 80, TRUE, TRUE),
    (5, 'Cabai Merah Keriting 250g', 'cabai-merah-keriting', 'Cabai merah keriting segar dengan tingkat kepedasan sedang, perfect untuk masakan Indonesia yang pedas.', 25000, 18000, 60, TRUE, FALSE),
    (5, 'Bawang Merah 500g', 'bawang-merah-500g', 'Bawang merah pilihan dengan ukuran sedang, aroma harum, dan rasa yang kuat. Essential untuk masakan Indonesia.', 35000, 25000, 100, TRUE, TRUE),
    (5, 'Jagung Manis 2 buah', 'jagung-manis-2buah', 'Jagung manis segar dengan biji yang penuh dan manis. Bisa direbus, dibakar, atau dijadikan salad.', 16000, 10000, 90, TRUE, FALSE),
    (5, 'Timun Jepang 500g', 'timun-jepang-500g', 'Timun Jepang dengan kulit tipis dan rasa yang segar, cocok untuk lalapan, salad, atau jus detox.', 14000, 9000, 110, TRUE, FALSE),
    (5, 'Paprika Mix 3 warna', 'paprika-mix-3warna', 'Paprika mix merah, kuning, dan hijau yang segar dan renyah. Kaya vitamin C dan perfect untuk tumisan.', 45000, 32000, 40, TRUE, TRUE),
    (5, 'Kacang Panjang 500g', 'kacang-panjang-500g', 'Kacang panjang segar dan muda, cocok untuk tumisan, gado-gado, atau sayur asem. Renyah dan bergizi.', 11000, 7000, 130, TRUE, FALSE),
    (5, 'Terong Ungu 500g', 'terong-ungu-500g', 'Terong ungu segar dengan daging yang padat, cocok untuk berbagai olahan seperti terong balado atau capcay.', 13000, 8500, 85, TRUE, FALSE),
    (5, 'Sawi Hijau 1 ikat', 'sawi-hijau-1ikat', 'Sawi hijau segar dengan daun yang lebar dan hijau, kaya vitamin dan mineral. Cocok untuk sup atau tumisan.', 7500, 4500, 160, TRUE, TRUE),
    (5, 'Labu Siam 1 buah', 'labu-siam-1buah', 'Labu siam segar dengan tekstur renyah dan rasa yang mild. Cocok untuk sayur bening atau tumisan.', 8000, 5000, 70, TRUE, FALSE);

-- Additional Cosmetics Products (category_id = 1)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (1, 'Foundation Liquid Full Coverage', 'foundation-liquid-full', 'Foundation cair dengan coverage penuh, tahan 24 jam, dan SPF 30. Tersedia 20 shade untuk semua warna kulit.', 185000, 125000, 80, TRUE, TRUE),
    (1, 'Eyeshadow Palette Nude', 'eyeshadow-palette-nude', 'Palette eyeshadow dengan 12 shade nude yang versatile, formula pigmented dan mudah di-blend.', 165000, 110000, 60, TRUE, FALSE),
    (1, 'Lip Tint Korean Style', 'lip-tint-korean', 'Lip tint dengan formula water-based yang memberikan warna natural dan tahan lama. Inspired by K-beauty.', 85000, 55000, 100, TRUE, TRUE),
    (1, 'Primer Face Pore Minimizing', 'primer-pore-minimizing', 'Primer wajah yang mengecilkan tampilan pori-pori dan membuat makeup tahan lebih lama. Cocok untuk kulit berminyak.', 125000, 85000, 70, TRUE, FALSE),
    (1, 'Bronzer Powder Matte', 'bronzer-powder-matte', 'Bronzer powder dengan finish matte untuk contouring dan memberikan efek sun-kissed yang natural.', 145000, 95000, 50, TRUE, TRUE),
    (1, 'Makeup Remover Micellar Oil', 'makeup-remover-oil', 'Makeup remover berbasis oil yang efektif mengangkat makeup waterproof tanpa meninggalkan residu berminyak.', 95000, 65000, 90, TRUE, FALSE),
    (1, 'Brow Gel Tinted Clear', 'brow-gel-tinted', 'Brow gel dengan tint ringan untuk memberikan warna dan hold pada alis. Formula long-lasting dan natural.', 75000, 50000, 85, TRUE, FALSE),
    (1, 'Face Mist Hydrating Spray', 'face-mist-hydrating', 'Face mist dengan hyaluronic acid untuk hidrasi instant. Bisa digunakan sebelum dan sesudah makeup.', 65000, 42000, 110, TRUE, TRUE),
    (1, 'Contour Stick Cream', 'contour-stick-cream', 'Contour stick dalam bentuk cream yang mudah di-blend, tersedia dalam 6 shade untuk berbagai warna kulit.', 105000, 70000, 65, TRUE, FALSE),
    (1, 'Nail Polish Set 12 Colors', 'nail-polish-set-12', 'Set nail polish 12 warna dengan formula quick-dry dan chip-resistant. Includes base coat dan top coat.', 195000, 130000, 40, TRUE, TRUE);

-- Additional Sports Products (category_id = 4)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (4, 'Sepatu Futsal Nike Mercurial', 'sepatu-futsal-nike', 'Sepatu futsal dengan teknologi Mercurial untuk kontrol bola maksimal dan kenyamanan saat bermain di lapangan indoor.', 1200000, 850000, 30, TRUE, TRUE),
    (4, 'Raket Tenis Wilson Pro Staff', 'raket-tenis-wilson', 'Raket tenis profesional dengan frame carbon fiber, weight 315g, dan string tension tinggi untuk power maksimal.', 2500000, 1800000, 15, TRUE, FALSE),
    (4, 'Sepeda Lipat Polygon 20 inch', 'sepeda-lipat-polygon', 'Sepeda lipat dengan frame aluminum, 7 speed, dan mudah dilipat untuk penyimpanan. Cocok untuk commuting.', 3500000, 2800000, 10, TRUE, TRUE),
    (4, 'Sarung Tinju Boxing Gloves', 'sarung-tinju-boxing', 'Sarung tinju profesional dengan padding tebal, wrist support, dan bahan kulit sintetis berkualitas tinggi.', 450000, 320000, 25, TRUE, FALSE),
    (4, 'Papan Skateboard Complete', 'skateboard-complete', 'Skateboard lengkap dengan deck maple 7-ply, trucks aluminum, dan wheels PU 99A. Ready to ride.', 850000, 600000, 20, TRUE, TRUE),
    (4, 'Helm Sepeda MTB Specialized', 'helm-sepeda-mtb', 'Helm sepeda mountain bike dengan ventilasi optimal, adjustable fit, dan sertifikasi safety internasional.', 650000, 450000, 35, TRUE, FALSE),
    (4, 'Kaos Olahraga Dri-FIT', 'kaos-olahraga-drifit', 'Kaos olahraga dengan teknologi Dri-FIT yang menyerap keringat dan cepat kering. Tersedia berbagai ukuran.', 185000, 125000, 80, TRUE, FALSE),
    (4, 'Botol Minum Olahraga 1L', 'botol-minum-olahraga', 'Botol minum olahraga dengan kapasitas 1L, BPA-free, dan dilengkapi sport cap untuk kemudahan minum saat berolahraga.', 125000, 85000, 100, TRUE, TRUE),
    (4, 'Celana Training Adidas', 'celana-training-adidas', 'Celana training dengan bahan polyester yang nyaman, elastic waistband, dan side pockets. Perfect untuk workout.', 320000, 220000, 50, TRUE, FALSE),
    (4, 'Stopwatch Digital Profesional', 'stopwatch-digital-pro', 'Stopwatch digital dengan akurasi tinggi, memory untuk 100 lap times, dan tahan air. Cocok untuk pelatih.', 285000, 200000, 40, TRUE, FALSE);

-- Additional Medicine Products (category_id = 3)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (3, 'Masker Medis 3-Ply Box 50pcs', 'masker-medis-3ply-50', 'Masker medis 3-ply dengan filter efficiency tinggi, earloop elastis, dan nose clip adjustable. Box isi 50pcs.', 75000, 50000, 200, TRUE, TRUE),
    (3, 'Hand Sanitizer Gel 500ml', 'hand-sanitizer-500ml', 'Hand sanitizer gel dengan kandungan alkohol 70%, moisturizer, dan aroma fresh. Efektif membunuh kuman.', 45000, 30000, 150, TRUE, TRUE),
    (3, 'Vitamin D3 1000 IU', 'vitamin-d3-1000iu', 'Suplemen Vitamin D3 1000 IU untuk kesehatan tulang dan sistem imun. Isi 60 softgel capsules.', 85000, 58000, 100, TRUE, FALSE),
    (3, 'Obat Batuk Herbal Sirup', 'obat-batuk-herbal', 'Obat batuk herbal dari ekstrak jahe, madu, dan jeruk nipis. Aman untuk dewasa dan anak di atas 6 tahun.', 35000, 22000, 120, TRUE, TRUE),
    (3, 'Plester Luka Waterproof', 'plester-luka-waterproof', 'Plester luka tahan air dengan adhesive kuat dan breathable material. Box isi 20 pieces berbagai ukuran.', 25000, 15000, 180, TRUE, FALSE),
    (3, 'Minyak Kayu Putih 60ml', 'minyak-kayu-putih-60ml', 'Minyak kayu putih murni untuk meredakan masuk angin, pegal-pegal, dan gigitan serangga. Aroma segar dan hangat.', 18000, 12000, 200, TRUE, FALSE),
    (3, 'Suplemen Omega 3 Fish Oil', 'omega-3-fish-oil', 'Suplemen Omega 3 dari minyak ikan untuk kesehatan jantung dan otak. Isi 60 softgel capsules.', 125000, 85000, 80, TRUE, TRUE),
    (3, 'Salep Anti Gatal 15g', 'salep-anti-gatal', 'Salep untuk mengatasi gatal-gatal akibat alergi, gigitan serangga, atau iritasi kulit. Formula non-greasy.', 28000, 18000, 100, TRUE, FALSE),
    (3, 'Tetes Mata Refresh 15ml', 'tetes-mata-refresh', 'Tetes mata untuk mengatasi mata kering dan lelah akibat penggunaan gadget. Formula gentle dan sterile.', 42000, 28000, 90, TRUE, FALSE),
    (3, 'Koyo Pereda Nyeri Otot', 'koyo-pereda-nyeri', 'Koyo dengan formula hangat untuk meredakan nyeri otot dan sendi. Efek tahan hingga 8 jam. Isi 5 lembar.', 32000, 20000, 110, TRUE, TRUE);

-- Additional Milk Products (category_id = 2)
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (2, 'Susu Almond Unsweetened 1L', 'susu-almond-unsweetened', 'Susu almond tanpa pemanis tambahan, kaya vitamin E dan rendah kalori. Cocok untuk diet dan vegan.', 45000, 32000, 80, TRUE, TRUE),
    (2, 'Greek Yogurt Strawberry 150g', 'greek-yogurt-strawberry', 'Greek yogurt rasa strawberry dengan protein tinggi dan probiotik untuk kesehatan pencernaan.', 18000, 12000, 100, TRUE, FALSE),
    (2, 'Keju Mozzarella Shredded 200g', 'keju-mozzarella-shredded', 'Keju mozzarella parut siap pakai untuk pizza, pasta, dan berbagai masakan Italia.', 42000, 28000, 60, TRUE, TRUE),
    (2, 'Susu Kambing Murni 500ml', 'susu-kambing-murni', 'Susu kambing murni yang mudah dicerna, kaya nutrisi, dan cocok untuk yang alergi susu sapi.', 35000, 24000, 40, TRUE, FALSE),
    (2, 'Butter Unsalted 227g', 'butter-unsalted-227g', 'Butter tawar premium untuk baking dan cooking, tekstur creamy dan rasa yang rich.', 55000, 38000, 50, TRUE, TRUE),
    (2, 'Whipping Cream 200ml', 'whipping-cream-200ml', 'Whipping cream untuk membuat kue dan dessert, mudah dikocok dan hasil yang stabil.', 28000, 18000, 70, TRUE, FALSE),
    (2, 'Susu Oat Barista 1L', 'susu-oat-barista', 'Susu oat khusus untuk barista yang bisa di-steam dan foam, perfect untuk latte art.', 52000, 36000, 45, TRUE, TRUE),
    (2, 'Cottage Cheese 200g', 'cottage-cheese-200g', 'Cottage cheese rendah lemak dengan protein tinggi, cocok untuk diet dan healthy lifestyle.', 38000, 25000, 35, TRUE, FALSE),
    (2, 'Susu Cokelat Organik 250ml', 'susu-cokelat-organik', 'Susu cokelat organik tanpa pestisida dan bahan kimia berbahaya, rasa cokelat yang natural.', 15000, 10000, 90, TRUE, FALSE),
    (2, 'Parmesan Cheese Grated 100g', 'parmesan-grated-100g', 'Keju parmesan parut premium dengan rasa yang kuat dan aroma yang khas untuk pasta dan risotto.', 65000, 45000, 30, TRUE, TRUE);

-- Final Additional Products for Various Categories
INSERT INTO products (category_id, NAME, slug, description, price, cost_price, stock, is_active, is_featured) VALUES
    (6, 'Earbuds Wireless TWS', 'earbuds-wireless-tws', 'Earbuds wireless dengan teknologi TWS, noise cancelling, dan battery life hingga 24 jam dengan charging case.', 850000, 600000, 40, TRUE, TRUE),
    (7, 'Tas Ransel Laptop 15 inch', 'tas-ransel-laptop-15', 'Tas ransel dengan kompartemen laptop 15 inch, anti theft zipper, dan bahan water resistant.', 285000, 200000, 55, TRUE, FALSE),
    (8, 'Coffee Maker French Press', 'coffee-maker-french-press', 'French press coffee maker dengan kapasitas 1L, borosilicate glass, dan stainless steel filter.', 185000, 130000, 35, TRUE, TRUE),
    (9, 'Set Alat Tulis Sekolah', 'set-alat-tulis-sekolah', 'Set alat tulis lengkap untuk sekolah berisi pensil, pulpen, penghapus, penggaris, dan tempat pensil.', 75000, 50000, 100, TRUE, FALSE),
    (10, 'Mainan Edukasi Alphabet', 'mainan-edukasi-alphabet', 'Mainan edukasi untuk belajar alphabet dengan suara dan lampu, cocok untuk anak usia 2-5 tahun.', 165000, 115000, 45, TRUE, TRUE),
    (1, 'Sunscreen SPF 50 PA+++', 'sunscreen-spf50-pa', 'Sunscreen dengan perlindungan tinggi SPF 50 PA+++, water resistant, dan tidak meninggalkan white cast.', 125000, 85000, 80, TRUE, TRUE),
    (4, 'Sepatu Lari Adidas Ultraboost', 'sepatu-lari-adidas-ultra', 'Sepatu lari dengan teknologi Boost untuk energy return maksimal dan kenyamanan sepanjang hari.', 2200000, 1700000, 20, TRUE, TRUE),
    (5, 'Paket Sayur Organik Mix', 'paket-sayur-organik-mix', 'Paket sayuran organik campuran berisi bayam, kangkung, sawi, dan tomat. Fresh dari kebun organik.', 45000, 30000, 60, TRUE, TRUE),
    (3, 'Termometer Infrared Non-Contact', 'termometer-infrared', 'Termometer infrared tanpa kontak dengan akurasi tinggi dan hasil pengukuran dalam 1 detik.', 285000, 200000, 25, TRUE, TRUE),
    (2, 'Es Krim Vanilla Premium 1L', 'es-krim-vanilla-1l', 'Es krim vanilla premium dengan bahan susu murni dan vanilla bean asli. Tekstur creamy dan rasa yang rich.', 85000, 58000, 30, TRUE, FALSE);

-- Create event to automatically clean up expired reservations
CREATE EVENT IF NOT EXISTS CleanupExpiredReservations
ON SCHEDULE EVERY 5 MINUTE
DO
BEGIN
    -- Mark expired reservations
    UPDATE stock_reservations
    SET status = 'expired'
    WHERE status = 'active' AND expires_at < NOW();

    -- Update reserved_stock for affected products
    UPDATE products p
    SET reserved_stock = (
        SELECT COALESCE(SUM(sr.quantity), 0)
        FROM stock_reservations sr
        WHERE sr.product_id = p.product_id
        AND sr.status = 'active'
        AND sr.expires_at > NOW()
    );

    -- Delete old expired reservations (older than 1 day)
    DELETE FROM stock_reservations
    WHERE status IN ('expired', 'confirmed', 'cancelled')
    AND reserved_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
END;

-- Update some existing products to be featured
UPDATE products SET is_featured = TRUE WHERE product_id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 15, 20, 25, 30, 35, 40);