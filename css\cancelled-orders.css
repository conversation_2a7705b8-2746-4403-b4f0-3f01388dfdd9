/**
 * Cancelled Orders Styling
 * Special styles for cancelled orders to make them visually distinct
 */

/* Cancelled order card styling */
.cancelled-order-card {
    position: relative;
    opacity: 0.85;
    filter: grayscale(20%);
    transition: all 0.3s ease;
    overflow: hidden;
}

.cancelled-order-card:hover {
    opacity: 1;
    filter: grayscale(0%);
    transform: translateY(-2px);
}

/* Cancelled ribbon overlay */
.cancelled-ribbon {
    position: absolute;
    top: 15px;
    right: -35px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 5px 40px;
    font-size: 12px;
    font-weight: bold;
    transform: rotate(45deg);
    z-index: 10;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Cancelled badge animation */
.cancelled-badge {
    animation: pulse-danger 2s infinite;
    position: relative;
}

@keyframes pulse-danger {
    0% { 
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
        transform: scale(1);
    }
    70% { 
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
        transform: scale(1.05);
    }
    100% { 
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
        transform: scale(1);
    }
}

/* Watermark effect for cancelled orders */
.cancelled-watermark {
    position: relative;
    overflow: hidden;
}

.cancelled-watermark::after {
    content: 'DIBATALKAN';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 1.8rem;
    font-weight: bold;
    color: rgba(220, 53, 69, 0.08);
    z-index: 0;
    pointer-events: none;
    text-shadow: 0 0 10px rgba(220, 53, 69, 0.1);
}

/* Cancelled order header styling */
.cancelled-order-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    color: white;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.cancelled-order-header h4 {
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.cancelled-order-header p {
    margin-bottom: 0;
    opacity: 0.9;
}

/* Background colors for cancelled elements */
.bg-danger-subtle {
    background-color: #f8d7da !important;
}

.bg-cancelled {
    background-color: #f8d7da !important;
}

.text-cancelled {
    color: #721c24 !important;
}

/* Strikethrough effect for cancelled items */
.cancelled-strikethrough {
    text-decoration: line-through;
    opacity: 0.7;
}

/* Cancelled item overlay effect */
.cancelled-item-overlay {
    position: relative;
}

.cancelled-item-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(220, 53, 69, 0.1);
    border-radius: 5px;
    z-index: 1;
}

/* Cancelled product image overlay */
.cancelled-product-image {
    position: relative;
    opacity: 0.5;
}

.cancelled-product-image::after {
    content: '✕';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    color: #dc3545;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
    z-index: 2;
}

/* Cancelled table styling */
.table-cancelled {
    opacity: 0.8;
}

.table-cancelled thead {
    background-color: #f8d7da;
    color: #721c24;
}

.table-cancelled tbody tr {
    background-color: rgba(248, 215, 218, 0.3);
}

.table-cancelled .text-decoration-line-through {
    opacity: 0.7;
}

/* Cancelled alert styling */
.alert-cancelled {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 1px solid #f5c6cb;
    color: #721c24;
    border-radius: 10px;
}

.alert-cancelled .alert-heading {
    color: #721c24;
    font-weight: bold;
}

/* Cancelled progress track */
.progress-track.cancelled-order {
    opacity: 0.8;
}

.progress-track.cancelled-order .progress-step.cancelled {
    color: #dc3545;
    font-weight: bold;
}

.progress-track.cancelled-order .progress-step.cancelled .step-icon {
    color: #dc3545;
    font-size: 1.5rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.progress-track.cancelled-order .progress-step.cancelled .step-info h5 {
    color: #dc3545;
    font-weight: bold;
}

/* Cancelled button styling */
.btn-cancelled {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-cancelled:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: white;
}

/* Cancelled status badge */
.status-badge.cancelled {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    animation: pulse-danger 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .cancelled-ribbon {
        font-size: 10px;
        padding: 3px 30px;
        right: -30px;
    }
    
    .cancelled-watermark::after {
        font-size: 1.2rem;
    }
    
    .cancelled-order-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }
}

/* Print styles - hide cancelled styling when printing */
@media print {
    .cancelled-ribbon,
    .cancelled-watermark::after,
    .cancelled-badge {
        display: none !important;
    }
    
    .cancelled-order-card {
        opacity: 1 !important;
        filter: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .cancelled-watermark::after {
        color: rgba(220, 53, 69, 0.15);
    }
    
    .bg-danger-subtle {
        background-color: rgba(220, 53, 69, 0.2) !important;
    }
    
    .alert-cancelled {
        background: rgba(220, 53, 69, 0.2);
        border-color: rgba(220, 53, 69, 0.3);
        color: #f8d7da;
    }
}

/* Accessibility improvements */
.cancelled-order-card:focus-within {
    outline: 2px solid #dc3545;
    outline-offset: 2px;
}

.cancelled-ribbon:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

/* Animation for newly cancelled orders */
.newly-cancelled {
    animation: cancelledOrderAppear 0.8s ease-out;
}

@keyframes cancelledOrderAppear {
    0% {
        opacity: 1;
        filter: grayscale(0%);
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        filter: grayscale(50%);
        transform: scale(0.98);
    }
    100% {
        opacity: 0.85;
        filter: grayscale(20%);
        transform: scale(1);
    }
}
