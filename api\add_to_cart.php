<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
require_once '../config/database.php';

// Get POST data
$data = json_decode(file_get_contents('php://input'), true);
$productId = $data['product_id'] ?? null;
$quantity = $data['quantity'] ?? 1;
$userId = $data['user_id'] ?? null; // In real app, get this from session

// Validate input
if (!$productId || !$userId) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => 'Product ID and User ID are required'
    ]);
    exit;
}

try {
    $conn = getConnection();
    
    // Start transaction
    $conn->beginTransaction();
    
    // Check product stock and status
    $stmt = $conn->prepare("
        SELECT product_id, stock, price, is_active 
        FROM products 
        WHERE product_id = ? AND is_active = 1
    ");
    $stmt->execute([$productId]);
    $product = $stmt->fetch();
    
    if (!$product) {
        throw new Exception('Product not found or inactive');
    }
    
    if ($product['stock'] < $quantity) {
        throw new Exception('Insufficient stock');
    }
    
    // Get or create cart for user
    $stmt = $conn->prepare("
        SELECT cart_id FROM carts 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$userId]);
    $cart = $stmt->fetch();
    
    if (!$cart) {
        // Create new cart
        $stmt = $conn->prepare("
            INSERT INTO carts (user_id) 
            VALUES (?)
        ");
        $stmt->execute([$userId]);
        $cartId = $conn->lastInsertId();
    } else {
        $cartId = $cart['cart_id'];
    }
    
    // Check if product already in cart
    $stmt = $conn->prepare("
        SELECT cart_item_id, quantity 
        FROM cart_items 
        WHERE cart_id = ? AND product_id = ?
    ");
    $stmt->execute([$cartId, $productId]);
    $cartItem = $stmt->fetch();
    
    if ($cartItem) {
        // Update existing cart item
        $newQuantity = $cartItem['quantity'] + $quantity;
        if ($newQuantity > $product['stock']) {
            throw new Exception('Cannot add more items than available in stock');
        }
        
        $stmt = $conn->prepare("
            UPDATE cart_items 
            SET quantity = ? 
            WHERE cart_item_id = ?
        ");
        $stmt->execute([$newQuantity, $cartItem['cart_item_id']]);
    } else {
        // Add new cart item
        $stmt = $conn->prepare("
            INSERT INTO cart_items (cart_id, product_id, quantity) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$cartId, $productId, $quantity]);
    }
    
    // Get updated cart count
    $stmt = $conn->prepare("
        SELECT SUM(quantity) as cart_count 
        FROM cart_items ci 
        JOIN carts c ON ci.cart_id = c.cart_id 
        WHERE c.user_id = ?
    ");
    $stmt->execute([$userId]);
    $result = $stmt->fetch();
    $cartCount = (int)($result['cart_count'] ?? 0);
    
    // Commit transaction
    $conn->commit();
    
    // Return success response
    echo json_encode([
        'status' => 'success',
        'message' => 'Product added to cart',
        'cart_count' => $cartCount
    ]);
    
} catch(Exception $e) {
    // Rollback transaction on error
    if ($conn && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 