<?php
header('Content-Type: application/json');
require_once '../config/database.php';

try {
    $conn = getConnection();
    
    // Get product ID from query parameter if provided
    $productId = isset($_GET['product_id']) ? (int)$_GET['product_id'] : null;
    
    if ($productId) {
        // Get modal for specific product
        $stmt = $conn->prepare("
            SELECT pm.*, p.name as product_name, p.image as product_image 
            FROM product_modals pm
            JOIN products p ON pm.product_id = p.id
            WHERE pm.product_id = ? AND pm.is_active = true
        ");
        $stmt->execute([$productId]);
    } else {
        // Get all active modals
        $stmt = $conn->prepare("
            SELECT pm.*, p.name as product_name, p.image as product_image 
            FROM product_modals pm
            JOIN products p ON pm.product_id = p.id
            WHERE pm.is_active = true
        ");
        $stmt->execute();
    }
    
    $modals = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get base URL for images
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $baseUrl = $protocol . $host . '/tewuneed';
    
    // Format image URLs
    foreach ($modals as &$modal) {
        if (!empty($modal['product_image'])) {
            $modal['product_image'] = $baseUrl . '/' . $modal['product_image'];
        }
    }
    
    // Return success response
    echo json_encode([
        'status' => 'success',
        'data' => $modals
    ]);
    
} catch(Exception $e) {
    // Return error response
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
} 