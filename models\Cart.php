<?php
require_once __DIR__ . '/../config/database.php';

class Cart {
    private $db;

    public function __construct() {
        $this->db = getConnection();
    }

    public function getCart($user_id) {
        try {
            // Get or create cart
            $cart = $this->getOrCreateCart($user_id);
            if (!$cart) {
                return false;
            }

            // Get cart items with product details
            $sql = "SELECT ci.*, p.name, p.price, p.image, p.stock,
                          (ci.quantity * p.price) as subtotal
                   FROM cart_items ci
                   JOIN products p ON ci.product_id = p.product_id
                   WHERE ci.cart_id = :cart_id";
            
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':cart_id', $cart['cart_id'], PDO::PARAM_INT);
            $stmt->execute();
            
            $cart['items'] = $stmt->fetchAll();
            
            // Calculate total
            $cart['total'] = array_sum(array_column($cart['items'], 'subtotal'));
            
            return $cart;
        } catch (PDOException $e) {
            error_log("Error in getCart: " . $e->getMessage());
            return false;
        }
    }

    private function getOrCreateCart($user_id) {
        try {
            // Check if user has an existing cart
            $sql = "SELECT * FROM carts WHERE user_id = :user_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $cart = $stmt->fetch();
            
            if (!$cart) {
                // Create new cart
                $sql = "INSERT INTO carts (user_id) VALUES (:user_id)";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
                $stmt->execute();
                
                // Get the created cart
                return $this->getOrCreateCart($user_id);
            }
            
            return $cart;
        } catch (PDOException $e) {
            error_log("Error in getOrCreateCart: " . $e->getMessage());
            return false;
        }
    }

    public function addItem($user_id, $product_id, $quantity = 1) {
        try {
            $this->db->beginTransaction();
            
            // Get or create cart
            $cart = $this->getOrCreateCart($user_id);
            if (!$cart) {
                throw new Exception("Could not create cart");
            }
            
            // Check if product exists and has enough stock
            $sql = "SELECT stock FROM products WHERE product_id = :product_id AND is_active = TRUE";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $product = $stmt->fetch();
            if (!$product || $product['stock'] < $quantity) {
                throw new Exception("Product not available or insufficient stock");
            }
            
            // Check if item already exists in cart
            $sql = "SELECT * FROM cart_items WHERE cart_id = :cart_id AND product_id = :product_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':cart_id', $cart['cart_id'], PDO::PARAM_INT);
            $stmt->bindParam(':product_id', $product_id, PDO::PARAM_INT);
            $stmt->execute();
            
            $cartItem = $stmt->fetch();
            
            if ($cartItem) {
                // Update quantity
                $newQuantity = $cartItem['quantity'] + $quantity;
                if ($newQuantity > $product['stock']) {
                    throw new Exception("Cannot add more items than available in stock");
                }
                
                $sql = "UPDATE cart_items 
                        SET quantity = :quantity 
                        WHERE cart_item_id = :cart_item_id";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    ':quantity' => $newQuantity,
                    ':cart_item_id' => $cartItem['cart_item_id']
                ]);
            } else {
                // Add new item
                $sql = "INSERT INTO cart_items (cart_id, product_id, quantity) 
                        VALUES (:cart_id, :product_id, :quantity)";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    ':cart_id' => $cart['cart_id'],
                    ':product_id' => $product_id,
                    ':quantity' => $quantity
                ]);
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log("Error in addItem: " . $e->getMessage());
            return false;
        }
    }

    public function updateItemQuantity($user_id, $cart_item_id, $quantity) {
        try {
            $this->db->beginTransaction();
            
            // Get cart
            $cart = $this->getCart($user_id);
            if (!$cart) {
                throw new Exception("Cart not found");
            }
            
            // Check if item exists and belongs to user's cart
            $sql = "SELECT ci.*, p.stock 
                    FROM cart_items ci
                    JOIN products p ON ci.product_id = p.product_id
                    WHERE ci.cart_item_id = :cart_item_id 
                    AND ci.cart_id = :cart_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':cart_item_id', $cart_item_id, PDO::PARAM_INT);
            $stmt->bindParam(':cart_id', $cart['cart_id'], PDO::PARAM_INT);
            $stmt->execute();
            
            $item = $stmt->fetch();
            if (!$item) {
                throw new Exception("Cart item not found");
            }
            
            // Check stock
            if ($quantity > $item['stock']) {
                throw new Exception("Cannot add more items than available in stock");
            }
            
            if ($quantity <= 0) {
                // Remove item
                $sql = "DELETE FROM cart_items WHERE cart_item_id = :cart_item_id";
                $stmt = $this->db->prepare($sql);
                $stmt->bindParam(':cart_item_id', $cart_item_id, PDO::PARAM_INT);
                $stmt->execute();
            } else {
                // Update quantity
                $sql = "UPDATE cart_items 
                        SET quantity = :quantity 
                        WHERE cart_item_id = :cart_item_id";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    ':quantity' => $quantity,
                    ':cart_item_id' => $cart_item_id
                ]);
            }
            
            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log("Error in updateItemQuantity: " . $e->getMessage());
            return false;
        }
    }

    public function removeItem($user_id, $cart_item_id) {
        try {
            // Get cart
            $cart = $this->getCart($user_id);
            if (!$cart) {
                return false;
            }
            
            // Remove item
            $sql = "DELETE FROM cart_items 
                    WHERE cart_item_id = :cart_item_id 
                    AND cart_id = :cart_id";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                ':cart_item_id' => $cart_item_id,
                ':cart_id' => $cart['cart_id']
            ]);
            
            return true;
        } catch (PDOException $e) {
            error_log("Error in removeItem: " . $e->getMessage());
            return false;
        }
    }

    public function clearCart($user_id) {
        try {
            // Get cart
            $cart = $this->getCart($user_id);
            if (!$cart) {
                return false;
            }
            
            // Remove all items
            $sql = "DELETE FROM cart_items WHERE cart_id = :cart_id";
            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':cart_id', $cart['cart_id'], PDO::PARAM_INT);
            $stmt->execute();
            
            return true;
        } catch (PDOException $e) {
            error_log("Error in clearCart: " . $e->getMessage());
            return false;
        }
    }
}
?> 