/**
 * Global JavaScript Error Handler
 * Prevents JavaScript errors from breaking form submissions
 */

(function() {
    'use strict';
    
    // Global error handler
    window.addEventListener('error', function(e) {
        console.warn('JavaScript Error caught and handled:', {
            message: e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno,
            error: e.error
        });
        
        // Don't let errors break the page
        e.preventDefault();
        return true;
    });
    
    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(e) {
        console.warn('Unhandled Promise Rejection caught:', e.reason);
        e.preventDefault();
        return true;
    });
    
    // Provide fallbacks for missing functions
    window.addEventListener('DOMContentLoaded', function() {
        
        // Fallback for missing RealtimeClient
        if (typeof RealtimeClient === 'undefined') {
            window.RealtimeClient = function() {
                return {
                    on: function() {},
                    emit: function() {},
                    connect: function() {},
                    disconnect: function() {}
                };
            };
            console.log('RealtimeClient fallback created');
        }
        
        // Fallback for missing Firebase Firestore
        if (typeof firebase !== 'undefined' && firebase.firestore === undefined) {
            firebase.firestore = function() {
                console.warn('Firebase Firestore not available - using fallback');
                return {
                    collection: function() {
                        return {
                            doc: function() {
                                return {
                                    set: function() { return Promise.resolve(); },
                                    get: function() { return Promise.resolve({exists: false}); },
                                    update: function() { return Promise.resolve(); },
                                    delete: function() { return Promise.resolve(); }
                                };
                            },
                            add: function() { return Promise.resolve(); },
                            where: function() { return this; },
                            orderBy: function() { return this; },
                            limit: function() { return this; },
                            get: function() { return Promise.resolve({docs: []}); }
                        };
                    }
                };
            };
        }
        
        // Ensure form submissions work even with JavaScript errors
        const forms = document.querySelectorAll('form');
        forms.forEach(function(form) {
            // Add backup submit handler
            form.addEventListener('submit', function(e) {
                console.log('Form submit event detected for:', form.id || 'unnamed form');
                
                // If this is the checkout form, ensure it submits
                if (form.id === 'checkout-form') {
                    console.log('Checkout form submission - ensuring it works');
                    
                    // Check if payment method is selected
                    const paymentMethod = form.querySelector('input[name="payment_method"]:checked') || 
                                        form.querySelector('select[name="payment_method"]');
                    
                    if (!paymentMethod || !paymentMethod.value) {
                        // Try to auto-select first available payment method
                        const firstPayment = form.querySelector('input[name="payment_method"]') ||
                                           form.querySelector('select[name="payment_method"] option[value]');
                        
                        if (firstPayment) {
                            if (firstPayment.type === 'radio') {
                                firstPayment.checked = true;
                            } else if (firstPayment.tagName === 'OPTION') {
                                firstPayment.selected = true;
                                firstPayment.parentElement.value = firstPayment.value;
                            }
                            console.log('Auto-selected payment method:', firstPayment.value);
                        } else {
                            console.error('No payment method available to auto-select');
                            alert('Please select a payment method');
                            e.preventDefault();
                            return false;
                        }
                    }
                    
                    // Ensure form has action
                    if (!form.action || form.action.endsWith('#')) {
                        form.action = 'checkout.php';
                        console.log('Set form action to checkout.php');
                    }
                    
                    // Ensure form method is POST
                    if (form.method.toLowerCase() !== 'post') {
                        form.method = 'POST';
                        console.log('Set form method to POST');
                    }
                }
            });
        });
        
        console.log('Error handler and form fallbacks initialized');
    });
    
    // Override console.error to prevent error spam
    const originalConsoleError = console.error;
    console.error = function(...args) {
        // Filter out known non-critical errors
        const message = args.join(' ');
        
        if (message.includes('realtime_client.js') ||
            message.includes('firebase.firestore is not a function') ||
            message.includes('RealtimeClient is not defined')) {
            // Convert to warning for known issues
            console.warn('Known issue (non-critical):', ...args);
            return;
        }
        
        // Call original console.error for other errors
        originalConsoleError.apply(console, args);
    };
    
    console.log('Global error handler loaded');
})();
