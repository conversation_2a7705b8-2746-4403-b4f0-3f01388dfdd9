<?php
// Start session
session_start();

// Set content type to JSON
header('Content-Type: application/json');

// Include Firebase authentication helper and functions
require_once '../includes/firebase_auth.php';
require_once '../includes/functions.php';
require_once '../includes/db_connect.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
if (!isset($input['uid']) || !isset($input['email'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

try {
    // First, check if firebase_user_id column exists in users table
    $stmt = $conn->query("SHOW COLUMNS FROM users LIKE 'firebase_user_id'");
    $hasFirebaseColumn = $stmt->rowCount() > 0;

    if (!$hasFirebaseColumn) {
        // Add firebase_user_id column if it doesn't exist
        try {
            $conn->exec("ALTER TABLE users ADD COLUMN firebase_user_id VARCHAR(255) NULL UNIQUE AFTER user_id");
            error_log("Added firebase_user_id column to users table");
        } catch (PDOException $e) {
            error_log("Error adding firebase_user_id column: " . $e->getMessage());
            throw new Exception('Database schema update failed');
        }
    }

    // Make sure required columns allow NULL for Firebase users
    try {
        $conn->exec("ALTER TABLE users MODIFY COLUMN username VARCHAR(50) NULL");
        $conn->exec("ALTER TABLE users MODIFY COLUMN password VARCHAR(255) NULL");
        $conn->exec("ALTER TABLE users MODIFY COLUMN full_name VARCHAR(100) NULL");
    } catch (PDOException $e) {
        // Log but don't fail - columns might already be nullable
        error_log("Column modification warning: " . $e->getMessage());
    }

    // Sync Firebase user with local database
    $local_user_id = syncFirebaseUserToDatabase(
        $input['uid'],
        $input['email'],
        $input['displayName'] ?? null,
        $input['photoURL'] ?? null
    );

    if (!$local_user_id) {
        throw new Exception('Failed to sync user with database');
    }

    // Create Firebase session
    $_SESSION['firebase_user_id'] = $input['uid'];
    $_SESSION['user_id'] = $local_user_id; // Use local user ID for compatibility
    $_SESSION['local_user_id'] = $local_user_id;
    $_SESSION['user_name'] = $input['displayName'] ?? 'User';
    $_SESSION['full_name'] = $input['displayName'] ?? 'User';
    $_SESSION['username'] = $input['displayName'] ?? 'User';
    $_SESSION['user_email'] = $input['email'];
    $_SESSION['email'] = $input['email'];
    $_SESSION['user_photo'] = $input['photoURL'] ?? '';
    $_SESSION['role'] = $input['role'] ?? 'customer';
    $_SESSION['admin_logged_in'] = ($input['role'] === 'admin');
    $_SESSION['last_activity'] = time();
    $_SESSION['last_regeneration'] = time();

    // Regenerate session ID for security
    session_regenerate_id(true);

    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'user' => [
            'uid' => $input['uid'],
            'local_user_id' => $local_user_id,
            'email' => $input['email'],
            'displayName' => $input['displayName'] ?? 'User',
            'role' => $input['role'] ?? 'customer'
        ]
    ]);

} catch (Exception $e) {
    error_log("Firebase login error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Failed to create session. Please try again.',
        'debug' => $e->getMessage() // Include debug info for development
    ]);
}
?>
