<?php
// Start output buffering
ob_start();

$page = 'products';
$page_title = 'Edit Product';

// Pastikan session dimulai
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    $_SESSION['alert_type'] = 'danger';
    $_SESSION['alert_message'] = 'Anda harus login sebagai admin untuk mengakses halaman ini';
    header('Location: ../login.php');
    exit;
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['alert_message'] = 'No product ID specified';
    $_SESSION['alert_type'] = 'danger';
    header('Location: products.php');
    exit;
}

$product_id = $_GET['id'];

// Get product data
try {
    $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        $_SESSION['alert_message'] = 'Product not found';
        $_SESSION['alert_type'] = 'danger';
        header('Location: products.php');
        exit;
    }

    // Get all categories for dropdown
    $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $_SESSION['alert_message'] = 'Database error: ' . $e->getMessage();
    $_SESSION['alert_type'] = 'danger';
    header('Location: products.php');
    exit;
}

// Include header setelah semua redirect potensial
require_once 'includes/header.php';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate input
        $NAME = trim($_POST['NAME'] ?? '');
        $category_id = (int)($_POST['category_id'] ?? 0);
        $description = trim($_POST['description'] ?? '');
        $price = (float)($_POST['price'] ?? 0);
        $stock = (int)($_POST['stock'] ?? 0);
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        // Generate slug
        $slug = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', $NAME));

        // Validation
        $errors = [];
        if (empty($NAME)) $errors[] = "Product name is required";
        if ($category_id <= 0) $errors[] = "Category is required";
        if ($price <= 0) $errors[] = "Price must be greater than 0";
        if ($stock < 0) $errors[] = "Stock cannot be negative";

        if (empty($errors)) {
            // Begin transaction
            $conn->beginTransaction();

            // Handle image upload
            $image_name = $product['image']; // Keep existing image by default
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                $max_size = 2 * 1024 * 1024; // 2MB

                if (!in_array($_FILES['image']['type'], $allowed_types)) {
                    throw new Exception("Only JPG, PNG, and GIF files are allowed");
                }

                if ($_FILES['image']['size'] > $max_size) {
                    throw new Exception("File size is too large (maximum 2MB)");
                }

                // Create uploads directory if it doesn't exist
                $upload_dir = '../uploads/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                // Generate unique filename
                $image_name = time() . '_' . preg_replace('/[^a-zA-Z0-9]/', '_', $_FILES['image']['name']);
                $target_file = $upload_dir . $image_name;

                // Upload file
                if (!move_uploaded_file($_FILES['image']['tmp_name'], $target_file)) {
                    throw new Exception("Failed to upload image");
                }

                // Delete old image if exists
                if (!empty($product['image'])) {
                    $old_image = $upload_dir . $product['image'];
                    if (file_exists($old_image)) {
                        @unlink($old_image);
                    }
                }
            }

            // Update product in database
            $stmt = $conn->prepare("
                UPDATE products SET
                    category_id = ?,
                    NAME = ?,
                    slug = ?,
                    description = ?,
                    price = ?,
                    stock = ?,
                    image = ?,
                    is_active = ?
                WHERE product_id = ?
            ");

            $stmt->execute([
                $category_id,
                $NAME,
                $slug,
                $description,
                $price,
                $stock,
                $image_name,
                $is_active,
                $product_id
            ]);

            // Update product search index if exists
            try {
                $stmt = $conn->prepare("
                    UPDATE product_search ps
                    JOIN categories c ON c.category_id = ?
                    SET ps.name = ?,
                        ps.description = ?,
                        ps.category_name = c.name
                    WHERE ps.product_id = ?
                ");
                $stmt->execute([$category_id, $NAME, $description, $product_id]);
            } catch (PDOException $e) {
                // Table might not exist, continue
            }

            // Log inventory change if stock was updated
            if ($stock != $product['stock']) {
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO inventory_logs (
                            product_id, admin_id, old_stock, new_stock,
                            adjustment, notes
                        ) VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $product_id,
                        $_SESSION['user_id'] ?? null,
                        $product['stock'],
                        $stock,
                        $stock - $product['stock'],
                        'Updated from admin panel'
                    ]);
                } catch (PDOException $e) {
                    // Table might not exist, continue
                }
            }

            // Commit transaction
            $conn->commit();

            $_SESSION['alert_message'] = "Product updated successfully";
            $_SESSION['alert_type'] = 'success';

            // Redirect to product list
            header("Location: products.php");
            exit;

        } else {
            $errorMessage = implode('<br>', $errors);
        }
    } catch (PDOException $e) {
        $conn->rollBack();
        $errorMessage = "Database error: " . $e->getMessage();
    } catch (Exception $e) {
        $conn->rollBack();
        $errorMessage = $e->getMessage();
    }
}
?>

<!-- Form sudah dimuat dari header.php, tidak perlu deklarasi HTML lagi -->
<style>
    .required-field::after {
        content: " *";
        color: red;
    }
    .preview-image {
        max-width: 100%;
        max-height: 200px;
        margin-top: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 3px;
    }
    .product-section {
        background-color: #f9f9f9;
        border: 1px solid #e9e9e9;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .section-title {
        border-bottom: 2px solid #0d6efd;
        padding-bottom: 8px;
        margin-bottom: 15px;
        color: #0d6efd;
    }
    .form-control:focus, .form-select:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }
    .image-placeholder {
        width: 200px;
        height: 200px;
        border: 2px dashed #ccc;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #777;
        font-size: 14px;
    }
    .image-preview-container {
        position: relative;
        display: inline-block;
    }
    .remove-image {
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(255,0,0,0.7);
        color: white;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        text-align: center;
        line-height: 25px;
        cursor: pointer;
    }
    .card-header-tabs .nav-link.active {
        background-color: #fff;
        border-bottom-color: #fff;
        font-weight: bold;
    }
    .tab-content {
        padding: 20px;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.25rem 0.25rem;
    }
    .form-action-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 32px;
        flex-wrap: wrap;
    }
    .form-action-buttons .btn {
        min-width: 170px;
        font-size: 1rem;
        box-shadow: 0 2px 8px rgba(13,110,253,0.08);
        transition: box-shadow 0.2s, background 0.2s, color 0.2s;
    }
    .form-action-buttons .btn-primary:hover {
        background: #0b5ed7;
        box-shadow: 0 4px 16px rgba(13,110,253,0.18);
    }
    .form-action-buttons .btn-outline-info:hover {
        background: #0dcaf0;
        color: #fff;
        box-shadow: 0 4px 16px rgba(13,202,240,0.18);
    }
    @media (max-width: 600px) {
        .form-action-buttons {
            flex-direction: column;
            align-items: stretch;
        }
        .form-action-buttons .btn {
            min-width: unset;
            width: 100%;
        }
    }
</style>

<!-- Main Content -->
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12">
            <?php if (isset($errorMessage)): ?>
                <div class="alert alert-danger"><?php echo $errorMessage; ?></div>
            <?php elseif (isset($_SESSION['alert_message'])): ?>
                <div class="alert alert-success"><?php echo $_SESSION['alert_message']; ?></div>
            <?php endif; ?>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-edit me-2"></i> Edit Produk: <?php echo htmlspecialchars($product['NAME']); ?></h5>
                        <a href="products.php" class="btn btn-sm btn-light"><i class="fas fa-arrow-left me-1"></i> Kembali</a>
                    </div>
                </div>

                <div class="card-body">
                    <form action="" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <input type="hidden" name="product_id" value="<?php echo $product_id; ?>">
                        <div class="product-section">
                            <h5 class="section-title"><i class="fas fa-info-circle me-2"></i>Informasi Dasar Produk</h5>
                            <div class="row g-3">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="NAME" class="form-label required-field">Product Name</label>
                                        <input type="text" class="form-control" id="NAME" name="NAME" value="<?php echo htmlspecialchars($product['NAME']); ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control summernote" id="description" name="description" rows="5"><?php echo htmlspecialchars($product['description'] ?? ''); ?></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="price" class="form-label required-field">Price (Rp)</label>
                                                <input type="number" class="form-control" id="price" name="price" min="0" step="1000" value="<?php echo htmlspecialchars($product['price']); ?>" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="stock" class="form-label required-field">Stock</label>
                                                <input type="number" class="form-control" id="stock" name="stock" value="<?php echo htmlspecialchars($product['stock']); ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label required-field">Category</label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['category_id']; ?>" <?php echo ($product['category_id'] == $category['category_id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($category['NAME']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="image" class="form-label">Product Image</label>
                                        <?php if (!empty($product['image'])): ?>
                                            <div class="mb-2">
                                                <img src="<?php echo '../uploads/products/' . $product['image']; ?>" class="img-thumbnail preview-image" alt="<?php echo htmlspecialchars($product['NAME']); ?>">
                                            </div>
                                        <?php endif; ?>
                                        <input type="file" class="form-control" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                                        <div class="form-text">Leave empty to keep current image. Format: JPG, PNG, GIF. Max: 2MB.</div>
                                        <img id="imagePreview" class="preview-image img-thumbnail mt-2" style="display: none;">
                                    </div>
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo ($product['is_active'] ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_active">Active</label>
                                        <div class="form-text">If not checked, product will not be displayed in the store.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-action-buttons">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Simpan Perubahan
                            </button>
                            <a href="../product-detail.php?id=<?php echo $product_id; ?>" target="_blank" class="btn btn-outline-info">
                                <i class="fas fa-eye me-1"></i> Lihat di Halaman User
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
