            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="/tewuneed2/admin/status-dropdown.js"></script>
    <script>
        // Confirmation for delete actions
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('[data-confirm]');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (!confirm(this.getAttribute('data-confirm'))) {
                        e.preventDefault();
                    }
                });
            });
            
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-dismissible');
                alerts.forEach(alert => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
            
            // Enhance all status dropdown menus
            const statusButtons = document.querySelectorAll('.dropdown-toggle[data-bs-toggle="dropdown"]');
            statusButtons.forEach(button => {
                if (button.classList.contains('status-btn')) return; // Skip if already enhanced
                
                // Add status-btn class for identification
                button.classList.add('status-btn');
                
                // Find dropdown items and enhance them
                const dropdownMenu = button.nextElementSibling;
                if (dropdownMenu && dropdownMenu.classList.contains('dropdown-menu')) {
                    const items = dropdownMenu.querySelectorAll('.dropdown-item');
                    items.forEach(item => {
                        item.classList.add('status-dropdown-item');
                        
                        // Extract order ID and status from parent form
                        const form = item.closest('form');
                        if (form) {
                            const orderIdInput = form.querySelector('input[name="order_id"]');
                            const statusInput = form.querySelector('input[name="order_status"]');
                            
                            if (orderIdInput && statusInput) {
                                item.setAttribute('data-order-id', orderIdInput.value);
                                item.setAttribute('data-status', statusInput.value);
                            }
                        }
                    });
                }
            });
            
            // Handle status form submissions with visual feedback
            const statusForms = document.querySelectorAll('form.status-form');
            statusForms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';
                        submitBtn.disabled = true;
                    }
                });
            });
        });
    </script>
</body>
</html>
