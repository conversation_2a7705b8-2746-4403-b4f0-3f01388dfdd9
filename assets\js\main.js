// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add to cart functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.dataset.productId;
            addToCart(productId);
        });
    });

    // Quantity input handlers
    const quantityInputs = document.querySelectorAll('.quantity-input');
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            const productId = this.dataset.productId;
            updateCartItem(productId, this.value);
        });
    });

    // Remove from cart functionality
    const removeButtons = document.querySelectorAll('.remove-from-cart');
    removeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const productId = this.dataset.productId;
            removeFromCart(productId);
        });
    });

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Search functionality
    const searchInput = document.querySelector('#search-input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            const searchTerm = this.value;
            if (searchTerm.length >= 2) {
                performSearch(searchTerm);
            }
        }, 300));
    }
});

// Add to cart function
function addToCart(productId) {
    showSpinner();
    fetch('ajax/working_add_to_cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `product_id=${productId}&quantity=1`
    })
    .then(response => {
        return response.text().then(text => {
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error("Failed to parse JSON:", text);
                throw new Error("Server returned non-JSON response. Check console for details.");
            }
        });
    })
    .then(data => {
        hideSpinner();
        if (data.success) {
            // Show notification with product name and quantity
            const productName = data.data.product_name || 'Product';
            const quantity = data.data.quantity || 1;
            showAlert(`${quantity} ${productName} telah ditambahkan ke keranjang!`, 'success');
            // Update cart count
            updateCartCount(data.data.cart_count);
        } else {
            // Don't show error alerts - they're blocked by our error filter
            console.log('Add to cart failed:', data.message);
        }
    })
    .catch(error => {
        hideSpinner();
        showAlert(error.message || 'Error adding product to cart. Please try again.', 'danger');
        console.error('AJAX Error:', error);
    });
}

// Update cart item quantity
function updateCartItem(productId, quantity) {
    showSpinner();
    fetch('ajax/update_cart_item.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `product_id=${productId}&quantity=${quantity}`
    })
    .then(response => {
        return response.text().then(text => {
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error("Failed to parse JSON (updateCartItem):", text);
                throw new Error("Server returned non-JSON response (updateCartItem). Check console for details.");
            }
        });
    })
    .then(data => {
        hideSpinner();
        if (data.success) {
            showAlert('Cart updated successfully!', 'success');
            if (typeof updateCartTotal === 'function' && data.cart_total !== undefined) {
                updateCartTotal(data.cart_total);
            }
            if (typeof updateCartPage === 'function') {
                updateCartPage(data);
            }
        } else {
            showAlert(data.message || 'Error updating cart', 'danger');
        }
    })
    .catch(error => {
        hideSpinner();
        showAlert(error.message || 'Error updating cart. Please try again.', 'danger');
        console.error('AJAX Error (updateCartItem):', error);
    });
}

// Remove from cart function
function removeFromCart(productId) {
    showSpinner();
    fetch('ajax/remove_cart_item.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `product_id=${productId}`
    })
    .then(response => {
        return response.text().then(text => {
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error("Failed to parse JSON (removeFromCart):", text);
                throw new Error("Server returned non-JSON response (removeFromCart). Check console for details.");
            }
        });
    })
    .then(data => {
        hideSpinner();
        if (data.success) {
            const cartItem = document.querySelector(`.cart-item[data-product-id="${productId}"]`) ||
                             document.querySelector(`[data-product-id="${productId}"]`).closest('.cart-item');

            if (cartItem) {
                cartItem.remove();
            }
            if (data.cart_count !== undefined) {
                updateCartCount(data.cart_count);
            }
            if (data.cart_total !== undefined) {
                updateCartTotal(data.cart_total);
            }
            if (typeof updateCartPage === 'function') {
                updateCartPage(data);
            }
            showAlert('Product removed from cart', 'success');
        } else {
            showAlert(data.message || 'Error removing product from cart', 'danger');
        }
    })
    .catch(error => {
        hideSpinner();
        showAlert(error.message || 'Error removing product from cart. Please try again.', 'danger');
        console.error('AJAX Error (removeFromCart):', error);
    });
}

// Search function
function performSearch(searchTerm) {
    showSpinner();
    fetch(`ajax/search.php?q=${encodeURIComponent(searchTerm)}`)
        .then(response => {
            return response.text().then(text => {
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error("Failed to parse JSON (performSearch):", text);
                    throw new Error("Server returned non-JSON response (performSearch). Check console for details.");
                }
            });
        })
        .then(data => {
            hideSpinner();
            if (data.success) {
                updateSearchResults(data.results);
            } else {
                showAlert(data.message || 'Error performing search', 'danger');
            }
        })
        .catch(error => {
            hideSpinner();
            showAlert('Error performing search', 'danger');
            console.error('Error:', error);
        });
}

// Update search results
function updateSearchResults(results) {
    const resultsContainer = document.querySelector('#search-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
        if (results.length === 0) {
            resultsContainer.innerHTML = '<p class="text-center">No results found</p>';
            return;
        }
        results.forEach(product => {
            resultsContainer.innerHTML += `
                <div class="search-result-item">
                    <img src="${product.image_url}" alt="${product.name}">
                    <div class="search-result-content">
                        <h5>${product.name}</h5>
                        <p class="price">${product.price}</p>
                        <button class="btn btn-primary btn-sm add-to-cart" data-product-id="${product.id}">
                            Add to Cart
                        </button>
                    </div>
                </div>
            `;
        });
    }
}

// Update cart count
function updateCartCount(count) {
    const cartCount = document.querySelector('#cart-count');
    if (cartCount) {
        cartCount.textContent = count;
    }

    // Also update cart badge in navbar
    const cartBadge = document.querySelector('.cart-count');
    if (cartBadge) {
        cartBadge.textContent = count;
        cartBadge.style.display = count > 0 ? 'inline' : 'none';
    }
}

// Update cart count from server
function updateCartCountFromServer() {
    fetch('ajax/get_cart_count.php')
    .then(response => response.json())
    .then(data => {
        updateCartCount(data.cart_count || 0);
    })
    .catch(error => {
        console.error('Error updating cart count:', error);
    });
}

// Update cart total
function updateCartTotal(total) {
    const cartTotal = document.querySelector('#cart-total');
    if (cartTotal) {
        cartTotal.textContent = total;
    }
}

// Show alert message
function showAlert(message, type) {
    const alertContainer = document.querySelector('#alert-container');
    if (alertContainer) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        alertContainer.appendChild(alert);
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
}

// Show loading spinner
function showSpinner() {
    const spinner = document.querySelector('#spinner');
    if (spinner) {
        spinner.style.display = 'block';
    }
}

// Hide loading spinner
function hideSpinner() {
    const spinner = document.querySelector('#spinner');
    if (spinner) {
        spinner.style.display = 'none';
    }
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Image preview for file inputs
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.querySelector('#image-preview');
            if (preview) {
                preview.src = e.target.result;
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Password strength checker
function checkPasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (password.match(/[a-z]+/)) strength++;
    if (password.match(/[A-Z]+/)) strength++;
    if (password.match(/[0-9]+/)) strength++;
    if (password.match(/[^a-zA-Z0-9]+/)) strength++;
    return strength;
}

// Update password strength indicator
function updatePasswordStrength(password) {
    const strength = checkPasswordStrength(password);
    const indicator = document.querySelector('#password-strength');
    if (indicator) {
        indicator.className = 'password-strength';
        switch(strength) {
            case 0:
            case 1:
                indicator.classList.add('weak');
                indicator.textContent = 'Weak';
                break;
            case 2:
            case 3:
                indicator.classList.add('medium');
                indicator.textContent = 'Medium';
                break;
            case 4:
            case 5:
                indicator.classList.add('strong');
                indicator.textContent = 'Strong';
                break;
        }
    }
}