<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    
    // Check if file was uploaded
    if (!isset($_FILES['profile_picture']) || $_FILES['profile_picture']['error'] !== UPLOAD_ERR_OK) {
        echo json_encode(['success' => false, 'message' => 'No file uploaded or upload error']);
        exit;
    }
    
    $file = $_FILES['profile_picture'];
    
    // Validate file size (2MB max)
    $max_size = 2 * 1024 * 1024; // 2MB in bytes
    if ($file['size'] > $max_size) {
        echo json_encode(['success' => false, 'message' => 'File size must be less than 2MB']);
        exit;
    }
    
    // Validate file type
    $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    $file_type = mime_content_type($file['tmp_name']);
    
    if (!in_array($file_type, $allowed_types)) {
        echo json_encode(['success' => false, 'message' => 'Only JPG, PNG, and GIF files are allowed']);
        exit;
    }
    
    // Create uploads/profiles directory if it doesn't exist
    $upload_dir = '../uploads/profiles/';
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            echo json_encode(['success' => false, 'message' => 'Failed to create upload directory']);
            exit;
        }
    }
    
    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'profile_' . $user_id . '_' . time() . '.' . $file_extension;
    $file_path = $upload_dir . $filename;
    
    // Get current profile picture to delete later
    $stmt = $conn->prepare("SELECT profile_picture FROM users WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $current_user = $stmt->fetch(PDO::FETCH_ASSOC);
    $old_picture = $current_user['profile_picture'] ?? null;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file']);
        exit;
    }
    
    // Resize image if needed (optional)
    try {
        $image_info = getimagesize($file_path);
        if ($image_info !== false) {
            $width = $image_info[0];
            $height = $image_info[1];
            
            // Resize if image is larger than 500x500
            if ($width > 500 || $height > 500) {
                $new_size = 500;
                $ratio = min($new_size / $width, $new_size / $height);
                $new_width = intval($width * $ratio);
                $new_height = intval($height * $ratio);
                
                // Create new image resource
                switch ($file_type) {
                    case 'image/jpeg':
                    case 'image/jpg':
                        $source = imagecreatefromjpeg($file_path);
                        break;
                    case 'image/png':
                        $source = imagecreatefrompng($file_path);
                        break;
                    case 'image/gif':
                        $source = imagecreatefromgif($file_path);
                        break;
                    default:
                        $source = false;
                }
                
                if ($source !== false) {
                    $destination = imagecreatetruecolor($new_width, $new_height);
                    
                    // Preserve transparency for PNG and GIF
                    if ($file_type === 'image/png' || $file_type === 'image/gif') {
                        imagealphablending($destination, false);
                        imagesavealpha($destination, true);
                        $transparent = imagecolorallocatealpha($destination, 255, 255, 255, 127);
                        imagefilledrectangle($destination, 0, 0, $new_width, $new_height, $transparent);
                    }
                    
                    imagecopyresampled($destination, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
                    
                    // Save resized image
                    switch ($file_type) {
                        case 'image/jpeg':
                        case 'image/jpg':
                            imagejpeg($destination, $file_path, 90);
                            break;
                        case 'image/png':
                            imagepng($destination, $file_path, 9);
                            break;
                        case 'image/gif':
                            imagegif($destination, $file_path);
                            break;
                    }
                    
                    imagedestroy($source);
                    imagedestroy($destination);
                }
            }
        }
    } catch (Exception $e) {
        // If image processing fails, continue with original file
        error_log("Image processing error: " . $e->getMessage());
    }
    
    // Update database with new profile picture
    $stmt = $conn->prepare("
        UPDATE users 
        SET profile_picture = ?, updated_at = NOW()
        WHERE user_id = ?
    ");
    
    $result = $stmt->execute([$filename, $user_id]);
    
    if ($result) {
        // Delete old profile picture if it exists
        if ($old_picture && $old_picture !== $filename) {
            $old_file_path = $upload_dir . $old_picture;
            if (file_exists($old_file_path)) {
                unlink($old_file_path);
            }
        }
        
        // Log activity
        $activity_stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, description, created_at) 
            VALUES (?, 'profile_photo_update', 'Profile photo updated', NOW())
        ");
        $activity_stmt->execute([$user_id]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Profile photo updated successfully',
            'filename' => $filename,
            'url' => 'uploads/profiles/' . $filename
        ]);
    } else {
        // Delete uploaded file if database update failed
        if (file_exists($file_path)) {
            unlink($file_path);
        }
        echo json_encode(['success' => false, 'message' => 'Failed to update profile photo in database']);
    }
    
} catch (Exception $e) {
    error_log("Profile photo upload error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while uploading profile photo']);
}
?>
