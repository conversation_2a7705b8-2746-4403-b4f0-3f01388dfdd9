<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    
    // Check if file was uploaded
    if (!isset($_FILES['profile_picture']) || $_FILES['profile_picture']['error'] !== UPLOAD_ERR_OK) {
        $error_message = 'No file uploaded';
        if (isset($_FILES['profile_picture'])) {
            switch ($_FILES['profile_picture']['error']) {
                case UPLOAD_ERR_INI_SIZE:
                case UPLOAD_ERR_FORM_SIZE:
                    $error_message = 'File is too large';
                    break;
                case UPLOAD_ERR_PARTIAL:
                    $error_message = 'File was only partially uploaded';
                    break;
                case UPLOAD_ERR_NO_FILE:
                    $error_message = 'No file was uploaded';
                    break;
                default:
                    $error_message = 'Unknown upload error';
            }
        }
        echo json_encode(['success' => false, 'message' => $error_message]);
        exit;
    }
    
    $file = $_FILES['profile_picture'];
    
    // Validate file type
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $file_type = mime_content_type($file['tmp_name']);
    
    if (!in_array($file_type, $allowed_types)) {
        echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG and GIF are allowed']);
        exit;
    }
    
    // Validate file size (max 2MB)
    $max_size = 2 * 1024 * 1024; // 2MB in bytes
    if ($file['size'] > $max_size) {
        echo json_encode(['success' => false, 'message' => 'File is too large. Maximum size is 2MB']);
        exit;
    }
    
    // Create upload directory if it doesn't exist
    $upload_dir = '../uploads/profiles/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Generate unique filename
    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'profile_' . $user_id . '_' . time() . '.' . $file_extension;
    $target_path = $upload_dir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $target_path)) {
        // Get current profile picture
        $stmt = $conn->prepare("SELECT profile_picture FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        $old_picture = $user['profile_picture'] ?? '';
        
        // Update profile picture in database
        $stmt = $conn->prepare("UPDATE users SET profile_picture = ?, updated_at = NOW() WHERE user_id = ?");
        $result = $stmt->execute([$filename, $user_id]);
        
        if ($result) {
            // Delete old profile picture if it exists
            if (!empty($old_picture) && $old_picture != $filename && file_exists($upload_dir . $old_picture)) {
                unlink($upload_dir . $old_picture);
            }
            
            // Update session data
            $_SESSION['user_photo'] = $filename;
            
            // Log activity
            $activity_stmt = $conn->prepare("
                INSERT INTO user_activities (user_id, activity_type, description, created_at) 
                VALUES (?, 'profile_update', 'Updated profile picture', NOW())
            ");
            $activity_stmt->execute([$user_id]);
            
            echo json_encode([
                'success' => true, 
                'message' => 'Profile picture updated successfully',
                'filename' => $filename
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update profile picture in database']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to upload file']);
    }
    
} catch (Exception $e) {
    error_log("Upload photo error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while uploading photo']);
}
?>