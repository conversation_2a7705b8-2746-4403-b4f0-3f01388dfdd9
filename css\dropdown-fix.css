/* 
 * DROPDOWN Z-INDEX FIX
 * Ensures dropdown menu is always visible above all other elements
 * Applied globally to all pages
 */

/* NAVBAR AND DROPDOWN Z-INDEX FIXES */
.navbar {
    z-index: 99999 !important;
    position: relative !important;
}

.navbar-nav {
    z-index: 99999 !important;
    position: relative !important;
}

.dropdown {
    z-index: 99999 !important;
    position: relative !important;
}

.dropdown-toggle {
    z-index: 99999 !important;
    position: relative !important;
    cursor: pointer !important;
    user-select: none !important;
}

.dropdown-menu {
    z-index: 99999 !important;
    position: absolute !important;
    display: none !important;
    top: 100% !important;
    left: auto !important;
    right: 0 !important;
    min-width: 200px !important;
    background: white !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15) !important;
    padding: 0.5rem !important;
    margin-top: 10px !important;
}

.dropdown-menu.show {
    display: block !important;
    z-index: 99999 !important;
}

/* USER DROPDOWN SPECIFIC */
#userDropdown {
    z-index: 99999 !important;
    position: relative !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* ENSURE OTHER ELEMENTS DON'T INTERFERE */
.hero-section {
    z-index: 1 !important;
}

.hero-content {
    z-index: 2 !important;
}

.floating-elements {
    z-index: 1 !important;
}

/* CAROUSEL AND BANNER FIXES */
.carousel {
    z-index: 1 !important;
}

.carousel-inner {
    z-index: 1 !important;
}

.carousel-item {
    z-index: 1 !important;
}

/* MODAL FIXES - Should be below dropdown */
.modal {
    z-index: 9998 !important;
}

.modal-backdrop {
    z-index: 9997 !important;
}

/* ALERT FIXES - Should be below dropdown */
.alert {
    z-index: 9996 !important;
}

#alert-container {
    z-index: 9996 !important;
}

/* SPINNER FIXES */
#spinner {
    z-index: 9995 !important;
}

/* ENSURE DROPDOWN WORKS ON MOBILE */
@media (max-width: 768px) {
    .navbar {
        z-index: 99999 !important;
    }
    
    .dropdown-menu {
        z-index: 99999 !important;
        position: absolute !important;
        right: 0 !important;
        left: auto !important;
        min-width: 180px !important;
    }
    
    .navbar-collapse {
        z-index: 99999 !important;
        position: relative !important;
    }
}

/* DEBUGGING - Remove in production */
.dropdown-debug {
    border: 2px solid red !important;
    background-color: rgba(255,0,0,0.1) !important;
}

/* HOVER EFFECTS */
.dropdown-toggle:hover {
    background-color: rgba(255,255,255,0.1) !important;
}

.dropdown-item:hover {
    background-color: rgba(0,0,0,0.05) !important;
}

/* ENSURE DROPDOWN ARROW WORKS */
.dropdown-toggle::after {
    z-index: 99999 !important;
    position: relative !important;
}

/* FIX FOR STICKY NAVBAR */
.navbar.sticky-top {
    z-index: 99999 !important;
}

.navbar.fixed-top {
    z-index: 99999 !important;
}

/* ENSURE DROPDOWN MENU POSITIONING */
.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* BACKDROP FILTER SUPPORT */
.dropdown-menu {
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

/* ANIMATION SUPPORT */
.dropdown-menu {
    transition: opacity 0.15s linear !important;
}

.dropdown-menu.show {
    opacity: 1 !important;
}

/* ENSURE DROPDOWN WORKS WITH BOOTSTRAP */
.dropdown-menu[data-bs-popper] {
    z-index: 99999 !important;
}

/* FORCE VISIBILITY */
.dropdown.show .dropdown-menu {
    display: block !important;
    z-index: 99999 !important;
}

/* PREVENT OVERFLOW ISSUES */
.navbar-nav .dropdown {
    position: static !important;
}

@media (min-width: 992px) {
    .navbar-nav .dropdown {
        position: relative !important;
    }
}

/* ENSURE DROPDOWN BUTTON IS CLICKABLE */
.navbar .dropdown-toggle {
    background: transparent !important;
    border: none !important;
}

.navbar .dropdown-toggle:focus {
    box-shadow: none !important;
    outline: none !important;
}

/* FINAL OVERRIDE FOR STUBBORN ELEMENTS */
.dropdown-menu {
    z-index: 2147483647 !important; /* Maximum z-index value */
}

.dropdown {
    z-index: 2147483647 !important;
}

#userDropdown {
    z-index: 2147483647 !important;
}

.navbar {
    z-index: 2147483646 !important;
}
