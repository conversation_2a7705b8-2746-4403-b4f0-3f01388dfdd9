<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/order_status_functions.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

try {
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    $last_check = isset($input['last_check']) ? (int)$input['last_check'] : 0;
    $user_id = (int)$_SESSION['user_id'];
    
    // Convert timestamp to MySQL datetime
    $last_check_date = date('Y-m-d H:i:s', $last_check / 1000);
    
    $updates = [];
    
    // First, check for real-time sync updates
    $real_time_updates = getPendingRealTimeSyncUpdates($conn, $user_id, $last_check_date);
    $processed_sync_ids = [];
    
    foreach ($real_time_updates as $sync_update) {
        $statuses = getOrderStatuses();
        $new_status_info = $statuses[$sync_update['new_status']] ?? ['label' => ucfirst($sync_update['new_status']), 'logo' => '📦'];
        $old_status_info = $statuses[$sync_update['old_status']] ?? ['label' => ucfirst($sync_update['old_status']), 'logo' => '⏰'];
        
        $updates[] = [
            'order_id' => $sync_update['order_id'],
            'order_number' => '#' . $sync_update['order_id'],
            'new_status' => $sync_update['new_status'],
            'new_status_label' => $new_status_info['label'],
            'new_status_logo' => $new_status_info['logo'],
            'old_status' => $sync_update['old_status'],
            'old_status_label' => $old_status_info['label'],
            'old_status_logo' => $old_status_info['logo'],
            'admin_note' => $sync_update['admin_note'],
            'tracking_number' => $sync_update['tracking_number'],
            'estimated_delivery' => $sync_update['estimated_delivery'],
            'updated_by_type' => $sync_update['updated_by_type'],
            'timestamp' => strtotime($sync_update['sync_timestamp']) * 1000,
            'source' => 'real_time_sync'
        ];
        
        $processed_sync_ids[] = $sync_update['sync_id'];
    }
    
    // Mark real-time sync updates as processed
    if (!empty($processed_sync_ids)) {
        markRealTimeSyncAsProcessed($conn, $processed_sync_ids);
    }
    
    // Check for deleted orders by comparing current orders with localStorage
    // This helps detect orders that were deleted by admin
    $current_orders_stmt = $conn->prepare("
        SELECT order_id, order_number
        FROM orders
        WHERE user_id = ?
    ");
    $current_orders_stmt->execute([$user_id]);
    $current_orders = $current_orders_stmt->fetchAll(PDO::FETCH_ASSOC);
    $current_order_ids = array_column($current_orders, 'order_id');

    // Store current order count for deletion detection
    $current_count = count($current_orders);
    $previous_count = $_SESSION['user_order_count'] ?? $current_count;
    $_SESSION['user_order_count'] = $current_count;

    // If count decreased, some orders were deleted
    if ($current_count < $previous_count) {
        $deleted_count = $previous_count - $current_count;
        $updates[] = [
            'action' => 'orders_deleted',
            'deleted_count' => $deleted_count,
            'message' => "Some of your orders have been cancelled and removed by administrator",
            'timestamp' => time() * 1000,
            'source' => 'deletion_detection'
        ];
    }

    // Also check for regular order updates
    $stmt = $conn->prepare("
        SELECT SQL_NO_CACHE
            o.order_id,
            o.order_number,
            COALESCE(o.current_status, o.order_status, o.status) as order_status,
            o.updated_at,
            o.tracking_number,
            COALESCE(o.order_number, CONCAT('#', o.order_id)) as display_number
        FROM orders o
        WHERE o.user_id = ?
        AND (o.updated_at > ? OR o.created_at > ?)
        ORDER BY o.updated_at DESC
    ");

    $stmt->execute([$user_id, $last_check_date, $last_check_date]);
    $updated_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($updated_orders as $order) {
        // Skip if we already have this update from real-time sync
        $already_synced = false;
        foreach ($updates as $existing_update) {
            if ($existing_update['order_id'] == $order['order_id'] && $existing_update['source'] === 'real_time_sync') {
                $already_synced = true;
                break;
            }
        }
        
        if ($already_synced) continue;
        
        // Check if this order status was recently updated by admin
        $status_check_stmt = $conn->prepare("
            SELECT 
                osh.new_status,
                osh.old_status,
                osh.notes,
                osh.admin_id,
                osh.created_at,
                osh.status_logo,
                u.username as admin_name
            FROM order_status_history osh
            LEFT JOIN users u ON osh.admin_id = u.user_id
            WHERE osh.order_id = ?
            AND osh.created_at > ?
            ORDER BY osh.created_at DESC
            LIMIT 1
        ");
        
        $status_check_stmt->execute([$order['order_id'], $last_check_date]);
        $status_history = $status_check_stmt->fetch(PDO::FETCH_ASSOC);
        
        // If there's a recent status change by admin, include it
        if ($status_history || strtotime($order['updated_at']) > ($last_check / 1000)) {
            $statuses = getOrderStatuses();
            $current_status = $order['order_status'];
            $status_info = $statuses[$current_status] ?? ['label' => ucfirst($current_status), 'logo' => '📦'];
            
            $old_status = $status_history['old_status'] ?? 'pending';
            $old_status_info = $statuses[$old_status] ?? ['label' => ucfirst($old_status), 'logo' => '⏰'];
            
            $updates[] = [
                'order_id' => $order['order_id'],
                'order_number' => $order['display_number'],
                'new_status' => $current_status,
                'new_status_label' => $status_info['label'],
                'new_status_logo' => $status_info['logo'],
                'old_status' => $old_status,
                'old_status_label' => $old_status_info['label'],
                'old_status_logo' => $old_status_info['logo'],
                'admin_note' => $status_history['notes'] ?? '',
                'admin_name' => $status_history['admin_name'] ?? 'System',
                'tracking_number' => $order['tracking_number'],
                'updated_at' => $order['updated_at'],
                'timestamp' => strtotime($order['updated_at']) * 1000,
                'source' => 'database_update'
            ];
        }
    }
    
    echo json_encode([
        'success' => true,
        'updates' => $updates,
        'has_updates' => count($updates) > 0,
        'last_check_time' => time() * 1000,
        'debug' => [
            'user_id' => $user_id,
            'last_check_date' => $last_check_date,
            'real_time_updates' => count($real_time_updates),
            'database_updates' => count($updated_orders),
            'total_updates' => count($updates)
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
