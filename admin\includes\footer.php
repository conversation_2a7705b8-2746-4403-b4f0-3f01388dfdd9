        </div>
    </div>

    <!-- jQuery (required for some admin functions) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap Bundle with <PERSON><PERSON> -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Toastify for notifications -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <!-- Admin Functions (simplified) -->
    <script src="js/admin-functions.js"></script>

    <!-- Admin Core JavaScript -->
    <script>
        // Initialize admin functionality
        $(document).ready(function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            // Confirm delete actions
            $('.btn-delete').on('click', function(e) {
                if (!confirm('Apakah Anda yakin ingin menghapus item ini?')) {
                    e.preventDefault();
                }
            });

            // Loading state for forms
            $('form').on('submit', function() {
                $(this).find('button[type="submit"]').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Processing...');
            });

            // Enable DataTables if loaded
            if ($.fn.DataTable) {
                $('.datatable').DataTable({
                    responsive: true,
                    pageLength: 25,
                    language: {
                        search: "_INPUT_",
                        searchPlaceholder: "Cari...",
                        lengthMenu: "Tampilkan _MENU_ data per halaman",
                        info: "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                        infoEmpty: "Tidak ada data",
                        infoFiltered: "(difilter dari _MAX_ total data)",
                        paginate: {
                            first: "Pertama",
                            last: "Terakhir",
                            next: "Selanjutnya",
                            previous: "Sebelumnya"
                        }
                    }
                });
            }
        });

        // Global notification function
        function showNotification(message, type = 'success') {
            Toastify({
                text: message,
                duration: 3000,
                gravity: "top",
                position: "right",
                style: {
                    background: type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'
                },
                stopOnFocus: true
            }).showToast();
        }

        // Global AJAX error handler
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            console.error('AJAX Error:', thrownError);
            showNotification('Terjadi kesalahan. Silakan coba lagi.', 'error');
        });

        // Real-time status sync initialization
        if (typeof OrderStatusSync !== 'undefined') {
            window.orderStatusSync = new OrderStatusSync();
        }
    </script>

    <!-- Real-time Communication Widget -->
    <?php include 'includes/realtime_widget.php'; ?>
</body>
</html>
