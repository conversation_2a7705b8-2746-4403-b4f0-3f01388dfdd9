<?php
/**
 * Admin Index - Main Entry Point for Admin Area
 * This file serves as the main entry point for the admin area and redirects to the dashboard
 */

// Ensure proper session handling
session_start();

// Include necessary files
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Check if admin authentication file exists and include it
if (file_exists('../includes/admin_auth.php')) {
    require_once '../includes/admin_auth.php';
} elseif (file_exists('auth.php')) {
    // Use the local auth file if the includes version doesn't exist
    require_once 'auth.php';
}

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    // Not logged in as admin, redirect to admin login page
    header('Location: admin_login.php');
    exit;
}

// Admin is authenticated, redirect to dashboard
header('Location: dashboard.php');
exit;

try {
    $conn = getDbConnection();
    
    // Total Products
    $stmt = $conn->query("SELECT COUNT(*) FROM products");
    $total_products = $stmt->fetchColumn();
    
    // Total Categories
    $stmt = $conn->query("SELECT COUNT(*) FROM categories");
    $total_categories = $stmt->fetchColumn();
    
    // Total Orders
    $stmt = $conn->query("SELECT COUNT(*) FROM orders");
    $total_orders = $stmt->fetchColumn();
    
    // Total Users
    // Check if role column exists
    $roleColumnExists = $conn->query("SHOW COLUMNS FROM users LIKE 'role'")->rowCount() > 0;
    if ($roleColumnExists) {
        $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'user'");
    } else {
        $stmt = $conn->query("SELECT COUNT(*) FROM users");
    }
    $total_users = $stmt->fetchColumn();
    
    // Recent Orders (ambil 5 pesanan terbaru)
    $recent_orders = getRecentOrders(5);
    
    // Low Stock Products (less than 10 items)
    $stmt = $conn->query("
        SELECT * FROM products 
        WHERE stock < 10 
        ORDER BY stock ASC 
        LIMIT 5
    ");
    $low_stock_products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

// Include header
require_once '../includes/admin_header.php';
?>

<!-- Content Row -->
<div class="row mb-4">
    <!-- Total Products Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">PRODUCTS</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_products; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Categories Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">CATEGORIES</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_categories; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-folder fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Orders Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">ORDERS</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_orders; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Users Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">USERS</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_users; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Recent Orders -->
    <div class="col-xl-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">Recent Orders</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_orders)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Customer</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_orders as $order): ?>
                            <tr>
                                <td>
                                    <a href="order_management.php?id=<?php echo $order['order_id']; ?>">
                                        #<?php echo $order['order_id']; ?>
                                    </a>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($order['customer_name'] ?? 'Guest'); ?>
                                </td>
                                <td>
                                    <?php echo 'Rp ' . number_format($order['total_amount'], 0, ',', '.'); ?>
                                </td>
                                <td>
                                    <?php 
                                    $status = $order['order_status'] ?? 'dibuat';
                                    $badge_class = getOrderStatusBadgeClass($status);
                                    $icon_class = getOrderStatusIcon($status);
                                    $status_display = getOrderStatusLabel($status);
                                    ?>
                                    <span class="badge bg-<?php echo $badge_class; ?>">
                                        <i class="fas <?php echo $icon_class; ?> me-1"></i> <?php echo $status_display; ?>
                                    </span>
                                </td>
                                <td>
                                    <?php 
                                    // Gunakan created_at secara default
                                    $date_field = $order['order_date'] ?? $order['created_at'] ?? null;
                                    echo $date_field ? date('d M Y', strtotime($date_field)) : 'N/A'; 
                                    ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <p class="text-center">No orders found</p>
                <?php endif; ?>
                <div class="text-center mt-3">
                    <a href="order_management.php" class="btn btn-primary btn-sm">View All Orders</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Low Stock Products -->
    <div class="col-xl-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold">Low Stock Products</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($low_stock_products)): ?>
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <!-- <tbody> -->
                            <?php foreach ($low_stock_products as $product): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($product['NAME']); ?></td>
                                <td><?php echo 'Rp ' . number_format($product['price'], 0, ',', '.'); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $product['stock'] < 5 ? 'danger' : 'warning'; ?>">
                                        <?php echo $product['stock']; ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="products.php" onclick="window.location.href='edit-product.php?id=<?php echo $product['product_id']; ?>'; return false;" class="btn btn-primary btn-sm edit-button">Edit</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <p class="text-center">No low stock products found</p>
                <?php endif; ?>
                <div class="text-center mt-3">
                    <a href="products.php" class="btn btn-primary btn-sm" onclick="event.preventDefault(); window.location.href='products.php';">View All Products</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../includes/admin_footer.php'; ?>
