<?php
header('Content-Type: application/json');
require_once '../config/database.php';
require_once '../includes/StockReservation.php';

session_start();

$action = $_POST['action'] ?? $_GET['action'] ?? '';

switch($action) {
    case 'check':
        // Check stock availability
        $productId = $_GET['product_id'] ?? 0;
        $quantity = $_GET['quantity'] ?? 1;

        $stmt = $pdo->prepare("SELECT stock FROM products WHERE product_id = ?");
        $stmt->execute([$productId]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($product) {
            echo json_encode([
                'success' => true,
                'available' => $product['stock'] >= $quantity,
                'current_stock' => $product['stock']
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Product not found'
            ]);
        }
        break;

    case 'reserve':
        // Reserve stock for cart
        $productId = $_POST['product_id'] ?? 0;
        $quantity = $_POST['quantity'] ?? 1;
        $userId = $_SESSION['user_id'] ?? null;
        $sessionId = session_id();

        try {
            $stockReservation = new StockReservation($conn);
            $result = $stockReservation->reserveStock($productId, $quantity, $userId, $sessionId);

            if ($result['success']) {
                // Get available stock after reservation
                $availableStock = $stockReservation->getAvailableStock($productId);
                echo json_encode([
                    'success' => true,
                    'message' => $result['message'],
                    'available_stock' => $availableStock
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => $result['message']
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error reserving stock: ' . $e->getMessage()
            ]);
        }
        break;

    case 'update':
        // Update stock (decrease)
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            exit;
        }

        $productId = $_POST['product_id'] ?? 0;
        $quantity = $_POST['quantity'] ?? 1;
        
        try {
            // Start transaction
            $pdo->beginTransaction();
            
            // Check current stock
            $stmt = $pdo->prepare("SELECT stock FROM products WHERE id = ? FOR UPDATE");
            $stmt->execute([$productId]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$product) {
                throw new Exception('Product not found');
            }
            
            if ($product['stock'] < $quantity) {
                throw new Exception('Insufficient stock');
            }
            
            // Update stock
            $stmt = $pdo->prepare("UPDATE products SET stock = stock - ? WHERE id = ?");
            $stmt->execute([$quantity, $productId]);
            
            // Get updated stock
            $stmt = $pdo->prepare("SELECT stock FROM products WHERE id = ?");
            $stmt->execute([$productId]);
            $updatedProduct = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $pdo->commit();
            
            echo json_encode([
                'success' => true,
                'message' => 'Stock updated successfully',
                'current_stock' => $updatedProduct['stock']
            ]);
        } catch (Exception $e) {
            $pdo->rollBack();
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        break;

    case 'restore':
        // Restore stock (increase) - used when removing items from cart
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            exit;
        }

        $productId = $_POST['product_id'] ?? 0;
        $quantity = $_POST['quantity'] ?? 1;
        
        try {
            $pdo->beginTransaction();
            
            $stmt = $pdo->prepare("UPDATE products SET stock = stock + ? WHERE id = ?");
            $stmt->execute([$quantity, $productId]);
            
            // Get updated stock
            $stmt = $pdo->prepare("SELECT stock FROM products WHERE id = ?");
            $stmt->execute([$productId]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $pdo->commit();
            
            echo json_encode([
                'success' => true,
                'message' => 'Stock restored successfully',
                'current_stock' => $product['stock']
            ]);
        } catch (Exception $e) {
            $pdo->rollBack();
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        break;

    default:
        echo json_encode([
            'success' => false,
            'message' => 'Invalid action'
        ]);
}
?> 