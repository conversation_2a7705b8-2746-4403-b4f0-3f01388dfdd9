
<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/firebase_auth.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors in JSON response

// Check if user is logged in
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    // Get user information from session
    $user_email = $_SESSION['user_email'] ?? $_SESSION['email'] ?? '';
    $firebase_uid = $_SESSION['firebase_user_id'] ?? '';
    $user_id = $_SESSION['user_id'] ?? $_SESSION['local_user_id'] ?? null;

    // Debug logging
    error_log("Profile Update Debug - user_email: " . $user_email);
    error_log("Profile Update Debug - firebase_uid: " . $firebase_uid);
    error_log("Profile Update Debug - user_id: " . $user_id);

    // Find user in database
    $db_user_id = null;

    // Try to find user by email first
    if ($user_email) {
        $stmt = $conn->prepare("SELECT user_id FROM users WHERE email = ?");
        $stmt->execute([$user_email]);
        $user_data = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($user_data) {
            $db_user_id = $user_data['user_id'];
        }
    }

    // If not found by email, try by user_id
    if (!$db_user_id && $user_id) {
        $stmt = $conn->prepare("SELECT user_id FROM users WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $user_data = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($user_data) {
            $db_user_id = $user_data['user_id'];
        }
    }

    // If still not found, try Firebase sync
    if (!$db_user_id && $firebase_uid && $user_email) {
        $db_user_id = syncFirebaseUserToDatabase(
            $firebase_uid,
            $user_email,
            $_SESSION['user_name'] ?? $_SESSION['full_name'] ?? 'User',
            $_SESSION['user_photo'] ?? ''
        );
    }

    // If still no user found, return error
    if (!$db_user_id) {
        echo json_encode(['success' => false, 'message' => 'User not found. Please login again.', 'debug' => [
            'user_email' => $user_email,
            'firebase_uid' => $firebase_uid,
            'user_id' => $user_id
        ]]);
        exit;
    }

    // Get form data
    $full_name = trim($_POST['full_name'] ?? '');
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $date_of_birth = $_POST['date_of_birth'] ?? null;
    $gender = $_POST['gender'] ?? null;
    $bio = trim($_POST['bio'] ?? '');

    // Debug log
    error_log("Update Profile Debug - db_user_id: " . $db_user_id);
    error_log("Update Profile Debug - form data: " . print_r($_POST, true));

    // Basic validation
    if (empty($full_name)) {
        echo json_encode(['success' => false, 'message' => 'Full name is required']);
        exit;
    }

    if (empty($username)) {
        $username = strtolower(str_replace(' ', '_', $full_name));
    }

    if (empty($email)) {
        echo json_encode(['success' => false, 'message' => 'Email is required']);
        exit;
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid email format']);
        exit;
    }

    // Validate date of birth if provided
    if (!empty($date_of_birth)) {
        $date = DateTime::createFromFormat('Y-m-d', $date_of_birth);
        if (!$date || $date->format('Y-m-d') !== $date_of_birth) {
            echo json_encode(['success' => false, 'message' => 'Invalid date of birth format']);
            exit;
        }
    } else {
        $date_of_birth = null;
    }

    // Validate gender if provided
    if (!empty($gender) && !in_array($gender, ['male', 'female', 'other'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid gender value']);
        exit;
    }
    
    // Try to update existing user record
    $stmt = $conn->prepare("
        UPDATE users
        SET full_name = ?, username = ?, email = ?, phone_number = ?,
            date_of_birth = ?, gender = ?, bio = ?, updated_at = NOW()
        WHERE user_id = ?
    ");

    $update_params = [
        $full_name,
        $username,
        $email,
        $phone ?: null,
        $date_of_birth,
        $gender ?: null,
        $bio ?: null,
        $db_user_id
    ];

    error_log("Executing UPDATE with params: " . print_r($update_params, true));

    if ($stmt->execute($update_params)) {
        $rowCount = $stmt->rowCount();
        error_log("Rows updated: " . $rowCount);

        // Update session data
        $_SESSION['full_name'] = $full_name;
        $_SESSION['user_name'] = $full_name;
        $_SESSION['username'] = $username;
        $_SESSION['email'] = $email;
        $_SESSION['user_email'] = $email;

        echo json_encode([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => [
                'full_name' => $full_name,
                'username' => $username,
                'email' => $email,
                'phone_number' => $phone,
                'date_of_birth' => $date_of_birth,
                'gender' => $gender,
                'bio' => $bio
            ]
        ]);
    } else {
        $errorInfo = $stmt->errorInfo();
        error_log("Database update failed: " . print_r($errorInfo, true));
        echo json_encode(['success' => false, 'message' => 'Failed to update profile: ' . $errorInfo[2]]);
    }
    
} catch (Exception $e) {
    error_log("Profile update error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    // Handle specific duplicate entry errors
    if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
        if (strpos($e->getMessage(), 'username') !== false) {
            echo json_encode(['success' => false, 'message' => 'Username is already taken. Please choose a different username.']);
        } elseif (strpos($e->getMessage(), 'email') !== false) {
            echo json_encode(['success' => false, 'message' => 'Email is already registered. Please use a different email.']);
        } else {
            echo json_encode(['success' => false, 'message' => 'This information is already registered. Please check your data.']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'An error occurred while updating profile. Please try again.']);
    }
}







