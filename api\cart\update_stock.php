<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

$response = ['success' => false, 'message' => '', 'stock' => 0];

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['product_id']) || !isset($input['quantity'])) {
        throw new Exception('Missing required parameters');
    }
    
    $product_id = (int)$input['product_id'];
    $quantity = (int)$input['quantity'];
    
    if ($quantity <= 0) {
        throw new Exception('Invalid quantity');
    }
    
    $conn = getConnection();
    
    // Start transaction
    $conn->beginTransaction();
    
    try {
        // Get current stock with row lock
        $stmt = $conn->prepare("SELECT stock FROM products WHERE product_id = :product_id FOR UPDATE");
        $stmt->execute(['product_id' => $product_id]);
        $result = $stmt->fetch();
        
        if (!$result) {
            throw new Exception('Product not found');
        }
        
        $currentStock = (int)$result['stock'];
        
        if ($currentStock < $quantity) {
            throw new Exception('Insufficient stock');
        }
        
        // Update stock
        $stmt = $conn->prepare("UPDATE products SET stock = stock - :quantity WHERE product_id = :product_id");
        $success = $stmt->execute([
            'product_id' => $product_id,
            'quantity' => $quantity
        ]);
        
        if (!$success) {
            throw new Exception('Failed to update stock');
        }
        
        // Get new stock value
        $stmt = $conn->prepare("SELECT stock FROM products WHERE product_id = :product_id");
        $stmt->execute(['product_id' => $product_id]);
        $newStock = $stmt->fetch()['stock'];
        
        // Commit transaction
        $conn->commit();
        
        $response = [
            'success' => true,
            'message' => 'Stock updated successfully',
            'stock' => (int)$newStock
        ];
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
