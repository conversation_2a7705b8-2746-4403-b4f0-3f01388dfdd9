// Bootstrap Tab Initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tabs
    const triggerTabList = [].slice.call(document.querySelectorAll('#profileTabs a'));
    triggerTabList.forEach(function(triggerEl) {
        const tabTrigger = new bootstrap.Tab(triggerEl);
        
        triggerEl.addEventListener('click', function(event) {
            event.preventDefault();
            tabTrigger.show();
            
            // Update URL hash without scrolling
            const tabId = this.getAttribute('href').substring(1);
            history.replaceState(null, null, '#' + tabId);
            
            // Load data for specific tabs when they're shown
            if (tabId === 'addresses') {
                loadAddresses();
            }
        });
    });
    
    // Initialize tab from URL hash
    const hash = window.location.hash.substring(1);
    if (hash && document.getElementById(hash)) {
        const tab = new bootstrap.Tab(document.querySelector(`#profileTabs a[href="#${hash}"]`));
        tab.show();
        
        // Load data for specific tabs
        if (hash === 'addresses') {
            loadAddresses();
        }
    }
    
    // Setup event listeners
    setupProfileForm();
    setupPasswordForm();
    setupPreferencesForm();
    setupPhotoUpload();
    
    // Store original form values for cancel functionality
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
        profileForm.querySelectorAll('input, textarea, select').forEach(input => {
            input.setAttribute('data-original', input.value);
        });
    }
});

// Enhanced Message Display Function
// ... existing code ...

// Enhanced showMessage function with better animations and icons
function showMessage(message, type = 'success', duration = 5000) {
    // Create message container if it doesn't exist
    let messageContainer = document.getElementById('message-container');
    if (!messageContainer) {
        messageContainer = document.createElement('div');
        messageContainer.id = 'message-container';
        messageContainer.className = 'position-fixed top-0 end-0 p-3';
        messageContainer.style.zIndex = '9999';
        document.body.appendChild(messageContainer);
    }
    
    // Create alert element with enhanced animation
    const alertId = 'alert-' + Date.now();
    const alertElement = document.createElement('div');
    alertElement.id = alertId;
    alertElement.className = `alert alert-${type} alert-dismissible fade show animate__animated animate__fadeInRight shadow-lg`;
    alertElement.role = 'alert';
    
    // Add icon based on message type
    let icon = '';
    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle me-2"></i>';
            break;
        case 'danger':
            icon = '<i class="fas fa-exclamation-circle me-2"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle me-2"></i>';
            break;
        case 'info':
            icon = '<i class="fas fa-info-circle me-2"></i>';
            break;
        default:
            icon = '<i class="fas fa-bell me-2"></i>';
    }
    
    // Set alert content with improved styling
    alertElement.innerHTML = `
        <div class="d-flex align-items-center">
            ${icon}
            <div>${message}</div>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // Add alert to container
    messageContainer.appendChild(alertElement);
    
    // Auto-dismiss after duration with enhanced animation
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            // Add exit animation
            alert.classList.remove('animate__fadeInRight');
            alert.classList.add('animate__fadeOutRight');
            
            // Remove after animation completes
            setTimeout(() => {
                if (alert && alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 500);
        }
    }, duration);
    
    // Return the alert element for further manipulation if needed
    return alertElement;
}

function showProfileMessage(message, type = 'success') {
    const messageContainer = document.getElementById('profile-message-container');
    if (!messageContainer) return;
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    messageContainer.innerHTML = '';
    messageContainer.appendChild(alertDiv);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => alertDiv.remove(), 300);
    }, 5000);
}

// Profile Form Functions
function setupProfileForm() {
    const profileForm = document.getElementById('profileForm');
    if (!profileForm) return;
    
    const formFields = profileForm.querySelectorAll('.form-control');
    const editBtn = document.getElementById('editProfileBtn');
    const cancelBtn = document.getElementById('cancelProfileBtn');
    const saveBtn = document.getElementById('saveProfileBtn');
    
    // Store original values
    formFields.forEach(field => {
        field.dataset.original = field.value;
    });
    
    // Edit button click handler
    if (editBtn) {
        editBtn.addEventListener('click', function() {
            // Enable form fields with animation
            formFields.forEach(field => {
                field.readOnly = false;
                field.classList.add('animate__animated', 'animate__pulse');
                setTimeout(() => {
                    field.classList.remove('animate__animated', 'animate__pulse');
                }, 1000);
            });
            
            // Show save and cancel buttons, hide edit button
            editBtn.style.display = 'none';
            if (saveBtn) saveBtn.style.display = 'inline-block';
            if (cancelBtn) cancelBtn.style.display = 'inline-block';
            
            // Focus on first field
            if (formFields.length > 0) {
                formFields[0].focus();
            }
        });
    }
    
    // Cancel button click handler
    if (cancelBtn) {
        cancelBtn.addEventListener('click', function() {
            cancelEdit(formFields, editBtn, saveBtn, cancelBtn);
        });
    }
    
    // Save button click handler
    if (saveBtn && profileForm) {
        saveBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Basic validation
            let isValid = true;
            let firstInvalidField = null;
            
            // Validate required fields
            formFields.forEach(field => {
                // Remove existing validation styling
                field.classList.remove('is-invalid');
                const feedbackElement = field.nextElementSibling;
                if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                    feedbackElement.remove();
                }
                
                // Check required fields
                if (field.required && !field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                    
                    // Add feedback message
                    const feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    feedback.textContent = 'This field is required';
                    field.parentNode.insertBefore(feedback, field.nextSibling);
                    
                    if (!firstInvalidField) {
                        firstInvalidField = field;
                    }
                }
                
                // Validate email format
                if (field.type === 'email' && field.value.trim()) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(field.value.trim())) {
                        isValid = false;
                        field.classList.add('is-invalid');
                        
                        // Add feedback message
                        const feedback = document.createElement('div');
                        feedback.className = 'invalid-feedback';
                        feedback.textContent = 'Please enter a valid email address';
                        field.parentNode.insertBefore(feedback, field.nextSibling);
                        
                        if (!firstInvalidField) {
                            firstInvalidField = field;
                        }
                    }
                }
                
                // Validate phone format (if it's a phone field)
                if (field.name === 'phone' && field.value.trim()) {
                    // Indonesian phone number format
                    const phoneRegex = /^(\+62|62|0)8[1-9][0-9]{6,9}$/;
                    if (!phoneRegex.test(field.value.trim())) {
                        isValid = false;
                        field.classList.add('is-invalid');
                        
                        // Add feedback message
                        const feedback = document.createElement('div');
                        feedback.className = 'invalid-feedback';
                        feedback.textContent = 'Please enter a valid Indonesian phone number';
                        field.parentNode.insertBefore(feedback, field.nextSibling);
                        
                        if (!firstInvalidField) {
                            firstInvalidField = field;
                        }
                    }
                }
            });
            
            // Focus on first invalid field
            if (firstInvalidField) {
                firstInvalidField.focus();
                return;
            }
            
            // If valid, save the profile
            if (isValid) {
                saveProfile(formFields, editBtn, saveBtn, cancelBtn);
            }
        });
    }
}

function cancelEdit(formFields, editBtn, saveBtn, cancelBtn) {
    // Reset form fields to original values with animation
    formFields.forEach(field => {
        field.value = field.dataset.original;
        field.readOnly = true;
        field.classList.remove('is-invalid');
        
        // Add subtle animation
        field.classList.add('animate__animated', 'animate__fadeIn');
        setTimeout(() => {
            field.classList.remove('animate__animated', 'animate__fadeIn');
        }, 1000);
        
        // Remove any validation feedback
        const feedbackElement = field.nextElementSibling;
        if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
            feedbackElement.remove();
        }
    });
    
    // Show edit button, hide save and cancel buttons
    if (editBtn) editBtn.style.display = 'inline-block';
    if (saveBtn) saveBtn.style.display = 'none';
    if (cancelBtn) cancelBtn.style.display = 'none';
}

function saveProfile(formFields, editBtn, saveBtn, cancelBtn) {
    // Show loading state
    const originalSaveBtnText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
    saveBtn.disabled = true;
    if (cancelBtn) cancelBtn.disabled = true;
    
    // Prepare form data
    const formData = new FormData();
    formFields.forEach(field => {
        formData.append(field.name, field.value.trim());
    });
    
    // Send AJAX request
    fetch('ajax/update_profile.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Update original values
            formFields.forEach(field => {
                field.dataset.original = field.value;
                field.readOnly = true;
                
                // Add success animation
                field.classList.add('animate__animated', 'animate__fadeIn');
                setTimeout(() => {
                    field.classList.remove('animate__animated', 'animate__fadeIn');
                }, 1000);
            });
            
            // Update displayed user info if needed
            const userFullName = document.querySelector('.user-full-name');
            const userEmail = document.querySelector('.user-email');
            
            if (userFullName) {
                const fullNameField = document.querySelector('input[name="full_name"]');
                if (fullNameField) {
                    userFullName.textContent = fullNameField.value;
                    userFullName.classList.add('animate__animated', 'animate__fadeIn');
                    setTimeout(() => {
                        userFullName.classList.remove('animate__animated', 'animate__fadeIn');
                    }, 1000);
                }
            }
            
            if (userEmail) {
                const emailField = document.querySelector('input[name="email"]');
                if (emailField) {
                    userEmail.textContent = emailField.value;
                    userEmail.classList.add('animate__animated', 'animate__fadeIn');
                    setTimeout(() => {
                        userEmail.classList.remove('animate__animated', 'animate__fadeIn');
                    }, 1000);
                }
            }
            
            // Show success message
            showMessage('Profile updated successfully!', 'success');
            
            // Reset UI
            if (editBtn) editBtn.style.display = 'inline-block';
            if (saveBtn) saveBtn.style.display = 'none';
            if (cancelBtn) cancelBtn.style.display = 'none';
        } else {
            // Show error message
            showMessage(data.message || 'Error updating profile', 'danger');
            
            // Handle validation errors
            if (data.errors) {
                Object.keys(data.errors).forEach(fieldName => {
                    const field = document.querySelector(`[name="${fieldName}"]`);
                    if (field) {
                        field.classList.add('is-invalid');
                        
                        // Add feedback message
                        const feedback = document.createElement('div');
                        feedback.className = 'invalid-feedback';
                        feedback.textContent = data.errors[fieldName];
                        field.parentNode.insertBefore(feedback, field.nextSibling);
                    }
                });
            }
        }
    })
    .catch(error => {
        showMessage('Error updating profile: ' + error.message, 'danger');
        console.error('Error:', error);
    })
    .finally(() => {
        // Reset button states
        saveBtn.innerHTML = originalSaveBtnText;
        saveBtn.disabled = false;
        if (cancelBtn) cancelBtn.disabled = false;
    });
}

// Password Form Functions
function setupPasswordForm() {
    const passwordForm = document.getElementById('changePasswordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const newPassword = formData.get('new_password');
            const confirmPassword = formData.get('confirm_password');
            
            // Validate passwords match
            if (newPassword !== confirmPassword) {
                showMessage('New passwords do not match', 'danger');
                return;
            }
            
            // Validate password strength
            if (newPassword.length < 8) {
                showMessage('Password must be at least 8 characters long', 'danger');
                return;
            }
            
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Updating...';
            submitBtn.disabled = true;
            
            // Send to change password endpoint
            fetch('ajax/change_password.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('Password updated successfully!', 'success');
                    this.reset();
                } else {
                    showMessage(data.message || 'Error updating password', 'danger');
                }
            })
            .catch(error => {
                showMessage('Error updating password', 'danger');
                console.error('Error:', error);
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
}

// Preferences Form Functions
function setupPreferencesForm() {
    const preferencesForm = document.getElementById('preferencesForm');
    if (preferencesForm) {
        preferencesForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
            submitBtn.disabled = true;
            
            // Send to update preferences endpoint
            fetch('ajax/update_preferences.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('Preferences updated successfully!', 'success');
                } else {
                    showMessage(data.message || 'Error updating preferences', 'danger');
                }
            })
            .catch(error => {
                showMessage('Error updating preferences', 'danger');
                console.error('Error:', error);
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
}

// Photo Upload Functions
function setupPhotoUpload() {
    const photoUploadInput = document.getElementById('profilePhotoUpload');
    const profileImage = document.querySelector('.profile-image img');
    const profileImageContainer = document.querySelector('.profile-image');
    
    if (!photoUploadInput || !profileImage) return;
    
    // Add hover effect to profile image
    if (profileImageContainer) {
        profileImageContainer.addEventListener('mouseenter', function() {
            this.classList.add('profile-image-hover');
            
            // Create or update the upload icon overlay
            let uploadIcon = this.querySelector('.upload-icon-overlay');
            if (!uploadIcon) {
                uploadIcon = document.createElement('div');
                uploadIcon.className = 'upload-icon-overlay animate__animated animate__fadeIn';
                uploadIcon.innerHTML = '<i class="fas fa-camera"></i><span>Change Photo</span>';
                this.appendChild(uploadIcon);
            } else {
                uploadIcon.classList.add('animate__animated', 'animate__fadeIn');
                uploadIcon.style.display = 'flex';
            }
        });
        
        profileImageContainer.addEventListener('mouseleave', function() {
            this.classList.remove('profile-image-hover');
            
            // Hide the upload icon overlay
            const uploadIcon = this.querySelector('.upload-icon-overlay');
            if (uploadIcon) {
                uploadIcon.classList.remove('animate__fadeIn');
                uploadIcon.classList.add('animate__fadeOut');
                setTimeout(() => {
                    uploadIcon.style.display = 'none';
                    uploadIcon.classList.remove('animate__fadeOut');
                }, 500);
            }
        });
        
        // Click on profile image to trigger file input
        profileImageContainer.addEventListener('click', function() {
            photoUploadInput.click();
        });
    }
    
    // Handle file selection
    photoUploadInput.addEventListener('change', function(e) {
        const file = this.files[0];
        if (!file) return;
        
        // Validate file type
        const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!validTypes.includes(file.type)) {
            showMessage('Please select a valid image file (JPEG, PNG, GIF, WEBP)', 'warning');
            this.value = '';
            return;
        }
        
        // Validate file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if (file.size > maxSize) {
            showMessage('Image size should not exceed 5MB', 'warning');
            this.value = '';
            return;
        }
        
        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            // Create preview container if it doesn't exist
            let previewContainer = document.querySelector('.photo-preview-container');
            if (!previewContainer) {
                previewContainer = document.createElement('div');
                previewContainer.className = 'photo-preview-container animate__animated animate__fadeIn';
                profileImageContainer.parentNode.insertBefore(previewContainer, profileImageContainer.nextSibling);
            } else {
                previewContainer.innerHTML = '';
                previewContainer.classList.add('animate__animated', 'animate__fadeIn');
            }
            
            // Create preview content
            previewContainer.innerHTML = `
                <div class="photo-preview">
                    <img src="${e.target.result}" alt="Preview" class="img-fluid rounded-circle animate__animated animate__zoomIn">
                    <div class="preview-actions mt-3">
                        <button type="button" class="btn btn-primary btn-sm me-2" id="uploadPhotoBtn">
                            <i class="fas fa-upload me-1"></i> Upload
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="cancelPhotoBtn">
                            <i class="fas fa-times me-1"></i> Cancel
                        </button>
                    </div>
                </div>
            `;
            
            // Setup action buttons
            document.getElementById('uploadPhotoBtn').addEventListener('click', function() {
                uploadProfilePhoto(file);
            });
            
            document.getElementById('cancelPhotoBtn').addEventListener('click', function() {
                // Remove preview with animation
                previewContainer.classList.remove('animate__fadeIn');
                previewContainer.classList.add('animate__fadeOut');
                setTimeout(() => {
                    previewContainer.remove();
                    photoUploadInput.value = '';
                }, 500);
            });
        };
        
        reader.readAsDataURL(file);
    });
}

function uploadProfilePhoto(file) {
    if (!file) return;
    
    // Create form data
    const formData = new FormData();
    formData.append('profile_photo', file);
    
    // Get upload button and store original text
    const uploadBtn = document.getElementById('uploadPhotoBtn');
    const originalBtnText = uploadBtn.innerHTML;
    
    // Show loading state
    uploadBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Uploading...';
    uploadBtn.disabled = true;
    document.getElementById('cancelPhotoBtn').disabled = true;
    
    // Send AJAX request
    fetch('ajax/upload_profile_photo.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Show success message
            showMessage(data.message || 'Profile photo updated successfully!', 'success');
            
            // Update profile image with animation
            const profileImage = document.querySelector('.profile-image img');
            if (profileImage) {
                // Add a fade out animation
                profileImage.classList.add('animate__animated', 'animate__fadeOut');
                
                // After fade out, update the image and fade in
                setTimeout(() => {
                    profileImage.src = data.photo_url + '?t=' + new Date().getTime(); // Add timestamp to prevent caching
                    profileImage.classList.remove('animate__fadeOut');
                    profileImage.classList.add('animate__fadeIn');
                    
                    // Remove animation classes after animation completes
                    setTimeout(() => {
                        profileImage.classList.remove('animate__animated', 'animate__fadeIn');
                    }, 1000);
                }, 500);
            }
            
            // Remove preview container with animation
            const previewContainer = document.querySelector('.photo-preview-container');
            if (previewContainer) {
                previewContainer.classList.remove('animate__fadeIn');
                previewContainer.classList.add('animate__fadeOut');
                setTimeout(() => {
                    previewContainer.remove();
                }, 500);
            }
            
            // Reset file input
            document.getElementById('profilePhotoUpload').value = '';
            
            // Update header profile image if exists
            const headerProfileImage = document.querySelector('.header-profile-image');
            if (headerProfileImage) {
                headerProfileImage.src = data.photo_url + '?t=' + new Date().getTime();
            }
        } else {
            // Show error message
            showMessage(data.message || 'Error updating profile photo', 'danger');
        }
    })
    .catch(error => {
        showMessage('Error uploading photo: ' + error.message, 'danger');
        console.error('Error:', error);
    })
    .finally(() => {
        // Reset button states
        if (uploadBtn) {
            uploadBtn.innerHTML = originalBtnText;
            uploadBtn.disabled = false;
        }
        
        const cancelBtn = document.getElementById('cancelPhotoBtn');
        if (cancelBtn) {
            cancelBtn.disabled = false;
        }
    });
}

// Address Management Functions
function loadAddresses() {
    const addressContainer = document.getElementById('addressList');
    if (!addressContainer) return;
    
    // Show loading state
    addressContainer.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Loading addresses...</p></div>';
    
    // Fetch addresses
    fetch('ajax/get_addresses.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                if (data.addresses && data.addresses.length > 0) {
                    // Clear container
                    addressContainer.innerHTML = '';
                    
                    // Add addresses with animation
                    data.addresses.forEach((address, index) => {
                        const addressCard = createAddressCard(address);
                        addressContainer.appendChild(addressCard);
                        
                        // Add staggered animation
                        setTimeout(() => {
                            addressCard.classList.add('animate__animated', 'animate__fadeInUp');
                        }, index * 100);
                    });
                } else {
                    // No addresses
                    addressContainer.innerHTML = `
                        <div class="text-center py-4 animate__animated animate__fadeIn">
                            <div class="mb-3">
                                <i class="fas fa-map-marker-alt fa-3x text-muted"></i>
                            </div>
                            <h5>No addresses found</h5>
                            <p class="text-muted">You haven't added any addresses yet.</p>
                            <button class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                                <i class="fas fa-plus-circle me-2"></i>Add New Address
                            </button>
                        </div>
                    `;
                }
            } else {
                // Error
                addressContainer.innerHTML = `
                    <div class="alert alert-danger animate__animated animate__shakeX">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.message || 'Error loading addresses'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            addressContainer.innerHTML = `
                <div class="alert alert-danger animate__animated animate__shakeX">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading addresses: ${error.message}
                </div>
                <button class="btn btn-outline-primary mt-3" onclick="loadAddresses()">
                    <i class="fas fa-sync-alt me-2"></i>Try Again
                </button>
            `;
        });
}

function createAddressCard(address) {
    const card = document.createElement('div');
    card.className = 'card mb-3 border-0 shadow-sm';
    card.setAttribute('data-address-id', address.id);
    
    // Add default badge if this is the default address
    const defaultBadge = address.is_default ? 
        `<span class="badge bg-primary position-absolute top-0 end-0 m-2 animate__animated animate__pulse">Default</span>` : '';
    
    card.innerHTML = `
        <div class="card-body">
            ${defaultBadge}
            <div class="d-flex align-items-start">
                <div class="address-icon me-3">
                    <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                </div>
                <div class="address-details flex-grow-1">
                    <h5 class="card-title mb-1">${address.label || 'Address'}</h5>
                    <p class="card-text mb-1">${address.recipient_name}</p>
                    <p class="card-text mb-1">${address.phone}</p>
                    <p class="card-text mb-1">${address.address_line1}</p>
                    ${address.address_line2 ? `<p class="card-text mb-1">${address.address_line2}</p>` : ''}
                    <p class="card-text mb-1">${address.city}, ${address.province}, ${address.postal_code}</p>
                    <div class="address-actions mt-3">
                        ${!address.is_default ? 
                            `<button class="btn btn-sm btn-outline-primary me-2 set-default-btn" data-address-id="${address.id}">
                                <i class="fas fa-check-circle me-1"></i>Set as Default
                            </button>` : ''
                        }
                        <button class="btn btn-sm btn-outline-secondary me-2 edit-address-btn" data-address-id="${address.id}">
                            <i class="fas fa-edit me-1"></i>Edit
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-address-btn" data-address-id="${address.id}">
                            <i class="fas fa-trash-alt me-1"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add event listeners
    setupAddressCardListeners(card, address);
    
    return card;
}

function setupAddressCardListeners(card, address) {
    // Set default button
    const setDefaultBtn = card.querySelector('.set-default-btn');
    if (setDefaultBtn) {
        setDefaultBtn.addEventListener('click', function() {
            setDefaultAddress(address.id);
        });
    }
    
    // Edit button
    const editBtn = card.querySelector('.edit-address-btn');
    if (editBtn) {
        editBtn.addEventListener('click', function() {
            editAddress(address);
        });
    }
    
    // Delete button
    const deleteBtn = card.querySelector('.delete-address-btn');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            deleteAddress(address.id);
        });
    }
}

function saveAddress() {
    const form = document.getElementById('addAddressForm');
    if (!form) return;
    
    // Basic validation
    let isValid = true;
    let firstInvalidField = null;
    
    // Get all form fields
    const formFields = form.querySelectorAll('.form-control, .form-select');
    
    // Validate required fields
    formFields.forEach(field => {
        // Remove existing validation styling
        field.classList.remove('is-invalid');
        const feedbackElement = field.nextElementSibling;
        if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
            feedbackElement.remove();
        }
        
        // Check required fields
        if (field.required && !field.value.trim()) {
            isValid = false;
            field.classList.add('is-invalid');
            
            // Add feedback message
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'This field is required';
            field.parentNode.insertBefore(feedback, field.nextSibling);
            
            if (!firstInvalidField) {
                firstInvalidField = field;
            }
        }
        
        // Validate phone format (if it's a phone field)
        if (field.name === 'phone' && field.value.trim()) {
            // Indonesian phone number format
            const phoneRegex = /^(\+62|62|0)8[1-9][0-9]{6,9}$/;
            if (!phoneRegex.test(field.value.trim())) {
                isValid = false;
                field.classList.add('is-invalid');
                
                // Add feedback message
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Please enter a valid Indonesian phone number';
                field.parentNode.insertBefore(feedback, field.nextSibling);
                
                if (!firstInvalidField) {
                    firstInvalidField = field;
                }
            }
        }
        
        // Validate postal code (if it's a postal code field)
        if (field.name === 'postal_code' && field.value.trim()) {
            // Indonesian postal code format (5 digits)
            const postalCodeRegex = /^\d{5}$/;
            if (!postalCodeRegex.test(field.value.trim())) {
                isValid = false;
                field.classList.add('is-invalid');
                
                // Add feedback message
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Please enter a valid 5-digit postal code';
                field.parentNode.insertBefore(feedback, field.nextSibling);
                
                if (!firstInvalidField) {
                    firstInvalidField = field;
                }
            }
        }
    });
    
    // Focus on first invalid field
    if (firstInvalidField) {
        firstInvalidField.focus();
        return;
    }
    
    // If valid, save the address
    if (isValid) {
        // Get form data
        const formData = new FormData(form);
        
        // Get submit button and store original text
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
        submitBtn.disabled = true;
        
        // Send AJAX request
        fetch('ajax/save_address.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Show success message
                showMessage(data.message || 'Address saved successfully!', 'success');
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('addAddressModal'));
                if (modal) {
                    modal.hide();
                }
                
                // Reset form
                form.reset();
                
                // Reload addresses
                loadAddresses();
            } else {
                // Show error message
                showMessage(data.message || 'Error saving address', 'danger');
                
                // Handle validation errors
                if (data.errors) {
                    Object.keys(data.errors).forEach(fieldName => {
                        const field = form.querySelector(`[name="${fieldName}"]`);
                        if (field) {
                            field.classList.add('is-invalid');
                            
                            // Add feedback message
                            const feedback = document.createElement('div');
                            feedback.className = 'invalid-feedback';
                            feedback.textContent = data.errors[fieldName];
                            field.parentNode.insertBefore(feedback, field.nextSibling);
                        }
                    });
                }
            }
        })
        .catch(error => {
            showMessage('Error saving address: ' + error.message, 'danger');
            console.error('Error:', error);
        })
        .finally(() => {
            // Reset button state
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
        });
    }
}

function setDefaultAddress(addressId) {
    if (!addressId) return;
    
    // Show confirmation dialog
    if (!confirm('Set this as your default address?')) return;
    
    // Find the button that was clicked
    const button = document.querySelector(`.set-default-btn[data-address-id="${addressId}"]`);
    if (button) {
        // Store original text and disable button
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Setting...';
        button.disabled = true;
        
        // Disable all address action buttons temporarily
        document.querySelectorAll('.address-actions button').forEach(btn => {
            btn.disabled = true;
        });
        
        // Send AJAX request
        fetch('ajax/set_default_address.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `address_id=${addressId}`
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Show success message
                showMessage(data.message || 'Default address updated!', 'success');
                
                // Reload addresses to reflect changes
                loadAddresses();
            } else {
                // Show error message
                showMessage(data.message || 'Error setting default address', 'danger');
                
                // Reset button state
                if (button) {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
                
                // Re-enable all address action buttons
                document.querySelectorAll('.address-actions button').forEach(btn => {
                    btn.disabled = false;
                });
            }
        })
        .catch(error => {
            showMessage('Error setting default address: ' + error.message, 'danger');
            console.error('Error:', error);
            
            // Reset button state
            if (button) {
                button.innerHTML = originalText;
                button.disabled = false;
            }
            
            // Re-enable all address action buttons
            document.querySelectorAll('.address-actions button').forEach(btn => {
                btn.disabled = false;
            });
        });
    }
}

function editAddress(address) {
    if (!address) return;
    
    // Get the form
    const form = document.getElementById('addAddressForm');
    if (!form) return;
    
    // Set form title
    const modalTitle = document.querySelector('#addAddressModal .modal-title');
    if (modalTitle) {
        modalTitle.textContent = 'Edit Address';
    }
    
    // Set address ID in hidden field
    const addressIdField = form.querySelector('input[name="address_id"]');
    if (addressIdField) {
        addressIdField.value = address.id;
    }
    
    // Fill form fields
    const fields = [
        'label', 'recipient_name', 'phone', 'address_line1', 'address_line2',
        'city', 'province', 'postal_code'
    ];
    
    fields.forEach(field => {
        const input = form.querySelector(`[name="${field}"]`);
        if (input && address[field] !== undefined) {
            input.value = address[field];
            
            // Add animation
            input.classList.add('animate__animated', 'animate__fadeIn');
            setTimeout(() => {
                input.classList.remove('animate__animated', 'animate__fadeIn');
            }, 1000);
        }
    });
    
    // Set is_default checkbox
    const defaultCheckbox = form.querySelector('input[name="is_default"]');
    if (defaultCheckbox) {
        defaultCheckbox.checked = address.is_default;
    }
    
    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('addAddressModal'));
    modal.show();
}

function deleteAddress(addressId) {
    if (!addressId) return;
    
    // Show confirmation dialog with custom styling
    const confirmDelete = confirm('Are you sure you want to delete this address? This action cannot be undone.');
    if (!confirmDelete) return;
    
    // Find the address card
    const addressCard = document.querySelector(`.card[data-address-id="${addressId}"]`);
    if (addressCard) {
        // Add delete animation
        addressCard.classList.add('animate__animated', 'animate__fadeOut');
        
        // Send delete request after animation starts
        setTimeout(() => {
            // Send AJAX request
            fetch('ajax/delete_address.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `address_id=${addressId}`
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success message
                    showMessage(data.message || 'Address deleted successfully!', 'success');
                    
                    // Remove the card after animation completes
                    setTimeout(() => {
                        addressCard.remove();
                        
                        // Check if there are no more addresses
                        const addressContainer = document.getElementById('addressList');
                        if (addressContainer && addressContainer.children.length === 0) {
                            // Show empty state
                            addressContainer.innerHTML = `
                                <div class="text-center py-4 animate__animated animate__fadeIn">
                                    <div class="mb-3">
                                        <i class="fas fa-map-marker-alt fa-3x text-muted"></i>
                                    </div>
                                    <h5>No addresses found</h5>
                                    <p class="text-muted">You haven't added any addresses yet.</p>
                                    <button class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                                        <i class="fas fa-plus-circle me-2"></i>Add New Address
                                    </button>
                                </div>
                            `;
                        }
                    }, 500);
                } else {
                    // Show error message
                    showMessage(data.message || 'Error deleting address', 'danger');
                    
                    // Remove animation class
                    addressCard.classList.remove('animate__animated', 'animate__fadeOut');
                }
            })
            .catch(error => {
                showMessage('Error deleting address: ' + error.message, 'danger');
                console.error('Error:', error);
                
                // Remove animation class
                addressCard.classList.remove('animate__animated', 'animate__fadeOut');
            });
        }, 100);
    }
}

function editAddress(addressId) {
    if (!addressId) return;
    
    // Fetch address details
    fetch(`ajax/get_address.php?address_id=${addressId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.address) {
                const address = data.address;
                const form = document.getElementById('addAddressForm');
                
                // Set form values
                form.querySelector('[name="address_id"]').value = address.id;
                form.querySelector('[name="label"]').value = address.label;
                form.querySelector('[name="recipient_name"]').value = address.recipient_name;
                form.querySelector('[name="phone"]').value = address.phone;
                form.querySelector('[name="province"]').value = address.province;
                form.querySelector('[name="city"]').value = address.city;
                form.querySelector('[name="postal_code"]').value = address.postal_code;
                form.querySelector('[name="address"]').value = address.address;
                form.querySelector('[name="is_default"]').checked = address.is_default;
                
                // Update modal title
                document.querySelector('#addAddressModal .modal-title').textContent = 'Edit Address';
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('addAddressModal'));
                modal.show();
            } else {
                showMessage(data.message || 'Error loading address details', 'danger');
            }
        })
        .catch(error => {
            showMessage('Error loading address details', 'danger');
            console.error('Error:', error);
        });
}

function setupAddressModal() {
    const modal = document.getElementById('addAddressModal');
    if (!modal) return;
    
    // Reset form when modal is hidden
    modal.addEventListener('hidden.bs.modal', function() {
        const form = document.getElementById('addAddressForm');
        if (form) {
            form.reset();
            
            // Clear validation states
            form.querySelectorAll('.is-invalid').forEach(field => {
                field.classList.remove('is-invalid');
            });
            
            // Remove feedback messages
            form.querySelectorAll('.invalid-feedback').forEach(feedback => {
                feedback.remove();
            });
            
            // Reset form title
            const modalTitle = modal.querySelector('.modal-title');
            if (modalTitle) {
                modalTitle.textContent = 'Add New Address';
            }
            
            // Reset address ID field
            const addressIdField = form.querySelector('input[name="address_id"]');
            if (addressIdField) {
                addressIdField.value = '';
            }
        }
    });
    
    // Add animation when modal is shown
    modal.addEventListener('show.bs.modal', function() {
        const modalDialog = this.querySelector('.modal-dialog');
        if (modalDialog) {
            modalDialog.classList.add('animate__animated', 'animate__fadeInDown');
            setTimeout(() => {
                modalDialog.classList.remove('animate__animated', 'animate__fadeInDown');
            }, 1000);
        }
    });
    
    // Setup province and city dropdowns
    setupProvinceDropdown();
}

function setupProvinceDropdown() {
    const provinceSelect = document.querySelector('select[name="province"]');
    const citySelect = document.querySelector('select[name="city"]');
    
    if (!provinceSelect || !citySelect) return;
    
    // Disable city dropdown initially
    citySelect.disabled = true;
    
    // Load provinces
    fetch('ajax/get_provinces.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.provinces) {
                // Clear existing options except the first one
                while (provinceSelect.options.length > 1) {
                    provinceSelect.remove(1);
                }
                
                // Add new options
                data.provinces.forEach(province => {
                    const option = document.createElement('option');
                    option.value = province.id;
                    option.textContent = province.name;
                    provinceSelect.appendChild(option);
                });
                
                // Check if there's a selected province value
                const selectedProvinceId = provinceSelect.getAttribute('data-selected');
                if (selectedProvinceId) {
                    provinceSelect.value = selectedProvinceId;
                    loadCities(selectedProvinceId);
                }
            }
        })
        .catch(error => {
            console.error('Error loading provinces:', error);
        });
    
    // Event listener for province change
    provinceSelect.addEventListener('change', function() {
        const provinceId = this.value;
        const citySelect = document.querySelector('select[name="city"]');
        
        // Reset and disable city dropdown
        citySelect.disabled = true;
        citySelect.innerHTML = '<option value="">Select City</option>';
        
        if (!provinceId) return;
        
        // Show loading state
        citySelect.innerHTML = '<option value="">Loading cities...</option>';
        
        fetch(`ajax/get_cities.php?province_id=${encodeURIComponent(provinceId)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && Array.isArray(data.cities)) {
                    // Add city options
                    data.cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.id;
                        option.textContent = city.name;
                        citySelect.appendChild(option);
                    });
                    
                    // Enable dropdown
                    citySelect.disabled = false;
                    
                    // Set previously selected city if exists
                    const selectedCityId = citySelect.getAttribute('data-selected');
                    if (selectedCityId) {
                        citySelect.value = selectedCityId;
                    }
                    
                    // Add animation if supported
                    if (typeof window.getComputedStyle !== 'undefined') {
                        citySelect.classList.add('animate__animated', 'animate__fadeIn');
                        setTimeout(() => {
                            citySelect.classList.remove('animate__animated', 'animate__fadeIn');
                        }, 1000);
                    }
                } else {
                    citySelect.innerHTML = '<option value="">No cities found</option>';
                    showMessage('No cities available for selected province', 'warning');
                }
            })
            .catch(error => {
                console.error('Error loading cities:', error);
                citySelect.innerHTML = '<option value="">Error loading cities</option>';
                showMessage('Failed to load cities: ' + error.message, 'danger');
            });
    });
    
   // Fixed Province-City Dropdown Code
function initializeProvinceCity() {
    const provinceSelect = document.getElementById('province');
    const citySelect = document.getElementById('city');
    
    // Check if elements exist
    if (!provinceSelect || !citySelect) {
        console.error('Province or City select element not found');
        return;
    }
    
    // Initialize city dropdown as disabled
    citySelect.disabled = true;
    
    // Function to show messages (assuming this function exists)
    function showMessage(message, type = 'info') {
        // If showMessage function doesn't exist, use console.log or alert
        if (typeof window.showMessage === 'function') {
            window.showMessage(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
            // Optionally show alert for errors
            if (type === 'danger' || type === 'error') {
                alert(message);
            }
        }
    }
    
    // Load provinces on page load
    loadProvinces();
    
    function loadProvinces() {
        // Show loading state
        provinceSelect.innerHTML = '<option value="">Loading provinces...</option>';
        provinceSelect.disabled = true;
        
        fetch('ajax/get_provinces.php')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Reset dropdown
                provinceSelect.innerHTML = '<option value="">Select Province</option>';
                
                if (data.success && Array.isArray(data.provinces) && data.provinces.length > 0) {
                    // Add province options
                    data.provinces.forEach(province => {
                        const option = document.createElement('option');
                        option.value = province.id;
                        option.textContent = province.name;
                        provinceSelect.appendChild(option);
                    });
                    
                    // Enable dropdown
                    provinceSelect.disabled = false;
                    
                    // Add animation (if animate.css is loaded)
                    if (typeof window.getComputedStyle !== 'undefined') {
                        provinceSelect.classList.add('animate__animated', 'animate__fadeIn');
                        setTimeout(() => {
                            provinceSelect.classList.remove('animate__animated', 'animate__fadeIn');
                        }, 1000);
                    }
                } else {
                    provinceSelect.innerHTML = '<option value="">No provinces found</option>';
                    showMessage('No provinces data available', 'warning');
                }
            })
            .catch(error => {
                console.error('Province loading error:', error);
                provinceSelect.innerHTML = '<option value="">Error loading provinces</option>';
                provinceSelect.disabled = false; // Allow retry
                showMessage('Error loading provinces: ' + error.message, 'danger');
            });
    }
    
    // Province change event listener
    provinceSelect.addEventListener('change', function() {
        const provinceId = this.value;
        
        // Reset city dropdown
        citySelect.innerHTML = '<option value="">Select City</option>';
        citySelect.disabled = true;
        
        if (!provinceId || provinceId === '') {
            return; // No province selected
        }
        
        loadCities(provinceId);
    });
    
    function loadCities(provinceId) {
        // Show loading in city dropdown
        citySelect.innerHTML = '<option value="">Loading cities...</option>';
        
        // Validate provinceId
        if (!provinceId || isNaN(provinceId)) {
            citySelect.innerHTML = '<option value="">Invalid province selected</option>';
            showMessage('Invalid province selection', 'danger');
            return;
        }
        
        // Load cities for selected province
        fetch(`ajax/get_cities.php?province_id=${encodeURIComponent(provinceId)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Reset dropdown
                citySelect.innerHTML = '<option value="">Select City</option>';
                
                if (data.success && Array.isArray(data.cities) && data.cities.length > 0) {
                    // Add city options
                    data.cities.forEach(city => {
                        const option = document.createElement('option');
                        option.value = city.id;
                        option.textContent = city.name;
                        citySelect.appendChild(option);
                    });
                    
                    // Enable dropdown
                    citySelect.disabled = false;
                    
                    // Add animation (if animate.css is loaded)
                    if (typeof window.getComputedStyle !== 'undefined') {
                        citySelect.classList.add('animate__animated', 'animate__fadeIn');
                        setTimeout(() => {
                            citySelect.classList.remove('animate__animated', 'animate__fadeIn');
                        }, 1000);
                    }
                } else {
                    citySelect.innerHTML = '<option value="">No cities found</option>';
                    showMessage(data.message || 'No cities found for selected province', 'warning');
                }
            })
            .catch(error => {
                console.error('City loading error:', error);
                citySelect.innerHTML = '<option value="">Error loading cities</option>';
                showMessage('Error loading cities: ' + error.message, 'danger');
            });
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeProvinceCity);
} else {
    initializeProvinceCity();
}
// Initialize all profile functions
document.addEventListener('DOMContentLoaded', function() {
    initProfile();
});

function initProfile() {
    // Setup profile form
    setupProfileForm();
    
    // Setup photo upload
    setupPhotoUpload();
    
    // Load addresses
    loadAddresses();
    
    // Setup address modal
    setupAddressModal();
    
    // Setup tabs with animation
    setupProfileTabs();
    
    // Setup security settings
    setupSecuritySettings();
    
    // Setup order history
    setupOrderHistory();
    
    console.log('Profile initialized successfully');
}

function setupProfileTabs() {
    const tabLinks = document.querySelectorAll('.nav-link[data-bs-toggle="tab"]');
    if (!tabLinks.length) return;
    
    tabLinks.forEach(tabLink => {
        tabLink.addEventListener('shown.bs.tab', function(e) {
            // Get the newly activated tab content
            const targetId = e.target.getAttribute('href');
            const targetContent = document.querySelector(targetId);
            
            if (targetContent) {
                // Add animation to the tab content
                targetContent.classList.add('animate__animated', 'animate__fadeIn');
                setTimeout(() => {
                    targetContent.classList.remove('animate__animated', 'animate__fadeIn');
                }, 1000);
                
                // Update URL hash without scrolling
                const scrollPosition = window.scrollY;
                window.location.hash = targetId;
                window.scrollTo(0, scrollPosition);
            }
        });
    });
    
    // Activate tab based on URL hash on page load
    const hash = window.location.hash;
    if (hash) {
        const activeTab = document.querySelector(`.nav-link[href="${hash}"]`);
        if (activeTab) {
            const tab = new bootstrap.Tab(activeTab);
            tab.show();
        }
    }
}

function setupSecuritySettings() {
    const securityForm = document.getElementById('securityForm');
    if (!securityForm) return;
    
    securityForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Basic validation
        let isValid = true;
        let firstInvalidField = null;
        
        // Get password fields
        const currentPassword = document.getElementById('currentPassword');
        const newPassword = document.getElementById('newPassword');
        const confirmPassword = document.getElementById('confirmPassword');
        
        // Clear previous validation
        [currentPassword, newPassword, confirmPassword].forEach(field => {
            if (!field) return;
            
            field.classList.remove('is-invalid');
            const feedbackElement = field.nextElementSibling;
            if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                feedbackElement.remove();
            }
        });
        
        // Validate current password
        if (!currentPassword.value.trim()) {
            isValid = false;
            currentPassword.classList.add('is-invalid');
            
            // Add feedback message
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'Please enter your current password';
            currentPassword.parentNode.insertBefore(feedback, currentPassword.nextSibling);
            
            if (!firstInvalidField) {
                firstInvalidField = currentPassword;
            }
        }
        
        // Validate new password
        if (!newPassword.value.trim()) {
            isValid = false;
            newPassword.classList.add('is-invalid');
            
            // Add feedback message
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'Please enter a new password';
            newPassword.parentNode.insertBefore(feedback, newPassword.nextSibling);
            
            if (!firstInvalidField) {
                firstInvalidField = newPassword;
            }
        } else if (newPassword.value.length < 8) {
            isValid = false;
            newPassword.classList.add('is-invalid');
            
            // Add feedback message
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'Password must be at least 8 characters long';
            newPassword.parentNode.insertBefore(feedback, newPassword.nextSibling);
            
            if (!firstInvalidField) {
                firstInvalidField = newPassword;
            }
        }
        
        // Validate confirm password
        if (!confirmPassword.value.trim()) {
            isValid = false;
            confirmPassword.classList.add('is-invalid');
            
            // Add feedback message
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'Please confirm your new password';
            confirmPassword.parentNode.insertBefore(feedback, confirmPassword.nextSibling);
            
            if (!firstInvalidField) {
                firstInvalidField = confirmPassword;
            }
        } else if (confirmPassword.value !== newPassword.value) {
            isValid = false;
            confirmPassword.classList.add('is-invalid');
            
            // Add feedback message
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'Passwords do not match';
            confirmPassword.parentNode.insertBefore(feedback, confirmPassword.nextSibling);
            
            if (!firstInvalidField) {
                firstInvalidField = confirmPassword;
            }
        }
        
        // Focus on first invalid field
        if (firstInvalidField) {
            firstInvalidField.focus();
            return;
        }
        
        // If valid, submit the form
        if (isValid) {
            // Get submit button and store original text
            const submitBtn = securityForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Updating...';
            submitBtn.disabled = true;
            
            // Prepare form data
            const formData = new FormData(securityForm);
            
            // Send AJAX request
            fetch('ajax/update_password.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success message
                    showMessage(data.message || 'Password updated successfully!', 'success');
                    
                    // Reset form
                    securityForm.reset();
                    
                    // Add success animation to form
                    securityForm.classList.add('animate__animated', 'animate__pulse');
                    setTimeout(() => {
                        securityForm.classList.remove('animate__animated', 'animate__pulse');
                    }, 1000);
                } else {
                    // Show error message
                    showMessage(data.message || 'Error updating password', 'danger');
                    
                    // Handle validation errors
                    if (data.errors) {
                        Object.keys(data.errors).forEach(fieldName => {
                            const field = document.getElementById(fieldName);
                            if (field) {
                                field.classList.add('is-invalid');
                                
                                // Add feedback message
                                const feedback = document.createElement('div');
                                feedback.className = 'invalid-feedback';
                                feedback.textContent = data.errors[fieldName];
                                field.parentNode.insertBefore(feedback, field.nextSibling);
                            }
                        });
                    }
                }
            })
            .catch(error => {
                showMessage('Error updating password: ' + error.message, 'danger');
                console.error('Error:', error);
            })
            .finally(() => {
                // Reset button state
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            });
        }
    });
}

function setupOrderHistory() {
    const orderHistoryContainer = document.getElementById('orderHistory');
    if (!orderHistoryContainer) return;
    
    // Add hover effect to order cards
    const orderCards = orderHistoryContainer.querySelectorAll('.card');
    orderCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('shadow-lg');
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('shadow-lg');
            this.style.transform = 'translateY(0)';
            this.style.transition = 'all 0.3s ease';
        });
    });
    
    // Add click event to view order details
    const viewDetailsBtns = orderHistoryContainer.querySelectorAll('.view-order-details');
    viewDetailsBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const orderId = this.getAttribute('data-order-id');
            if (orderId) {
                viewOrderDetails(orderId);
            }
        });
    });
}

function viewOrderDetails(orderId) {
    if (!orderId) return;
    
    // Show loading state
    const orderDetailsContainer = document.getElementById('orderDetailsContainer');
    if (!orderDetailsContainer) return;
    
    orderDetailsContainer.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Loading order details...</p></div>';
    
    // Show modal
    const orderDetailsModal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
    orderDetailsModal.show();
    
    // Fetch order details
    fetch(`ajax/get_order_details.php?order_id=${orderId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.order) {
                // Render order details
                const order = data.order;
                
                let detailsHTML = `
                    <div class="order-header mb-4 animate__animated animate__fadeIn">
                        <h5 class="mb-3">Order #${order.order_number}</h5>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Date:</span>
                            <span>${order.order_date}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Status:</span>
                            <span class="badge bg-${getStatusColor(order.status)}">${order.status}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Total:</span>
                            <span class="fw-bold">Rp ${formatNumber(order.total_amount)}</span>
                        </div>
                    </div>
                    
                    <h6 class="mb-3">Items</h6>
                    <div class="order-items mb-4">
                `;
                
                // Add order items
                if (order.items && order.items.length > 0) {
                    order.items.forEach((item, index) => {
                        detailsHTML += `
                            <div class="card mb-2 border-0 shadow-sm animate__animated animate__fadeInUp" style="animation-delay: ${index * 100}ms">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <img src="${item.product_image || 'assets/img/product-placeholder.jpg'}" alt="${item.product_name}" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="mb-0">${item.product_name}</h6>
                                            <div class="d-flex justify-content-between align-items-center mt-1">
                                                <small class="text-muted">${item.quantity} x Rp ${formatNumber(item.price)}</small>
                                                <span>Rp ${formatNumber(item.quantity * item.price)}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    detailsHTML += '<div class="alert alert-info">No items found for this order.</div>';
                }
                
                detailsHTML += '</div>';
                
                // Add shipping address
                if (order.shipping_address) {
                    detailsHTML += `
                        <h6 class="mb-3">Shipping Address</h6>
                        <div class="card mb-4 border-0 shadow-sm animate__animated animate__fadeIn">
                            <div class="card-body p-3">
                                <p class="mb-1">${order.shipping_address.recipient_name}</p>
                                <p class="mb-1">${order.shipping_address.phone}</p>
                                <p class="mb-1">${order.shipping_address.address_line1}</p>
                                ${order.shipping_address.address_line2 ? `<p class="mb-1">${order.shipping_address.address_line2}</p>` : ''}
                                <p class="mb-1">${order.shipping_address.city}, ${order.shipping_address.province}, ${order.shipping_address.postal_code}</p>
                            </div>
                        </div>
                    `;
                }
                
                // Add payment details
                detailsHTML += `
                    <h6 class="mb-3">Payment Details</h6>
                    <div class="card border-0 shadow-sm animate__animated animate__fadeIn">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span>Rp ${formatNumber(order.subtotal)}</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Shipping:</span>
                                <span>Rp ${formatNumber(order.shipping_cost)}</span>
                            </div>
                            ${order.discount ? `
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Discount:</span>
                                    <span>-Rp ${formatNumber(order.discount)}</span>
                                </div>
                            ` : ''}
                            <div class="d-flex justify-content-between fw-bold">
                                <span>Total:</span>
                                <span>Rp ${formatNumber(order.total_amount)}</span>
                            </div>
                        </div>
                    </div>
                `;
                
                // Update container
                orderDetailsContainer.innerHTML = detailsHTML;
            } else {
                // Show error
                orderDetailsContainer.innerHTML = `
                    <div class="alert alert-danger animate__animated animate__shakeX">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${data.message || 'Error loading order details'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            orderDetailsContainer.innerHTML = `
                <div class="alert alert-danger animate__animated animate__shakeX">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading order details: ${error.message}
                </div>
                <button class="btn btn-outline-primary mt-3" onclick="viewOrderDetails('${orderId}')">
                    <i class="fas fa-sync-alt me-2"></i>Try Again
                </button>
            `;
        });
}

function getStatusColor(status) {
    switch (status.toLowerCase()) {
        case 'pending':
            return 'warning';
        case 'processing':
            return 'info';
        case 'shipped':
            return 'primary';
        case 'delivered':
            return 'success';
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}

function formatNumber(number) {
    return parseFloat(number).toLocaleString('id-ID');
}

// Enhanced form validation function
function validateForm(form, validationRules) {
    let isValid = true;
    const invalidFields = [];
    
    // Reset all validation states
    form.querySelectorAll('.is-invalid').forEach(field => {
        field.classList.remove('is-invalid', 'animate__animated', 'animate__shakeX');
    });
    form.querySelectorAll('.invalid-feedback').forEach(feedback => {
        feedback.textContent = '';
    });
    
    // Apply validation rules
    for (const fieldName in validationRules) {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (!field) continue;
        
        const rules = validationRules[fieldName];
        let fieldIsValid = true;
        let errorMessage = '';
        
        // Check each rule for the field
        for (const rule of rules) {
            if (rule.type === 'required' && !field.value.trim()) {
                fieldIsValid = false;
                errorMessage = rule.message || 'This field is required';
                break;
            } else if (rule.type === 'minLength' && field.value.trim().length < rule.value) {
                fieldIsValid = false;
                errorMessage = rule.message || `Minimum ${rule.value} characters required`;
                break;
            } else if (rule.type === 'maxLength' && field.value.trim().length > rule.value) {
                fieldIsValid = false;
                errorMessage = rule.message || `Maximum ${rule.value} characters allowed`;
                break;
            } else if (rule.type === 'pattern' && !rule.pattern.test(field.value.trim())) {
                fieldIsValid = false;
                errorMessage = rule.message || 'Invalid format';
                break;
            } else if (rule.type === 'match' && field.value !== form.querySelector(`[name="${rule.field}"]`).value) {
                fieldIsValid = false;
                errorMessage = rule.message || 'Fields do not match';
                break;
            } else if (rule.type === 'custom' && !rule.validate(field.value, form)) {
                fieldIsValid = false;
                errorMessage = rule.message || 'Invalid value';
                break;
            }
        }
        
        // Mark field as invalid if it fails validation
        if (!fieldIsValid) {
            isValid = false;
            invalidFields.push(field);
            
            // Add visual feedback with animation
            field.classList.add('is-invalid', 'animate__animated', 'animate__shakeX');
            
            // Create or update feedback message
            let feedback = field.nextElementSibling;
            if (!feedback || !feedback.classList.contains('invalid-feedback')) {
                feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                field.parentNode.insertBefore(feedback, field.nextSibling);
            }
            feedback.textContent = errorMessage;
        } else {
            // Optionally add valid feedback
            field.classList.add('is-valid');
        }
    }
    
    // Focus on the first invalid field
    if (invalidFields.length > 0) {
        invalidFields[0].focus();
    }
    
    return isValid;
}

function editAddress(addressId) {
    if (!addressId) return;
    
    // Fetch address details
    fetch(`ajax/get_address.php?address_id=${addressId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.address) {
                const address = data.address;
                const form = document.getElementById('addAddressForm');
                
                // Set form values
                form.querySelector('[name="address_id"]').value = address.id;
                form.querySelector('[name="label"]').value = address.label;
                form.querySelector('[name="recipient_name"]').value = address.recipient_name;
                form.querySelector('[name="phone"]').value = address.phone;
                form.querySelector('[name="province"]').value = address.province;
                form.querySelector('[name="city"]').value = address.city;
                form.querySelector('[name="postal_code"]').value = address.postal_code;
                form.querySelector('[name="address"]').value = address.address;
                form.querySelector('[name="is_default"]').checked = address.is_default;
                
                // Update modal title
                document.querySelector('#addAddressModal .modal-title').textContent = 'Edit Address';
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('addAddressModal'));
                modal.show();
            } else {
                showMessage(data.message || 'Error loading address details', 'danger');
            }
        })
        .catch(error => {
            showMessage('Error loading address details', 'danger');
            console.error('Error:', error);
        });
}

function setupAddressModal() {
    const modal = document.getElementById('addAddressModal');
    if (!modal) return;
    
    // Reset form when modal is hidden
    modal.addEventListener('hidden.bs.modal', function() {
        const form = document.getElementById('addAddressForm');
        if (form) {
            form.reset();
            
            // Clear validation states
            form.querySelectorAll('.is-invalid').forEach(field => {
                field.classList.remove('is-invalid');
            });
            
            // Remove feedback messages
            form.querySelectorAll('.invalid-feedback').forEach(feedback => {
                feedback.remove();
            });
            
            // Reset form title
            const modalTitle = modal.querySelector('.modal-title');
            if (modalTitle) {
                modalTitle.textContent = 'Add New Address';
            }
            
            // Reset address ID field
            const addressIdField = form.querySelector('input[name="address_id"]');
            if (addressIdField) {
                addressIdField.value = '';
            }
        }
    });
    
    // Add animation when modal is shown
    modal.addEventListener('show.bs.modal', function() {
        const modalDialog = this.querySelector('.modal-dialog');
        if (modalDialog) {
            modalDialog.classList.add('animate__animated', 'animate__fadeInDown');
            setTimeout(() => {
                modalDialog.classList.remove('animate__animated', 'animate__fadeInDown');
            }, 1000);
        }
    });
    
    // Setup province and city dropdowns
    setupProvinceDropdown();
}

function setupProvinceDropdown() {
    const provinceSelect = document.querySelector('select[name="province"]');
    const citySelect = document.querySelector('select[name="city"]');
    
    if (!provinceSelect || !citySelect) return;
    
    // Disable city dropdown initially
    citySelect.disabled = true;
    
    // Load provinces
    fetch('ajax/get_provinces.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.provinces) {
                // Clear existing options except the first one
                while (provinceSelect.options.length > 1) {
                    provinceSelect.remove(1);
                }
                
                // Add new options
                data.provinces.forEach(province => {
                    const option = document.createElement('option');
                    option.value = province.id;
                    option.textContent = province.name;
                    provinceSelect.appendChild(option);
                });
                
                // Check if there's a selected province value
                const selectedProvinceId = provinceSelect.getAttribute('data-selected');
                if (selectedProvinceId) {
                    provinceSelect.value = selectedProvinceId;
                    loadCities(selectedProvinceId);
                }
            }
        })
        .catch(error => {
            console.error('Error loading provinces:', error);
        });
    
    // Event listener for province change
    provinceSelect.addEventListener('change', function() {
        const provinceId = this.value;
        if (provinceId) {
            loadCities(provinceId);
        } else {
            // Reset city dropdown if no province selected
            citySelect.disabled = true;
            while (citySelect.options.length > 1) {
                citySelect.remove(1);
            }
        }
    });
}

function loadCities(provinceId) {
    const citySelect = document.querySelector('select[name="city"]');
    if (!citySelect) return;
    
    // Show loading state
    citySelect.disabled = true;
    
    fetch(`ajax/get_cities.php?province_id=${provinceId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Enable city dropdown
            citySelect.disabled = false;
            
            if (data.success && data.cities) {
                // Clear existing options except the first one
                while (citySelect.options.length > 1) {
                    citySelect.remove(1);
                }
                
                // Add new options
                data.cities.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.id;
                    option.textContent = city.name;
                    citySelect.appendChild(option);
                });
                
                // Check if there's a selected city value
                const selectedCityId = citySelect.getAttribute('data-selected');
                if (selectedCityId) {
                    citySelect.value = selectedCityId;
                }
            }
        })
        .catch(error => {
            console.error('Error loading cities:', error);
        });
        profileImageContainer.addEventListener('mouseleave', function() {
            this.classList.remove('profile-image-hover');
            const uploadIcon = this.querySelector('.upload-icon-overlay');
            if (uploadIcon) {
                uploadIcon.classList.add('animate__animated', 'animate__fadeOut');
                setTimeout(() => {
                    uploadIcon.style.display = 'none';
                    uploadIcon.classList.remove('animate__animated', 'animate__fadeOut');
                }, 300);
            }
        });
    }
};