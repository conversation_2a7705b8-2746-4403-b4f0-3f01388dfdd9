<?php
$page = 'products';
$page_title = 'Add New Product';
require_once 'includes/header.php';

// Get all categories for dropdown
try {
    $stmt = $conn->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['alert_message'] = 'Database error: ' . $e->getMessage();
    $_SESSION['alert_type'] = 'danger';
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate input
        $product_name = trim($_POST['product_name'] ?? '');
        $category_id = (int)($_POST['category_id'] ?? 0);
        $description = trim($_POST['description'] ?? '');
        $price = (float)($_POST['price'] ?? 0);
        $stock = (int)($_POST['stock'] ?? 0);
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        // Generate slug
        $slug = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', $product_name));

        // Validation
        $errors = [];
        if (empty($product_name)) $errors[] = "Nama produk wajib diisi";
        if ($category_id <= 0) $errors[] = "Kategori wajib dipilih";
        if ($price <= 0) $errors[] = "Harga jual wajib diisi dengan nilai lebih dari 0";
        if ($stock < 0) $errors[] = "Stok tidak boleh negatif";

        if (empty($errors)) {
            // Begin transaction
            $conn->beginTransaction();

            // Handle image upload
            $image_name = null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                $max_size = 2 * 1024 * 1024; // 2MB

                if (!in_array($_FILES['image']['type'], $allowed_types)) {
                    throw new Exception("Hanya file JPG, PNG, dan GIF yang diperbolehkan");
                }

                if ($_FILES['image']['size'] > $max_size) {
                    throw new Exception("Ukuran file terlalu besar (maksimal 2MB)");
                }

                // Create uploads directory if it doesn't exist
                $upload_dir = '../uploads/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                // Generate unique filename
                $image_name = time() . '_' . preg_replace('/[^a-zA-Z0-9]/', '_', $_FILES['image']['name']);
                $target_file = $upload_dir . $image_name;

                // Upload file
                if (!move_uploaded_file($_FILES['image']['tmp_name'], $target_file)) {
                    throw new Exception("Gagal mengupload gambar");
                }
            }

            // Insert product to database
            $stmt = $conn->prepare("
                INSERT INTO products (
                    category_id, name, slug, description, price, stock, image, is_active
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?
                )
            ");

            $stmt->execute([
                $category_id,
                $product_name,
                $slug,
                $description,
                $price,
                $stock,
                $image_name,
                $is_active
            ]);

            $product_id = $conn->lastInsertId();

            // Update product search index if exists
            try {
                $stmt = $conn->prepare("
                    INSERT INTO product_search (product_id, name, description, category_name)
                    SELECT ?, ?, ?, c.name
                    FROM categories c WHERE c.category_id = ?
                ");
                $stmt->execute([$product_id, $product_name, $description, $category_id]);
            } catch (PDOException $e) {
                // Table might not exist, continue
            }

            // Commit transaction
            $conn->commit();

            $_SESSION['alert_message'] = "Produk berhasil ditambahkan!";
            $_SESSION['alert_type'] = 'success';
            header('Location: products.php');
            exit;

        } else {
            $_SESSION['alert_message'] = implode('<br>', $errors);
            $_SESSION['alert_type'] = 'danger';
        }
    } catch (PDOException $e) {
        $conn->rollBack();
        $_SESSION['alert_message'] = "Database error: " . $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    } catch (Exception $e) {
        $conn->rollBack();
        $_SESSION['alert_message'] = $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
}
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="fs-4">Add New Product</h1>
            <a href="products.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Products
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <form action="" method="POST" enctype="multipart/form-data">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="product_name" class="form-label required-field">Product Name</label>
                                <input type="text" class="form-control" id="product_name" name="product_name" value="<?php echo htmlspecialchars($_POST['product_name'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="category_id" class="form-label required-field">Category</label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['category_id']; ?>" <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['category_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['category_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="price" class="form-label required-field">Price (Rp)</label>
                                <input type="number" class="form-control" id="price" name="price" min="0" step="1000" value="<?php echo htmlspecialchars($_POST['price'] ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="stock" class="form-label required-field">Stock</label>
                                <input type="number" class="form-control" id="stock" name="stock" min="0" value="<?php echo htmlspecialchars($_POST['stock'] ?? '0'); ?>" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">Product Image</label>
                            <input type="file" class="form-control" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                            <div class="form-text">Format: JPG, PNG, GIF. Maks: 2MB.</div>
                            <img id="imagePreview" class="preview-image img-thumbnail mt-2">
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo (!isset($_POST['is_active']) || $_POST['is_active']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">Active</label>
                            <div class="form-text">Jika tidak dicentang, produk tidak akan ditampilkan di toko.</div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <button type="reset" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-undo me-2"></i>Reset
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Simpan Produk
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Preview image
    function previewImage(input) {
        const preview = document.getElementById('imagePreview');
        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }

            reader.readAsDataURL(input.files[0]);
        } else {
            preview.style.display = 'none';
        }
    }
</script>

<?php require_once 'includes/footer.php'; ?>
