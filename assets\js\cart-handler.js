/**
 * <PERSON><PERSON> - <PERSON>ript untuk menangani fitur Add to Cart
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');
    const alertContainer = document.getElementById('alert-container');
    const spinner = document.getElementById('spinner');
    
    if (addToCartButtons.length > 0) {
        // Add event listeners to all cart buttons
        addToCartButtons.forEach(button => {
            button.addEventListener('click', handleAddToCart);
        });
    }
    
    /**
     * Handle add to cart click
     */
    function handleAddToCart() {
        const productId = this.getAttribute('data-product-id');
        const productName = this.getAttribute('data-product-name');
        const productPrice = this.getAttribute('data-product-price');
        const button = this;
        
        // Show loading
        if (spinner) spinner.style.display = 'block';
        button.disabled = true;
        
        // Send AJAX request
        fetch('api/add-to-cart.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: 1
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Hide spinner
            if (spinner) spinner.style.display = 'none';
            
            // Handle response
            if (data.success === true) {
                // Success message with quantity
                const quantity = data.data && data.data.quantity ? data.data.quantity : 1;
                showAlert('success', `<i class="bi bi-check-circle-fill"></i> ${quantity} ${productName} telah ditambahkan ke keranjang.`);

                // Update cart count if available
                updateCartCount(data.data ? data.data.cart_count : null);
                
                // Add a small delay before enabling the button again
                setTimeout(() => {
                    button.disabled = false;
                    
                    // Optional: Provide visual feedback
                    button.innerHTML = '<i class="bi bi-check-circle-fill"></i> Ditambahkan';
                    setTimeout(() => {
                        button.innerHTML = '<i class="bi bi-cart-plus-fill"></i> Add to Cart';
                    }, 1500);
                }, 500);
            } else {
                // Error message
                showAlert('danger', `<i class="bi bi-exclamation-triangle-fill"></i> ${data.message || 'Gagal menambahkan ke keranjang'}`);
                
                // Re-enable button
                button.disabled = false;
            }
        })
        .catch(error => {
            // Hide spinner
            if (spinner) spinner.style.display = 'none';
            
            // Re-enable button
            button.disabled = false;
            
            // Show error message
            showAlert('danger', `<i class="bi bi-exclamation-triangle-fill"></i> Terjadi kesalahan saat menambahkan produk ke keranjang.`);
            console.error('Add to cart error:', error);
        });
    }
    
    /**
     * Update cart count badge
     */
    function updateCartCount(count) {
        const cartCountElement = document.querySelector('.cart-count');
        if (cartCountElement && count) {
            cartCountElement.textContent = count;
            cartCountElement.classList.remove('d-none');
        }
    }
    
    /**
     * Show alert message
     */
    function showAlert(type, message) {
        if (!alertContainer) return;
        
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        // Add to container
        alertContainer.appendChild(alert);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 300);
        }, 3000);
    }
});
