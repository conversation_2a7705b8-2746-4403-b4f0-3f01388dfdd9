// Products page functionality
document.addEventListener('DOMContentLoaded', () => {
    // Add event delegation for add to cart buttons
    document.addEventListener('click', async (event) => {
        const addButton = event.target.closest('.add-to-cart');
        if (addButton && !addButton.disabled) {
            event.preventDefault();
            
            // Get product info
            const card = addButton.closest('.card');
            const productId = addButton.dataset.productId;
            const productName = card.querySelector('.card-title').textContent;
            const price = parseFloat(card.querySelector('.price').textContent.replace(/[^0-9.-]+/g, ''));
            const imageUrl = card.querySelector('.card-img-top').src;
            
            // Add to cart
            try {
                addButton.disabled = true;
                const oldText = addButton.textContent;
                addButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Adding...';
                
                const response = await fetch('/tewuneed2/update_cart.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'add',
                        product_id: productId,
                        name: productName,
                        price: price,
                        image_url: imageUrl,
                        quantity: 1
                    })
                });
                
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                
                const data = await response.json();
                
                if (data.success) {
                    // Show success message
                    showAlert('Success', 'Product added to cart successfully!', 'success');
                    
                    // Update cart count
                    updateCartCount(data.cart_count);
                    
                    // Change button text temporarily
                    addButton.textContent = 'Added!';
                    setTimeout(() => {
                        addButton.disabled = false;
                        addButton.textContent = oldText;
                    }, 2000);
                } else {
                    throw new Error(data.message || 'Error adding product to cart');
                }
            } catch (error) {
                console.error('Error:', error);
                showAlert('Error', error.message, 'danger');
                addButton.disabled = false;
                addButton.textContent = 'Add to Cart';
            }
        }
    });
});

// Show alert message
function showAlert(title, message, type = 'success') {
    let alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.className = 'position-fixed top-0 end-0 p-3 z-index-1000';
        document.body.appendChild(alertContainer);
    }
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        <strong>${title}:</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    alertContainer.appendChild(alert);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        alert.classList.remove('show');
        setTimeout(() => alert.remove(), 150);
    }, 5000);
}

// Update cart count
function updateCartCount(count) {
    const cartCount = document.getElementById('cart-count');
    if (cartCount) {
        cartCount.textContent = count;
        cartCount.style.display = count > 0 ? 'inline' : 'none';
    }
}
