<?php
session_start();
require_once '../config/database.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    $conn = getConnection();
    $stmt = $conn->query("SELECT p.*, c.name as category_name
                         FROM products p
                         LEFT JOIN categories c ON p.category_id = c.category_id
                         ORDER BY p.created_at DESC
                         LIMIT 5");
    
    while ($product = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>{$product['product_id']}</td>";
        echo "<td>";
        if (!empty($product['image'])) {
            echo "<img src='../uploads/{$product['image']}' class='product-image-preview'
                       onerror=\"this.src='../assets/img/no-image.jpg'\">";
        } else {
            echo "<img src='../assets/img/no-image.jpg' class='product-image-preview'>";
        }
        echo "</td>";
        echo "<td>{$product['NAME']}</td>";
        echo "<td>{$product['category_name']}</td>";
        echo "<td>Rp" . number_format($product['price'], 0, ',', '.') . "</td>";
        echo "<td>{$product['stock']}</td>";
        echo "<td>
                <div class='btn-group'>
                    <button type='button' class='btn btn-sm btn-primary' onclick='editProduct({$product['product_id']})'>
                        <i class='fas fa-edit'></i>
                    </button>
                    <button type='button' class='btn btn-sm btn-danger' onclick='deleteProduct({$product['product_id']})'>
                        <i class='fas fa-trash'></i>
                    </button>
                </div>
              </td>";
        echo "</tr>";
    }
    
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    exit;
} 