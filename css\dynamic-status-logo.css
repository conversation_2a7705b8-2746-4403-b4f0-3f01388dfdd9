/**
 * Dynamic Status Logo CSS
 * Animations and styling for real-time status updates
 */

/* Status Badge Animations */
.status-badge {
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.status-badge.status-updating {
    transform: scale(1.05);
    animation: pulse 0.6s ease-in-out;
}

.status-badge.status-updated {
    animation: statusUpdated 1s ease-in-out;
    box-shadow: 0 0 15px rgba(0, 123, 255, 0.5);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1.05); }
}

@keyframes statusUpdated {
    0% { 
        transform: scale(1.05);
        box-shadow: 0 0 15px rgba(0, 123, 255, 0.5);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 0 25px rgba(0, 123, 255, 0.8);
    }
    100% { 
        transform: scale(1);
        box-shadow: none;
    }
}

/* Status Logo Styling */
.status-logo {
    font-size: 1.1em;
    margin-right: 4px;
    display: inline-block;
    animation: logoFloat 2s ease-in-out infinite;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
}

/* Status-specific logo animations */
.status-badge[data-status="pending"] .status-logo {
    animation: clockTick 1s ease-in-out infinite;
}

.status-badge[data-status="confirmed"] .status-logo {
    animation: checkBounce 0.6s ease-in-out;
}

.status-badge[data-status="processing"] .status-logo {
    animation: gearSpin 2s linear infinite;
}

.status-badge[data-status="shipped"] .status-logo {
    animation: truckMove 3s ease-in-out infinite;
}

.status-badge[data-status="delivered"] .status-logo {
    animation: celebrationBounce 0.8s ease-in-out;
}

@keyframes clockTick {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(5deg); }
    75% { transform: rotate(-5deg); }
}

@keyframes checkBounce {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

@keyframes gearSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes truckMove {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(3px); }
}

@keyframes celebrationBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-8px); }
    60% { transform: translateY(-4px); }
}

/* Notification Toast Styling */
.notification-toast {
    position: relative;
    margin-bottom: 10px;
    border-left: 4px solid #007bff;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-toast.alert-success {
    border-left-color: #28a745;
}

.notification-toast.alert-warning {
    border-left-color: #ffc107;
}

.notification-toast.alert-danger {
    border-left-color: #dc3545;
}

.notification-toast.alert-info {
    border-left-color: #17a2b8;
}

/* Notification Center Styling */
.notification-item {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
}

.notification-item.unread {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 8px;
    width: 8px;
    height: 8px;
    background-color: #007bff;
    border-radius: 50%;
    transform: translateY(-50%);
    animation: notificationPulse 2s ease-in-out infinite;
}

@keyframes notificationPulse {
    0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
    50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
}

.notification-item:hover {
    background-color: #f1f3f4;
    transform: translateX(5px);
}

.notification-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.notification-logo {
    font-size: 1.2em;
    margin-right: 8px;
}

.notification-title {
    font-weight: 600;
    flex-grow: 1;
    color: #495057;
}

.notification-time {
    font-size: 0.85em;
    color: #6c757d;
}

.notification-message {
    color: #6c757d;
    font-size: 0.9em;
    line-height: 1.4;
    margin-bottom: 10px;
}

.notification-actions {
    display: flex;
    gap: 8px;
}

/* Status Statistics Cards Animation */
.stat-count {
    transition: all 0.3s ease;
}

.stat-count.updated {
    animation: countUpdate 0.6s ease-in-out;
}

@keyframes countUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); color: #007bff; }
    100% { transform: scale(1); }
}

/* Loading States */
.status-loading {
    position: relative;
    opacity: 0.7;
}

.status-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .notification-toast {
        margin: 5px;
        font-size: 0.9em;
    }
    
    .notification-item {
        padding: 12px;
    }
    
    .notification-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .notification-time {
        margin-top: 4px;
    }
    
    .notification-actions {
        flex-direction: column;
    }
    
    .notification-actions .btn {
        width: 100%;
        margin-bottom: 5px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .notification-item.unread {
        background-color: #2d3748;
        border-left-color: #4299e1;
    }
    
    .notification-item:hover {
        background-color: #4a5568;
    }
    
    .notification-title {
        color: #e2e8f0;
    }
    
    .notification-message {
        color: #a0aec0;
    }
    
    .notification-time {
        color: #718096;
    }
}

/* Accessibility */
.status-badge:focus,
.notification-item:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .notification-toast,
    .notification-item.unread::before {
        display: none;
    }
    
    .status-badge {
        animation: none;
        transform: none;
        box-shadow: none;
    }
}
