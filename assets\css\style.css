/* Global Styles */
:root {
    --primary-color: #4CAF50;
    --secondary-color: #2196F3;
    --accent-color: #FF9800;
    --text-color: #333;
    --light-gray: #f8f9fa;
    --border-color: #dee2e6;
}

body {
    font-family: 'Poppins', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
}

/* Header Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 80px 0;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Category Cards */
.category-card {
    transition: transform 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card img {
    height: 200px;
    object-fit: cover;
}

/* Product Cards */
.product-card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-card img {
    height: 200px;
    object-fit: cover;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: darken(var(--primary-color), 10%);
    border-color: darken(var(--primary-color), 10%);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Features Section */
.feature-box {
    padding: 2rem;
    text-align: center;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.feature-box i {
    color: var(--primary-color);
}

/* Cart Styles */
.cart-item {
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
}

.cart-item img {
    width: 100px;
    height: 100px;
    object-fit: cover;
}

/* Checkout Styles */
.checkout-form {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Footer */
.footer {
    background: var(--light-gray);
    padding: 3rem 0;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-section {
        padding: 40px 0;
    }

    .feature-box {
        margin-bottom: 1rem;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alert Messages */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 1px solid var(--border-color);
    padding: 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(76, 175, 80, 0.25);
}

/* Badge Styles */
.badge {
    padding: 0.5em 1em;
    border-radius: 20px;
    font-weight: 500;
}

/* Pagination */
.pagination .page-link {
    color: var(--primary-color);
    border: none;
    margin: 0 2px;
    border-radius: 4px;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Login & Register Form Styles */
.form-container {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    max-width: 400px;
    margin: 2rem auto;
    position: relative;
    overflow: hidden;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.form-container h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-color);
    font-weight: 600;
}

.social-login {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    color: #666;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
    z-index: 1;
}

.divider span {
    background: white;
    padding: 0 1rem;
    position: relative;
    z-index: 2;
}

.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-group input {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: transparent;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group label {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: #999;
    transition: all 0.3s ease;
    pointer-events: none;
    background: white;
    padding: 0 0.5rem;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label {
    top: -0.5rem;
    left: 0.75rem;
    font-size: 0.875rem;
    color: var(--primary-color);
}

.btn-signup, .btn-signin {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-signup:hover, .btn-signin:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-signup:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.signup-link, .login-link {
    text-align: center;
    margin-top: 1.5rem;
    color: #666;
}

.signup-link a, .login-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.signup-link a:hover, .login-link a:hover {
    text-decoration: underline;
}

.error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    text-align: center;
    padding: 0.5rem;
    background: #f8d7da;
    border-radius: 4px;
    border: 1px solid #f5c6cb;
}

.welcome-container {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 3rem;
    border-radius: 15px;
    text-align: center;
    margin: 2rem auto;
    max-width: 400px;
}

.welcome-container h1 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.welcome-container p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.floating {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Responsive adjustments for login/register */
@media (max-width: 768px) {
    .form-container, .welcome-container {
        margin: 1rem;
        padding: 2rem;
    }

    .container {
        padding: 1rem;
    }
}