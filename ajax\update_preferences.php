<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    
    // Get form data
    $email_notifications = isset($_POST['email_notifications']) ? 1 : 0;
    $sms_notifications = isset($_POST['sms_notifications']) ? 1 : 0;
    $marketing_emails = isset($_POST['marketing_emails']) ? 1 : 0;
    $language = $_POST['language'] ?? 'id';
    $currency = $_POST['currency'] ?? 'IDR';
    
    // Validate language
    $allowed_languages = ['en', 'id'];
    if (!in_array($language, $allowed_languages)) {
        echo json_encode(['success' => false, 'message' => 'Invalid language selection']);
        exit;
    }
    
    // Validate currency
    $allowed_currencies = ['IDR', 'USD'];
    if (!in_array($currency, $allowed_currencies)) {
        echo json_encode(['success' => false, 'message' => 'Invalid currency selection']);
        exit;
    }
    
    // Update or insert preferences
    $stmt = $conn->prepare("
        INSERT INTO user_preferences 
        (user_id, email_notifications, sms_notifications, marketing_emails, language, currency, updated_at) 
        VALUES (?, ?, ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE 
        email_notifications = VALUES(email_notifications),
        sms_notifications = VALUES(sms_notifications),
        marketing_emails = VALUES(marketing_emails),
        language = VALUES(language),
        currency = VALUES(currency),
        updated_at = NOW()
    ");
    
    $result = $stmt->execute([
        $user_id,
        $email_notifications,
        $sms_notifications,
        $marketing_emails,
        $language,
        $currency
    ]);
    
    if ($result) {
        // Log activity
        $activity_stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, description, created_at) 
            VALUES (?, 'preferences_update', 'User preferences updated', NOW())
        ");
        $activity_stmt->execute([$user_id]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Preferences updated successfully',
            'data' => [
                'email_notifications' => $email_notifications,
                'sms_notifications' => $sms_notifications,
                'marketing_emails' => $marketing_emails,
                'language' => $language,
                'currency' => $currency
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update preferences']);
    }
    
} catch (Exception $e) {
    error_log("Update preferences error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while updating preferences']);
}
?>
