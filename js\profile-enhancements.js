/**
 * Profile Enhancements - Additional animations and interactions for the profile page
 */

// Enhanced address card animations and interactions
function setupEnhancedAddressCards() {
    const addressCards = document.querySelectorAll('.address-card');
    
    addressCards.forEach(card => {
        // Add hover effect
        card.addEventListener('mouseenter', () => {
            card.classList.add('shadow-lg', 'border-primary');
            card.style.transform = 'translateY(-5px)';
            card.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', () => {
            card.classList.remove('shadow-lg', 'border-primary');
            card.style.transform = 'translateY(0)';
        });
        
        // Add click animation for buttons
        const buttons = card.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Add ripple effect
                const ripple = document.createElement('span');
                ripple.classList.add('ripple-effect');
                this.appendChild(ripple);
                
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                ripple.style.width = ripple.style.height = `${size}px`;
                
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                ripple.style.left = `${x}px`;
                ripple.style.top = `${y}px`;
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    });
}

// Enhanced loading button function
function setLoadingState(button, isLoading) {
    if (!button) return;
    
    if (isLoading) {
        // Store original text
        button.dataset.originalText = button.innerHTML;
        
        // Create spinner
        const spinner = document.createElement('span');
        spinner.className = 'spinner-border spinner-border-sm me-2';
        spinner.setAttribute('role', 'status');
        spinner.setAttribute('aria-hidden', 'true');
        
        // Set loading text with spinner
        button.innerHTML = '';
        button.appendChild(spinner);
        button.appendChild(document.createTextNode(' Loading...'));
        
        // Disable button
        button.disabled = true;
        
        // Add loading animation
        button.classList.add('btn-loading');
    } else {
        // Restore original text
        if (button.dataset.originalText) {
            button.innerHTML = button.dataset.originalText;
            delete button.dataset.originalText;
        }
        
        // Enable button
        button.disabled = false;
        
        // Remove loading animation
        button.classList.remove('btn-loading');
        
        // Add success animation briefly
        button.classList.add('btn-success-pulse');
        setTimeout(() => {
            button.classList.remove('btn-success-pulse');
        }, 1000);
    }
}

// Enhanced profile photo upload
function setupEnhancedPhotoUpload() {
    const photoContainer = document.querySelector('.profile-photo-container');
    const photoInput = document.getElementById('profilePhotoInput');
    
    if (!photoContainer || !photoInput) return;
    
    // Create overlay if it doesn't exist
    if (!photoContainer.querySelector('.profile-photo-overlay')) {
        const overlay = document.createElement('div');
        overlay.className = 'profile-photo-overlay';
        overlay.innerHTML = '<i class="fas fa-camera"></i>';
        photoContainer.appendChild(overlay);
    }
    
    // Add click event to container
    photoContainer.addEventListener('click', () => {
        photoInput.click();
    });
    
    // Add drag and drop functionality
    photoContainer.addEventListener('dragover', (e) => {
        e.preventDefault();
        photoContainer.classList.add('border-primary');
    });
    
    photoContainer.addEventListener('dragleave', () => {
        photoContainer.classList.remove('border-primary');
    });
    
    photoContainer.addEventListener('drop', (e) => {
        e.preventDefault();
        photoContainer.classList.remove('border-primary');
        
        if (e.dataTransfer.files.length) {
            photoInput.files = e.dataTransfer.files;
            const event = new Event('change', { bubbles: true });
            photoInput.dispatchEvent(event);
        }
    });
}

// Enhanced tab switching with animations
function setupEnhancedProfileTabs() {
    const tabLinks = document.querySelectorAll('[data-tab]');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const tabId = this.getAttribute('data-tab');
            
            // Hide all tab contents with animation
            document.querySelectorAll('.tab-content').forEach(tab => {
                if (tab.classList.contains('active')) {
                    tab.classList.add('animate__animated', 'animate__fadeOut');
                    setTimeout(() => {
                        tab.classList.remove('active', 'animate__animated', 'animate__fadeOut');
                    }, 300);
                } else {
                    tab.classList.remove('active');
                }
            });
            
            // Remove active class from all nav items
            document.querySelectorAll('.list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to clicked nav item
            this.classList.add('active');
            
            // Show selected tab with animation after a short delay
            setTimeout(() => {
                const selectedTab = document.getElementById(tabId);
                if (selectedTab) {
                    selectedTab.classList.add('active', 'animate__animated', 'animate__fadeIn');
                    setTimeout(() => {
                        selectedTab.classList.remove('animate__animated', 'animate__fadeIn');
                    }, 500);
                }
                
                // Update URL hash without scrolling
                history.replaceState(null, null, '#' + tabId);
                
                // Load data for specific tabs when they're shown
                if (tabId === 'addresses') {
                    loadAddresses();
                }
            }, 300);
        });
    });
}

// Initialize all enhanced features
function initEnhancedProfile() {
    setupEnhancedAddressCards();
    setupEnhancedPhotoUpload();
    setupEnhancedProfileTabs();
    
    // Add ripple effect to all buttons on the page
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            // Skip if button is inside an address card (already handled)
            if (this.closest('.address-card')) return;
            
            // Add ripple effect
            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');
            this.appendChild(ripple);
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = `${size}px`;
            
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initEnhancedProfile();
});