/**
 * Order Status Synchronization System
 * Handles real-time sync between admin panel and user interface
 */

class OrderStatusSync {
    constructor() {
        this.syncKey = 'tewuneed_order_status_sync';
        this.lastCheckKey = 'tewuneed_last_status_check';
        this.checkInterval = 15000; // 15 seconds
        this.isAdmin = window.location.pathname.includes('/admin/');
        this.isUserPanel = window.location.pathname.includes('my-orders.php');
        this.intervalId = null;
        
        this.init();
    }

    init() {
        console.log(`[OrderStatusSync] Initializing ${this.isAdmin ? 'Admin' : 'User'} panel sync`);
        
        // Start monitoring for status changes
        this.startMonitoring();
        
        // Listen for localStorage changes (cross-tab communication)
        this.setupStorageListener();
        
        // Apply any pending updates on page load
        setTimeout(() => {
            this.processPendingUpdates();
        }, 1000);
    }

    startMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        this.intervalId = setInterval(() => {
            this.checkForUpdates();
        }, this.checkInterval);

        console.log(`[OrderStatusSync] Started monitoring every ${this.checkInterval/1000}s`);
    }

    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('[OrderStatusSync] Stopped monitoring');
        }
    }

    setupStorageListener() {
        window.addEventListener('storage', (e) => {
            if (e.key === this.syncKey) {
                console.log('[OrderStatusSync] Storage change detected');
                this.processPendingUpdates();
            }
        });

        // Also listen for custom events (same-tab communication)
        window.addEventListener('orderStatusUpdate', (e) => {
            console.log('[OrderStatusSync] Custom event received:', e.detail);
            this.processStatusUpdate(e.detail);
        });
    }

    // Admin panel: Store status update for user panel
    storeStatusUpdate(orderId, newStatus, adminNote = '', adminName = 'Admin') {
        const update = {
            order_id: orderId,
            new_status: newStatus,
            original_status: newStatus,
            admin_note: adminNote,
            admin_name: adminName,
            timestamp: Date.now(),
            source: 'admin_update',
            processed: false
        };

        let updates = JSON.parse(localStorage.getItem(this.syncKey) || '{}');
        updates[orderId] = update;
        localStorage.setItem(this.syncKey, JSON.stringify(updates));

        console.log(`[OrderStatusSync] Stored update for order ${orderId}: ${newStatus}`);

        // Trigger custom event for same-tab communication
        window.dispatchEvent(new CustomEvent('orderStatusUpdate', {
            detail: update
        }));

        return update;
    }

    // Check for new updates
    checkForUpdates() {
        const lastCheck = parseInt(localStorage.getItem(this.lastCheckKey) || '0');
        const updates = JSON.parse(localStorage.getItem(this.syncKey) || '{}');
        
        let hasNewUpdates = false;

        Object.keys(updates).forEach(orderId => {
            const update = updates[orderId];
            
            // Process updates that are newer than last check and not yet processed
            if (update.timestamp > lastCheck && !update.processed) {
                this.processStatusUpdate(update);
                
                // Mark as processed
                update.processed = true;
                hasNewUpdates = true;
            }
        });

        if (hasNewUpdates) {
            // Update stored data with processed flag
            localStorage.setItem(this.syncKey, JSON.stringify(updates));
            
            // Update last check time
            localStorage.setItem(this.lastCheckKey, Date.now().toString());
        }
    }

    processPendingUpdates() {
        const updates = JSON.parse(localStorage.getItem(this.syncKey) || '{}');
        
        Object.keys(updates).forEach(orderId => {
            const update = updates[orderId];
            
            // Process unprocessed updates
            if (!update.processed) {
                this.processStatusUpdate(update);
                update.processed = true;
            }
        });

        // Save updated data
        localStorage.setItem(this.syncKey, JSON.stringify(updates));
    }

    // Process individual status update
    processStatusUpdate(update) {
        if (this.isUserPanel) {
            this.updateUserInterface(update);
        } else if (this.isAdmin) {
            this.updateAdminInterface(update);
        }
    }

    // Update user interface (my-orders.php)
    updateUserInterface(update) {
        const statusElement = document.getElementById(`status-${update.order_id}`);
        
        if (!statusElement) {
            console.log(`[OrderStatusSync] Status element not found for order ${update.order_id}`);
            return;
        }

        // Status mapping for user interface
        const statusConfig = {
            'pending': {label: 'Pending', color: 'warning', icon: 'fa-clock'},
            'diproses': {label: 'Processing', color: 'info', icon: 'fa-cog'},
            'processing': {label: 'Processing', color: 'info', icon: 'fa-cog'},
            'dikirim': {label: 'Shipped', color: 'primary', icon: 'fa-truck'},
            'shipped': {label: 'Shipped', color: 'primary', icon: 'fa-truck'},
            'terkirim': {label: 'Delivered', color: 'success', icon: 'fa-check-circle'},
            'delivered': {label: 'Delivered', color: 'success', icon: 'fa-check-circle'},
            'dibatalkan': {label: 'Cancelled', color: 'danger', icon: 'fa-times-circle'},
            'cancelled': {label: 'Cancelled', color: 'danger', icon: 'fa-times-circle'}
        };

        const config = statusConfig[update.new_status] || statusConfig['pending'];

        // Update status badge with animation
        statusElement.className = `badge bg-${config.color} status-badge status-update-animation`;
        statusElement.innerHTML = `<i class="fas ${config.icon} me-1"></i>${config.label}`;

        // Update admin note if provided
        if (update.admin_note && update.admin_note.trim() !== '') {
            this.updateAdminNote(update.order_id, update.admin_note);
        }

        // Show notification
        this.showUserNotification(update);

        console.log(`[OrderStatusSync] Updated user interface for order ${update.order_id}: ${update.new_status}`);
    }

    // Update admin interface
    updateAdminInterface(update) {
        // For admin interface, we might want to update order lists or other elements
        console.log(`[OrderStatusSync] Admin interface update for order ${update.order_id}: ${update.new_status}`);
    }

    // Update admin note in user interface
    updateAdminNote(orderId, adminNote) {
        const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);
        if (!orderCard) return;

        const cardBody = orderCard.querySelector('.card-body');
        let noteSection = cardBody.querySelector('.latest-note-section');

        if (!noteSection) {
            noteSection = document.createElement('div');
            noteSection.className = 'mb-3 latest-note-section';
            cardBody.appendChild(noteSection);
        }

        noteSection.innerHTML = `
            <small class="text-muted">Latest Update</small>
            <div class="small text-info">
                <i class="fas fa-comment-alt me-1"></i>
                ${adminNote}
            </div>
            <small class="text-muted">
                Updated: ${new Date().toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                })}
            </small>
        `;
    }

    // Show notification to user
    showUserNotification(update) {
        const notification = document.getElementById('statusNotification');
        const message = document.getElementById('notificationMessage');

        if (!notification || !message) return;

        const statusLabels = {
            'pending': 'Pending',
            'diproses': 'Processing',
            'processing': 'Processing',
            'dikirim': 'Shipped',
            'shipped': 'Shipped',
            'terkirim': 'Delivered',
            'delivered': 'Delivered',
            'dibatalkan': 'Cancelled',
            'cancelled': 'Cancelled'
        };

        const statusLabel = statusLabels[update.new_status] || 'Updated';
        message.innerHTML = `Order #${update.order_id} status updated to <strong>${statusLabel}</strong>`;

        notification.style.display = 'block';
        notification.classList.add('show');

        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.style.display = 'none';
            }, 150);
        }, 5000);
    }

    // Clean up old updates (call periodically)
    cleanupOldUpdates() {
        const updates = JSON.parse(localStorage.getItem(this.syncKey) || '{}');
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        
        let cleaned = false;
        Object.keys(updates).forEach(orderId => {
            if (updates[orderId].timestamp < oneHourAgo) {
                delete updates[orderId];
                cleaned = true;
            }
        });

        if (cleaned) {
            localStorage.setItem(this.syncKey, JSON.stringify(updates));
            console.log('[OrderStatusSync] Cleaned up old updates');
        }
    }

    // Destroy instance
    destroy() {
        this.stopMonitoring();
        console.log('[OrderStatusSync] Instance destroyed');
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize on relevant pages
    const isRelevantPage = window.location.pathname.includes('my-orders.php') || 
                          window.location.pathname.includes('admin/order') ||
                          window.location.pathname.includes('admin/order_management.php');
    
    if (isRelevantPage) {
        window.orderStatusSync = new OrderStatusSync();
        
        // Clean up old updates every 30 minutes
        setInterval(() => {
            window.orderStatusSync.cleanupOldUpdates();
        }, 30 * 60 * 1000);
    }
});

// Export for manual usage
window.OrderStatusSync = OrderStatusSync;
