<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    $user_id = $_SESSION['user_id'];
    $address_id = $_POST['address_id'] ?? 0;
    
    if (!$address_id) {
        echo json_encode(['success' => false, 'message' => 'Address ID is required']);
        exit;
    }
    
    // Check if address belongs to user
    $stmt = $conn->prepare("SELECT * FROM user_addresses WHERE address_id = ? AND user_id = ?");
    $stmt->execute([$address_id, $user_id]);
    $address = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$address) {
        echo json_encode(['success' => false, 'message' => 'Address not found']);
        exit;
    }
    
    // Don't allow deletion of default address if it's the only address
    if ($address['is_default']) {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_addresses WHERE user_id = ?");
        $stmt->execute([$user_id]);
        $total_addresses = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($total_addresses == 1) {
            echo json_encode(['success' => false, 'message' => 'Cannot delete the only address']);
            exit;
        }
    }
    
    // Start transaction
    $conn->beginTransaction();
    
    // Delete the address
    $stmt = $conn->prepare("DELETE FROM user_addresses WHERE address_id = ? AND user_id = ?");
    $result = $stmt->execute([$address_id, $user_id]);
    
    if ($result) {
        // If deleted address was default, set another address as default
        if ($address['is_default']) {
            $stmt = $conn->prepare("
                UPDATE user_addresses 
                SET is_default = 1 
                WHERE user_id = ? 
                ORDER BY created_at ASC 
                LIMIT 1
            ");
            $stmt->execute([$user_id]);
        }
        
        $conn->commit();
        
        // Log activity
        $activity_stmt = $conn->prepare("
            INSERT INTO user_activities (user_id, activity_type, description, created_at) 
            VALUES (?, 'address_delete', ?, NOW())
        ");
        $activity_stmt->execute([
            $user_id,
            "Deleted address: {$address['label']}"
        ]);
        
        echo json_encode([
            'success' => true, 
            'message' => 'Address deleted successfully'
        ]);
    } else {
        $conn->rollBack();
        echo json_encode(['success' => false, 'message' => 'Failed to delete address']);
    }
    
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    error_log("Delete address error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while deleting address']);
}
?>
