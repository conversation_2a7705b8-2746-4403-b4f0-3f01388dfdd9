/**
 * Local Cart - Alternative shopping cart implementation using localStorage
 */

// Initialize cart namespace
const TewuCart = {
    // Get cart from localStorage
    getCart: function() {
        const cart = localStorage.getItem('tewuneed_cart');
        return cart ? JSON.parse(cart) : [];
    },
    
    // Save cart to localStorage
    saveCart: function(cart) {
        localStorage.setItem('tewuneed_cart', JSON.stringify(cart));
    },
    
    // Add product to cart
    addProduct: function(product) {
        const cart = this.getCart();
        
        // Check if product already exists
        const existingItemIndex = cart.findIndex(item => item.product_id === product.product_id);
        
        if (existingItemIndex !== -1) {
            // Update quantity if product already exists
            cart[existingItemIndex].quantity += product.quantity;
        } else {
            // Add new product
            cart.push(product);
        }
        
        this.saveCart(cart);
        this.updateCartCount();
        
        return cart;
    },
    
    // Remove product from cart
    removeProduct: function(productId) {
        let cart = this.getCart();
        cart = cart.filter(item => item.product_id !== productId);
        this.saveCart(cart);
        this.updateCartCount();
        
        return cart;
    },
    
    // Update product quantity
    updateQuantity: function(productId, quantity) {
        const cart = this.getCart();
        const item = cart.find(item => item.product_id === productId);
        
        if (item) {
            item.quantity = quantity;
        }
        
        this.saveCart(cart);
        this.updateCartCount();
        
        return cart;
    },
    
    // Clear cart
    clearCart: function() {
        localStorage.removeItem('tewuneed_cart');
        this.updateCartCount();
    },
    
    // Get cart count
    getCartCount: function() {
        const cart = this.getCart();
        return cart.reduce((count, item) => count + item.quantity, 0);
    },
    
    // Update cart count in UI
    updateCartCount: function() {
        const count = this.getCartCount();
        const countElement = document.querySelector('.cart-count');
        
        if (countElement) {
            countElement.textContent = count;
            countElement.style.display = count > 0 ? 'inline-block' : 'none';
        }
    },
    
    // Calculate cart total
    getCartTotal: function() {
        const cart = this.getCart();
        return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    },
    
    // Initialize cart
    init: function() {
        this.updateCartCount();
        
        // Add event listener for add to cart buttons
        document.querySelectorAll('.add-to-cart-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const productId = parseInt(this.getAttribute('data-product-id'));
                const productName = this.getAttribute('data-product-name');
                const productPrice = parseInt(this.getAttribute('data-product-price'));
                const productImage = this.getAttribute('data-product-image') || 'default-product.jpg';
                
                // Create product object
                const product = {
                    product_id: productId,
                    name: productName,
                    price: productPrice,
                    quantity: 1,
                    image: productImage
                };
                
                // Add to cart
                TewuCart.addProduct(product);
                
                // Show success message
                const message = `<i class="bi bi-check-circle-fill"></i> ${productName} telah ditambahkan ke keranjang.`;
                TewuCart.showAlert('success', message);
            });
        });
    },
    
    // Show alert message
    showAlert: function(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const alertContainer = document.getElementById('alert-container');
        if (alertContainer) {
            alertContainer.innerHTML = alertHtml;
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => {
                        alertContainer.innerHTML = '';
                    }, 150);
                }
            }, 5000);
        }
    }
};

// Initialize cart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    TewuCart.init();
});
