<?php
header('Content-Type: application/json');
require_once '../includes/firebase_auth.php';

// Fungsi untuk memeriksa apakah email ada di database
function checkEmailExists($email) {
    try {
        $conn = getDbConnection();
        $stmt = $conn->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        return !empty($user);
    } catch (PDOException $e) {
        error_log('Error checking email: ' . $e->getMessage());
        return false;
    }
}

// Fungsi untuk memeriksa apakah email terdaftar di Firebase
function checkEmailFirebase($email) {
    // Karena keterbatasan API Firebase, kita hanya bisa memperkirakan
    // berdasarkan struktur aplikasi
    return true; // Asumsi email terdaftar
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $json = file_get_contents('php://input');
    $data = json_decode($json, true);
    
    if (isset($data['email'])) {
        $email = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
        $existsInDb = checkEmailExists($email);
        
        echo json_encode([
            'existsInDatabase' => $existsInDb,
            'message' => $existsInDb 
                ? 'Akun ditemukan dalam database lokal.' 
                : 'Akun tidak ditemukan dalam database lokal.'
        ]);
    } else {
        http_response_code(400);
        echo json_encode([
            'error' => 'Email tidak diberikan',
            'message' => 'Parameter email diperlukan'
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'error' => 'Method not allowed',
        'message' => 'Hanya metode POST yang diizinkan'
    ]);
}
?>
