/**
 * Order Management JavaScript Functions
 * Handles real-time updates, quick actions, and UI interactions
 */

class OrderManagement {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.startRealTimeUpdates();
    }

    init() {
        console.log('Order Management initialized');
        this.loadOrderStats();
    }

    setupEventListeners() {
        // Bulk actions
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('order-checkbox')) {
                this.updateBulkActions();
            }
        });

        // Select all checkbox
        const selectAllCheckbox = document.getElementById('selectAllOrders');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.selectAllOrders(e.target.checked);
            });
        }

        // Quick action buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn-generate-tracking')) {
                const orderId = e.target.closest('.btn-generate-tracking').dataset.orderId;
                this.generateTracking(orderId);
            }

            if (e.target.closest('.btn-notify-customer')) {
                const orderId = e.target.closest('.btn-notify-customer').dataset.orderId;
                this.showNotifyModal(orderId);
            }

            if (e.target.closest('.btn-bulk-update')) {
                this.showBulkUpdateModal();
            }
        });
    }

    /**
     * Load order statistics
     */
    async loadOrderStats() {
        try {
            const response = await fetch('ajax/quick_actions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ action: 'get_order_stats' })
            });

            const data = await response.json();
            if (data.success) {
                this.updateStatsCards(data.stats, data.summary);
            }
        } catch (error) {
            console.error('Error loading stats:', error);
        }
    }

    /**
     * Update statistics cards
     */
    updateStatsCards(stats, summary) {
        // Update status cards
        Object.keys(stats).forEach(status => {
            const card = document.querySelector(`[data-status="${status}"] .stat-count`);
            if (card) {
                card.textContent = stats[status].count;
            }
        });

        // Update summary cards
        const todayOrdersCard = document.querySelector('.today-orders-count');
        if (todayOrdersCard) {
            todayOrdersCard.textContent = summary.today_orders;
        }

        const todayRevenueCard = document.querySelector('.today-revenue-amount');
        if (todayRevenueCard) {
            todayRevenueCard.textContent = 'Rp ' + this.formatNumber(summary.today_revenue);
        }

        const pendingOrdersCard = document.querySelector('.pending-orders-count');
        if (pendingOrdersCard) {
            pendingOrdersCard.textContent = summary.pending_orders;
        }
    }

    /**
     * Generate tracking number for order
     */
    async generateTracking(orderId) {
        if (!confirm('Generate new tracking number for this order?')) {
            return;
        }

        try {
            const response = await fetch('ajax/quick_actions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'generate_tracking',
                    order_id: orderId
                })
            });

            const data = await response.json();
            if (data.success) {
                this.showAlert('success', data.message);
                
                // Update tracking number in table
                const trackingCell = document.querySelector(`tr[data-order-id="${orderId}"] .tracking-number`);
                if (trackingCell) {
                    trackingCell.innerHTML = `<code>${data.tracking_number}</code>`;
                }
                
                // Refresh page after 2 seconds
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                this.showAlert('danger', data.message);
            }
        } catch (error) {
            console.error('Error generating tracking:', error);
            this.showAlert('danger', 'Error generating tracking number');
        }
    }

    /**
     * Show notify customer modal
     */
    showNotifyModal(orderId) {
        const modal = document.getElementById('notifyCustomerModal');
        if (!modal) {
            this.createNotifyModal();
        }

        document.getElementById('notifyOrderId').value = orderId;
        new bootstrap.Modal(document.getElementById('notifyCustomerModal')).show();
    }

    /**
     * Create notify customer modal
     */
    createNotifyModal() {
        const modalHTML = `
        <div class="modal fade" id="notifyCustomerModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Notify Customer</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" id="notifyOrderId">
                        <div class="mb-3">
                            <label class="form-label">Message</label>
                            <textarea id="notifyMessage" class="form-control" rows="4" placeholder="Enter message for customer..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="orderManagement.sendNotification()">Send Notification</button>
                    </div>
                </div>
            </div>
        </div>`;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * Send notification to customer
     */
    async sendNotification() {
        const orderId = document.getElementById('notifyOrderId').value;
        const message = document.getElementById('notifyMessage').value.trim();

        if (!message) {
            this.showAlert('warning', 'Please enter a message');
            return;
        }

        try {
            const response = await fetch('ajax/quick_actions.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'notify_customer',
                    order_id: orderId,
                    message: message
                })
            });

            const data = await response.json();
            if (data.success) {
                this.showAlert('success', data.message);
                bootstrap.Modal.getInstance(document.getElementById('notifyCustomerModal')).hide();
                document.getElementById('notifyMessage').value = '';
            } else {
                this.showAlert('danger', data.message);
            }
        } catch (error) {
            console.error('Error sending notification:', error);
            this.showAlert('danger', 'Error sending notification');
        }
    }

    /**
     * Select all orders
     */
    selectAllOrders(checked) {
        const checkboxes = document.querySelectorAll('.order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
        this.updateBulkActions();
    }

    /**
     * Update bulk actions visibility
     */
    updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.order-checkbox:checked');
        const bulkActionsDiv = document.getElementById('bulkActions');
        
        if (bulkActionsDiv) {
            if (checkedBoxes.length > 0) {
                bulkActionsDiv.style.display = 'block';
                document.getElementById('selectedCount').textContent = checkedBoxes.length;
            } else {
                bulkActionsDiv.style.display = 'none';
            }
        }
    }

    /**
     * Show bulk update modal
     */
    showBulkUpdateModal() {
        const checkedBoxes = document.querySelectorAll('.order-checkbox:checked');
        if (checkedBoxes.length === 0) {
            this.showAlert('warning', 'Please select orders to update');
            return;
        }

        const modal = document.getElementById('bulkUpdateModal');
        if (!modal) {
            this.createBulkUpdateModal();
        }

        new bootstrap.Modal(document.getElementById('bulkUpdateModal')).show();
    }

    /**
     * Create bulk update modal
     */
    createBulkUpdateModal() {
        // This would need to be implemented based on available statuses
        // For now, just show a simple alert
        this.showAlert('info', 'Bulk update feature will be implemented');
    }

    /**
     * Start real-time updates
     */
    startRealTimeUpdates() {
        // Update stats every 30 seconds
        setInterval(() => {
            this.loadOrderStats();
        }, 30000);

        // Check for new orders every 10 seconds
        setInterval(() => {
            this.checkForNewOrders();
        }, 10000);
    }

    /**
     * Check for new orders
     */
    async checkForNewOrders() {
        // This would implement checking for new orders
        // and showing notifications
    }

    /**
     * Show alert message
     */
    showAlert(type, message) {
        const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>`;

        const alertContainer = document.getElementById('alertContainer') || document.querySelector('.container-fluid');
        alertContainer.insertAdjacentHTML('afterbegin', alertHTML);

        // Auto dismiss after 5 seconds
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                bootstrap.Alert.getOrCreateInstance(alert).close();
            }
        }, 5000);
    }

    /**
     * Format number with thousand separators
     */
    formatNumber(num) {
        return new Intl.NumberFormat('id-ID').format(num);
    }

    /**
     * Refresh orders table
     */
    refreshOrders() {
        window.location.reload();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.orderManagement = new OrderManagement();
});

// Global functions for backward compatibility
function updateStatus(orderId) {
    document.getElementById('updateOrderId').value = orderId;
    new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
}

function viewOrder(orderId) {
    fetch(`order_details.php?id=${orderId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('orderDetailsContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('viewOrderModal')).show();
        })
        .catch(error => {
            console.error('Error loading order details:', error);
            alert('Error loading order details');
        });
}

function refreshOrders() {
    window.location.reload();
}
