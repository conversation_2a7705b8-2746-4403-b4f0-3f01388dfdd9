<?php
session_start();
require_once "../includes/db_connect.php";

// If already logged in as admin, redirect to dashboard
if (isset($_SESSION["role"]) && $_SESSION["role"] === "admin") {
    header("Location: dashboard.php");
    exit;
}

$error = "";
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $email = trim($_POST["email"] ?? "");
    $password = $_POST["password"] ?? "";

    if (!empty($email) && !empty($password)) {
        try {
            // Check admin user in database
            $stmt = $conn->prepare("SELECT user_id, username, email, password, full_name, role FROM users WHERE email = ? AND role = ?");
            $stmt->execute([$email, "admin"]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            // Check if admin user exists and password is correct
            // For development: also check plain text password
            $password_valid = false;
            if ($admin && isset($admin["password"])) {
                // Try hashed password first
                if (password_verify($password, $admin["password"])) {
                    $password_valid = true;
                }
                // Fallback: check plain text password for development
                elseif ($password === $admin["password"]) {
                    $password_valid = true;

                    // Update password to hashed version for security
                    try {
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        $update_stmt = $conn->prepare("UPDATE users SET password = ? WHERE user_id = ?");
                        $update_stmt->execute([$hashed_password, $admin["user_id"]]);
                    } catch (Exception $hash_error) {
                        error_log("Password hash update error: " . $hash_error->getMessage());
                    }
                }
            }

            if ($password_valid) {
                // Login successful
                $_SESSION["user_id"] = $admin["user_id"];
                $_SESSION["username"] = $admin["username"];
                $_SESSION["email"] = $admin["email"];
                $_SESSION["full_name"] = $admin["full_name"] ?? $admin["username"];
                $_SESSION["role"] = $admin["role"];
                $_SESSION["admin_logged_in"] = true;

                // Log admin login activity (with error handling)
                try {
                    $activity_stmt = $conn->prepare("
                        INSERT INTO user_activities (user_id, activity_type, description, ip_address, user_agent, created_at)
                        VALUES (?, ?, ?, ?, ?, NOW())
                    ");
                    $activity_stmt->execute([
                        $admin["user_id"],
                        "admin_login",
                        "Admin logged in to dashboard",
                        $_SERVER["REMOTE_ADDR"] ?? "unknown",
                        $_SERVER["HTTP_USER_AGENT"] ?? "unknown"
                    ]);
                } catch (Exception $activity_error) {
                    // Log activity error but don't prevent login
                    error_log("Admin login activity logging error: " . $activity_error->getMessage());
                }

                header("Location: dashboard.php");
                exit;
            } else {
                $error = "Invalid email or password";
                error_log("Admin login failed for email: " . $email . " - User found: " . ($admin ? 'Yes' : 'No'));
            }
        } catch (Exception $e) {
            $error = "Login error: " . $e->getMessage();
            error_log("Admin login error: " . $e->getMessage());
        }
    } else {
        $error = "Please fill in all fields";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - TEWUNEED</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 3rem;
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0,0,0,0.175);
            width: 100%;
            max-width: 450px;
        }
        .login-title {
            text-align: center;
            margin-bottom: 2rem;
        }
        .login-title h2 {
            color: #495057;
            font-weight: 700;
        }
        .btn-admin {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem;
            font-weight: 600;
        }
        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-title">
            <i class="fas fa-crown fa-3x text-warning mb-3"></i>
            <h2>TEWUNEED ADMIN</h2>
            <p class="text-muted">Administrator Dashboard</p>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="mb-3">
                <label for="email" class="form-label">
                    <i class="fas fa-envelope me-2"></i>Email Address
                </label>
                <input type="email" class="form-control" id="email" name="email" required autofocus>
            </div>
            <div class="mb-4">
                <label for="password" class="form-label">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>
            <button type="submit" class="btn btn-admin btn-primary w-100">
                <i class="fas fa-sign-in-alt me-2"></i>Login to Dashboard
            </button>
        </form>

        <hr class="my-4">
        <div class="text-center">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                Default Admin: <EMAIL> / admin123
            </small>
        </div>

        <div class="text-center mt-3">
            <a href="../index.php" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-home me-1"></i>Back to Website
            </a>
        </div>
    </div>
</body>
</html>