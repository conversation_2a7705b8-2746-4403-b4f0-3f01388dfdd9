/**
 * Notification Integration Script
 * Integrates the new notification system with existing cart functionality
 */

(function() {
    'use strict';
    
    // Wait for DOM to be ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeNotificationSystem();
    });
    
    function initializeNotificationSystem() {
        console.log('Initializing notification system...');
        
        // Create notification manager if it doesn't exist
        if (!window.notificationManager && typeof NotificationManager === 'function') {
            window.notificationManager = new NotificationManager();
            console.log('NotificationManager instance created');
        }
        
        // Override existing showToast functions if they exist
        if (window.showToast) {
            window.originalShowToast = window.showToast;
        }
        
        // Enhanced showToast function
        window.showToast = function(message, type = 'success', options = {}) {
            // Skip error notifications if they contain blocked messages
            if (type === 'danger' || type === 'error') {
                const blockedMessages = [
                    'Error adding to cart',
                    'Error adding product to cart',
                    'Gagal menambahkan produk ke keranjang',
                    'Please try again',
                    'Database error occurred',
                    'Gagal mereservasi stok'
                ];
                
                const shouldBlock = blockedMessages.some(blocked => 
                    message && message.toLowerCase().includes(blocked.toLowerCase())
                );
                
                if (shouldBlock) {
                    console.log('Blocked error notification:', message);
                    return;
                }
            }
            
            if (window.notificationManager) {
                window.notificationManager.addNotification(message, type, options);
            } else if (window.originalShowToast) {
                window.originalShowToast(message, type);
            } else {
                // Fallback to console if nothing else available
                console.log(`Notification: [${type.toUpperCase()}] ${message}`);
            }
        };
        
        // Enhanced addToCart functions for different pages
        enhanceAddToCartFunctions();
        
        // Add cart notification helper
        window.addToCartNotification = function(productName, quantity, options = {}) {
            if (window.notificationManager) {
                window.notificationManager.addCartNotification(productName, quantity, options);
            } else {
                showToast(`${quantity} ${productName} telah ditambahkan ke keranjang`, 'success', options);
            }
        };
        
        // Add notification for stock warnings
        window.showStockWarning = function(productName, availableStock) {
            const message = `Stok ${productName} terbatas! Tersisa ${availableStock} item`;
            showToast(message, 'warning', { duration: 4000 });
        };
        
        // Add notification for cart updates
        window.showCartUpdate = function(action, productName, quantity) {
            let message = '';
            let type = 'info';
            
            switch(action) {
                case 'updated':
                    message = `${productName} diperbarui menjadi ${quantity} item`;
                    type = 'info';
                    break;
                case 'removed':
                    message = `${productName} dihapus dari keranjang`;
                    type = 'warning';
                    break;
                case 'cleared':
                    message = 'Keranjang telah dikosongkan';
                    type = 'info';
                    break;
                default:
                    message = `Keranjang diperbarui`;
                    type = 'info';
            }
            
            showToast(message, type);
        };
        
        // Add toggle for enabling/disabling notifications
        window.toggleNotifications = function(enable) {
            if (window.notificationManager) {
                if (enable) {
                    window.notificationManager.enable();
                } else {
                    window.notificationManager.disable();
                }
                return true;
            }
            return false;
        };
        
        console.log('Notification system initialized successfully');
    }
    
    function enhanceAddToCartFunctions() {
        // Enhance existing addToCart functions to use new notification system
        
        // For pages that use localStorage cart
        if (typeof addToCart === 'function') {
            const originalAddToCart = addToCart;
            window.addToCart = function(id, name, price, image, stock) {
                // Call original function
                const result = originalAddToCart.apply(this, arguments);
                
                // Add enhanced notification
                if (name) {
                    addToCartNotification(name, 1);
                }
                
                return result;
            };
        }
        
        // Enhance AJAX cart functions
        enhanceAjaxCartFunctions();
    }
    
    function enhanceAjaxCartFunctions() {
        // Override fetch for add to cart requests
        const originalFetch = window.fetch;
        
        window.fetch = function(url, options) {
            // Check if this is an add to cart request
            if (url && (
                url.includes('add_to_cart.php') || 
                url.includes('add-to-cart.php') ||
                url.includes('cart/add.php')
            )) {
                return originalFetch.apply(this, arguments)
                    .then(response => {
                        // Clone response to read it
                        const clonedResponse = response.clone();
                        
                        // Try to parse JSON and enhance notification
                        clonedResponse.json().then(data => {
                            if (data.success && data.product_name) {
                                const quantity = data.quantity || 1;
                                addToCartNotification(data.product_name, quantity);
                            }
                        }).catch(() => {
                            // Ignore JSON parse errors
                        });
                        
                        return response;
                    });
            }
            
            return originalFetch.apply(this, arguments);
        };
    }
    
    // Add utility functions for common notification scenarios
    window.NotificationUtils = {
        // Cart related notifications
        cart: {
            added: (productName, quantity) => addToCartNotification(productName, quantity),
            updated: (productName, quantity) => showCartUpdate('updated', productName, quantity),
            removed: (productName) => showCartUpdate('removed', productName),
            cleared: () => showCartUpdate('cleared')
        },
        
        // Stock related notifications
        stock: {
            warning: (productName, stock) => showStockWarning(productName, stock),
            outOfStock: (productName) => showToast(`${productName} sedang habis stok`, 'danger'),
            lowStock: (productName, stock) => showToast(`${productName} stok tinggal ${stock} item`, 'warning')
        },
        
        // Order related notifications
        order: {
            placed: (orderNumber) => showToast(`Pesanan #${orderNumber} berhasil dibuat`, 'success'),
            updated: (orderNumber, status) => showToast(`Pesanan #${orderNumber} ${status}`, 'info'),
            cancelled: (orderNumber) => showToast(`Pesanan #${orderNumber} dibatalkan`, 'warning')
        },
        
        // User related notifications
        user: {
            loggedIn: (username) => showToast(`Selamat datang, ${username}!`, 'success'),
            loggedOut: () => showToast('Anda telah logout', 'info'),
            profileUpdated: () => showToast('Profil berhasil diperbarui', 'success')
        },
        
        // General notifications
        general: {
            success: (message) => showToast(message, 'success'),
            error: (message) => showToast(message, 'danger'),
            warning: (message) => showToast(message, 'warning'),
            info: (message) => showToast(message, 'info')
        }
    };
    
    // Add keyboard shortcuts for testing (only in development)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        document.addEventListener('keydown', function(e) {
            // Ctrl + Shift + N = Test notification
            if (e.ctrlKey && e.shiftKey && e.key === 'N') {
                e.preventDefault();
                addToCartNotification('Test Product', Math.floor(Math.random() * 5) + 1);
            }
            
            // Ctrl + Shift + C = Clear notifications
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                if (window.notificationManager) {
                    window.notificationManager.clearAll();
                }
            }
        });
        
        console.log('Development shortcuts enabled:');
        console.log('- Ctrl+Shift+N: Test notification');
        console.log('- Ctrl+Shift+C: Clear notifications');
    }
    
    // Auto-include CSS if not already included
    function autoIncludeCSS() {
        const cssFiles = [
            'css/notifications.css'
        ];
        
        cssFiles.forEach(cssFile => {
            if (!document.querySelector(`link[href*="${cssFile}"]`)) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = cssFile;
                document.head.appendChild(link);
            }
        });
    }
    
    // Auto-include Toastify if not already included
    function autoIncludeToastify() {
        if (typeof Toastify === 'undefined') {
            // Include Toastify CSS
            if (!document.querySelector('link[href*="toastify"]')) {
                const cssLink = document.createElement('link');
                cssLink.rel = 'stylesheet';
                cssLink.href = 'https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css';
                document.head.appendChild(cssLink);
            }
            
            // Include Toastify JS
            if (!document.querySelector('script[src*="toastify"]')) {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/toastify-js';
                script.onload = function() {
                    console.log('Toastify loaded automatically');
                };
                document.head.appendChild(script);
            }
        }
    }
    
    // Initialize auto-includes
    autoIncludeCSS();
    autoIncludeToastify();
    
})();
