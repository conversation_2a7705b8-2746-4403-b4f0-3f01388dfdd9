/**
 * TewuNeed Admin Core JavaScript
 * File ini berisi fungsi-fungsi JavaScript inti untuk panel admin
 */

// Objek global untuk fungsi-fungsi admin
const TewuNeedAdmin = {
    /**
     * Inisialisasi fungsi-fungsi admin
     */
    init: function() {
        // Perbaiki masalah navigasi menu
        this.fixNavigation();
        
        // Inisialisasi notifikasi
        this.initNotifications();
        
        // Tambahkan event listener untuk tombol-tombol umum
        this.setupEventListeners();
        
        console.log('TewuNeed Admin Core initialized');
    },
    
    /**
     * Perbaiki masalah navigasi dengan menambahkan event handler tambahan
     */
    fixNavigation: function() {
        // Perbaiki navigasi menu sidebar
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const href = this.getAttribute('href');
                if (href && href !== '#') {
                    window.location.href = href;
                }
            });
        });
        
        // Perbaiki tombol edit pada tabel
        document.querySelectorAll('a.edit-button, button.edit-button').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const href = this.getAttribute('href') || this.getAttribute('data-href');
                if (href && href !== '#') {
                    window.location.href = href;
                }
            });
        });
    },
    
    /**
     * Inisialisasi sistem notifikasi
     */
    initNotifications: function() {
        // Cek dan tampilkan notifikasi dari session storage
        const notification = sessionStorage.getItem('admin_notification');
        if (notification) {
            try {
                const notifData = JSON.parse(notification);
                this.showNotification(notifData.message, notifData.type);
                sessionStorage.removeItem('admin_notification');
            } catch (e) {
                console.error('Error parsing notification data', e);
            }
        }
        
        // Tambahkan fade out otomatis untuk alert yang ada
        document.querySelectorAll('.alert:not(.alert-permanent)').forEach(alert => {
            setTimeout(() => {
                alert.classList.add('fade');
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 500);
            }, 5000);
        });
    },
    
    /**
     * Tampilkan notifikasi toast
     * @param {string} message Pesan yang akan ditampilkan
     * @param {string} type Jenis notifikasi (success, danger, warning, info)
     * @param {number} duration Durasi tampilan dalam milidetik
     */
    showNotification: function(message, type = 'info', duration = 5000) {
        const container = document.getElementById('notification-container');
        if (!container) {
            const newContainer = document.createElement('div');
            newContainer.id = 'notification-container';
            newContainer.style.position = 'fixed';
            newContainer.style.top = '20px';
            newContainer.style.right = '20px';
            newContainer.style.zIndex = '9999';
            document.body.appendChild(newContainer);
        }
        
        const notifElement = document.createElement('div');
        notifElement.className = `toast alert alert-${type} show`;
        notifElement.role = 'alert';
        notifElement.innerHTML = `
            <div class="toast-header">
                <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        document.getElementById('notification-container').appendChild(notifElement);
        
        setTimeout(() => {
            notifElement.classList.add('hiding');
            setTimeout(() => {
                if (notifElement.parentNode) {
                    notifElement.parentNode.removeChild(notifElement);
                }
            }, 500);
        }, duration);
        
        // Tambahkan handler untuk tombol close
        notifElement.querySelector('.btn-close').addEventListener('click', function() {
            if (notifElement.parentNode) {
                notifElement.parentNode.removeChild(notifElement);
            }
        });
    },
    
    /**
     * Setup event listeners untuk elemen-elemen interaktif di halaman admin
     */
    setupEventListeners: function() {
        // Konfirmasi untuk aksi delete
        document.querySelectorAll('.delete-confirm').forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Apakah Anda yakin ingin menghapus item ini?')) {
                    e.preventDefault();
                }
            });
        });
        
        // Form validation
        document.querySelectorAll('form.needs-validation').forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    },
    
    /**
     * Format angka sebagai mata uang Rupiah
     * @param {number} number Angka yang akan diformat
     * @returns {string} String mata uang Rupiah
     */
    formatRupiah: function(number) {
        return new Intl.NumberFormat('id-ID', { 
            style: 'currency', 
            currency: 'IDR',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(number);
    }
};

// Jalankan inisialisasi ketika DOM sudah siap
document.addEventListener('DOMContentLoaded', function() {
    TewuNeedAdmin.init();
});
