<?php
session_start();
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Set response header to JSON
header('Content-Type: application/json');

// Verify that the user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Silakan login untuk mengosongkan keranjang'
    ]);
    exit;
}

try {
    $conn = getDbConnection();
    
    // Begin transaction for data consistency
    $conn->beginTransaction();
    
    // Get user's cart id
    $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$cart) {
        // No cart to clear, return success
        echo json_encode([
            'success' => true,
            'message' => 'Keranjang sudah kosong'
        ]);
        exit;
    }
    
    $cart_id = $cart['cart_id'];
    
    // Delete all items from the cart
    $stmt = $conn->prepare("DELETE FROM cart_items WHERE cart_id = ?");
    $stmt->execute([$cart_id]);
    
    // Commit transaction
    $conn->commit();
    
    // Log activity
    logActivity($_SESSION['user_id'], 'cart_clear', "Cleared all items from cart");
    
    echo json_encode([
        'success' => true,
        'message' => 'Keranjang berhasil dikosongkan'
    ]);
    
} catch (Exception $e) {
    // Rollback transaction in case of error
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollBack();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
