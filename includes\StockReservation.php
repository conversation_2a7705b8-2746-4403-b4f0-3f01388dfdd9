<?php
/**
 * Stock Reservation System
 * Manages stock reservations to prevent overselling
 * Stock is only reduced when orders are actually confirmed
 */

class StockReservation {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * Reserve stock for a product when added to cart
     * @param int $product_id
     * @param int $quantity
     * @param int|null $user_id
     * @param string|null $session_id
     * @return array
     */
    public function reserveStock($product_id, $quantity, $user_id = null, $session_id = null) {
        try {
            // Use session ID if user is not logged in
            if (!$user_id && !$session_id) {
                $session_id = session_id();
            }
            
            $stmt = $this->conn->prepare("CALL ReserveStock(?, ?, ?, ?, @success, @message)");
            $stmt->execute([$product_id, $user_id, $session_id, $quantity]);
            
            // Get the output parameters
            $result = $this->conn->query("SELECT @success as success, @message as message")->fetch(PDO::FETCH_ASSOC);
            
            return [
                'success' => (bool)$result['success'],
                'message' => $result['message']
            ];
        } catch (Exception $e) {
            error_log("Error reserving stock: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to reserve stock: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Confirm reservation and reduce actual stock (when order is placed)
     * @param int $product_id
     * @param int $quantity
     * @param int|null $user_id
     * @param string|null $session_id
     * @return array
     */
    public function confirmReservation($product_id, $quantity, $user_id = null, $session_id = null) {
        try {
            if (!$user_id && !$session_id) {
                $session_id = session_id();
            }
            
            $stmt = $this->conn->prepare("CALL ConfirmReservation(?, ?, ?, ?, @success, @message)");
            $stmt->execute([$product_id, $user_id, $session_id, $quantity]);
            
            $result = $this->conn->query("SELECT @success as success, @message as message")->fetch(PDO::FETCH_ASSOC);
            
            return [
                'success' => (bool)$result['success'],
                'message' => $result['message']
            ];
        } catch (Exception $e) {
            error_log("Error confirming reservation: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to confirm reservation: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Cancel reservation (when item is removed from cart)
     * @param int $product_id
     * @param int|null $user_id
     * @param string|null $session_id
     * @param int|null $quantity - specific quantity to cancel, null for all
     * @return array
     */
    public function cancelReservation($product_id, $user_id = null, $session_id = null, $quantity = null) {
        try {
            if (!$user_id && !$session_id) {
                $session_id = session_id();
            }

            if ($quantity === null) {
                // Cancel all reservations for this product
                $stmt = $this->conn->prepare("CALL CancelReservation(?, ?, ?, @success, @message)");
                $stmt->execute([$product_id, $user_id, $session_id]);
            } else {
                // Cancel specific quantity - use manual approach
                return $this->cancelPartialReservation($product_id, $user_id, $session_id, $quantity);
            }

            $result = $this->conn->query("SELECT @success as success, @message as message")->fetch(PDO::FETCH_ASSOC);

            return [
                'success' => (bool)$result['success'],
                'message' => $result['message']
            ];
        } catch (Exception $e) {
            error_log("Error cancelling reservation: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to cancel reservation: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Cancel partial reservation (specific quantity)
     * @param int $product_id
     * @param int|null $user_id
     * @param string|null $session_id
     * @param int $quantity
     * @return array
     */
    private function cancelPartialReservation($product_id, $user_id, $session_id, $quantity) {
        try {
            $this->conn->beginTransaction();

            // Find active reservations for this user/session and product
            $sql = "SELECT * FROM stock_reservations
                    WHERE product_id = ? AND status = 'active' AND expires_at > NOW()";
            $params = [$product_id];

            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params[] = $user_id;
            } else {
                $sql .= " AND session_id = ?";
                $params[] = $session_id;
            }

            $sql .= " ORDER BY created_at ASC";

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            $reservations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $remainingToCancel = $quantity;

            foreach ($reservations as $reservation) {
                if ($remainingToCancel <= 0) break;

                if ($reservation['quantity'] <= $remainingToCancel) {
                    // Cancel entire reservation
                    $stmt = $this->conn->prepare("UPDATE stock_reservations SET status = 'cancelled' WHERE reservation_id = ?");
                    $stmt->execute([$reservation['reservation_id']]);
                    $remainingToCancel -= $reservation['quantity'];
                } else {
                    // Reduce reservation quantity
                    $newQuantity = $reservation['quantity'] - $remainingToCancel;
                    $stmt = $this->conn->prepare("UPDATE stock_reservations SET quantity = ? WHERE reservation_id = ?");
                    $stmt->execute([$newQuantity, $reservation['reservation_id']]);
                    $remainingToCancel = 0;
                }
            }

            // Update reserved_stock in products table
            $stmt = $this->conn->prepare("
                UPDATE products
                SET reserved_stock = (
                    SELECT COALESCE(SUM(quantity), 0)
                    FROM stock_reservations
                    WHERE product_id = ?
                    AND status = 'active'
                    AND expires_at > NOW()
                )
                WHERE product_id = ?
            ");
            $stmt->execute([$product_id, $product_id]);

            $this->conn->commit();

            return [
                'success' => true,
                'message' => 'Partial reservation cancelled successfully'
            ];

        } catch (Exception $e) {
            $this->conn->rollBack();
            error_log("Error cancelling partial reservation: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to cancel partial reservation: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get available stock for a product (actual stock minus active reservations)
     * @param int $product_id
     * @return int
     */
    public function getAvailableStock($product_id) {
        try {
            $stmt = $this->conn->prepare("SELECT GetAvailableStock(?) as available_stock");
            $stmt->execute([$product_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return (int)($result['available_stock'] ?? 0);
        } catch (Exception $e) {
            error_log("Error getting available stock: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get user's reservations
     * @param int|null $user_id
     * @param string|null $session_id
     * @return array
     */
    public function getUserReservations($user_id = null, $session_id = null) {
        try {
            if (!$user_id && !$session_id) {
                $session_id = session_id();
            }
            
            $sql = "SELECT sr.*, p.name as product_name, p.price 
                    FROM stock_reservations sr 
                    JOIN products p ON sr.product_id = p.product_id 
                    WHERE sr.status = 'active' AND sr.expires_at > NOW()";
            
            $params = [];
            if ($user_id) {
                $sql .= " AND sr.user_id = ?";
                $params[] = $user_id;
            } else {
                $sql .= " AND sr.session_id = ?";
                $params[] = $session_id;
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting user reservations: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Clean up expired reservations manually
     * @return bool
     */
    public function cleanupExpiredReservations() {
        try {
            // Mark expired reservations
            $this->conn->exec("UPDATE stock_reservations SET status = 'expired' WHERE status = 'active' AND expires_at < NOW()");
            
            // Update reserved_stock for affected products
            $this->conn->exec("
                UPDATE products p
                SET reserved_stock = (
                    SELECT COALESCE(SUM(sr.quantity), 0) 
                    FROM stock_reservations sr 
                    WHERE sr.product_id = p.product_id 
                    AND sr.status = 'active' 
                    AND sr.expires_at > NOW()
                )
            ");
            
            return true;
        } catch (Exception $e) {
            error_log("Error cleaning up expired reservations: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Extend reservation expiry time
     * @param int $product_id
     * @param int|null $user_id
     * @param string|null $session_id
     * @param int $minutes
     * @return bool
     */
    public function extendReservation($product_id, $user_id = null, $session_id = null, $minutes = 30) {
        try {
            if (!$user_id && !$session_id) {
                $session_id = session_id();
            }
            
            $sql = "UPDATE stock_reservations 
                    SET expires_at = DATE_ADD(NOW(), INTERVAL ? MINUTE)
                    WHERE product_id = ? AND status = 'active' AND expires_at > NOW()";
            
            $params = [$minutes, $product_id];
            if ($user_id) {
                $sql .= " AND user_id = ?";
                $params[] = $user_id;
            } else {
                $sql .= " AND session_id = ?";
                $params[] = $session_id;
            }
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Error extending reservation: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get product stock info including reservations
     * @param int $product_id
     * @return array
     */
    public function getProductStockInfo($product_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT 
                    p.stock as actual_stock,
                    p.reserved_stock,
                    GetAvailableStock(p.product_id) as available_stock,
                    (SELECT COUNT(*) FROM stock_reservations WHERE product_id = p.product_id AND status = 'active' AND expires_at > NOW()) as active_reservations
                FROM products p 
                WHERE p.product_id = ?
            ");
            $stmt->execute([$product_id]);
            
            return $stmt->fetch(PDO::FETCH_ASSOC) ?: [
                'actual_stock' => 0,
                'reserved_stock' => 0,
                'available_stock' => 0,
                'active_reservations' => 0
            ];
        } catch (Exception $e) {
            error_log("Error getting product stock info: " . $e->getMessage());
            return [
                'actual_stock' => 0,
                'reserved_stock' => 0,
                'available_stock' => 0,
                'active_reservations' => 0
            ];
        }
    }
}
?>
