<?php
require_once '../config.php';
require_once '../includes/firebase_auth.php';
require_once '../includes/functions.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON response header
header('Content-Type: application/json');

// Log request data
error_log('Cart API Request: ' . print_r($_POST, true));
error_log('Raw input: ' . file_get_contents('php://input'));

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Validate input
if (!isset($input['action'])) {
    $response['message'] = 'Missing action parameter';
    echo json_encode($response);
    exit;
}

// Get product details
$product = null;
if (isset($input['product_id'])) {
    $product = getProductById($input['product_id']);
    if (!$product) {
        $response['message'] = 'Invalid product';
        echo json_encode($response);
        exit;
    }
}

// Get database connection
$conn = getConnection();
if (!$conn) {
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

// Initialize or get user's cart
function getUserCart($conn, $user_id) {
    // Check if user has an active cart
    $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$user_id]);
    $cart = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($cart) {
        return $cart['cart_id'];
    }

    // Create new cart
    $stmt = $conn->prepare("INSERT INTO carts (user_id) VALUES (?)");
    $stmt->execute([$user_id]);
    return $conn->lastInsertId();
}

// Get cart items count
function getCartItemsCount($conn, $cart_id) {
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM cart_items WHERE cart_id = ?");
    $stmt->execute([$cart_id]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result['count'];
}

// Use a test user ID for now (you should get this from the session)
$user_id = $_SESSION['user_id'] ?? 1;
$cart_id = getUserCart($conn, $user_id);

// Handle cart actions
switch ($input['action']) {
    case 'add':
    case 'update':
        if (!isset($input['product_id']) || !isset($input['quantity'])) {
            $response['message'] = 'Missing required parameters';
            break;
        }

        $quantity = max(1, intval($input['quantity']));

        if (isLoggedIn()) {
            // Handle database cart
            try {
                // Get user's cart or create new one
                $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
                $stmt->execute([$_SESSION['user_id']]);
                $cart = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$cart) {
                    // Create new cart
                    $stmt = $conn->prepare("INSERT INTO carts (user_id) VALUES (?)");
                    $stmt->execute([$_SESSION['user_id']]);
                    $cart_id = $conn->lastInsertId();
                } else {
                    $cart_id = $cart['cart_id'];
                }
                
                // Check if product exists in cart
                $stmt = $conn->prepare("SELECT cart_item_id FROM cart_items WHERE cart_id = ? AND product_id = ?");
                $stmt->execute([$cart_id, $input['product_id']]);
                $cart_item = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($cart_item) {
                    // Update quantity
                    $stmt = $conn->prepare("UPDATE cart_items SET quantity = ? WHERE cart_item_id = ?");
                    $stmt->execute([$quantity, $cart_item['cart_item_id']]);
                } else {
                    // Add new item
                    $stmt = $conn->prepare("INSERT INTO cart_items (cart_id, product_id, quantity) VALUES (?, ?, ?)");
                    $stmt->execute([$cart_id, $input['product_id'], $quantity]);
                }
                
                $response['success'] = true;
                $response['message'] = 'Cart updated successfully';
                $response['data'] = [
                    'cart_count' => getCartItemsCount($conn, $cart_id),
                    'cart_total' => getCartTotal($conn, $cart_id)
                ];
            } catch (PDOException $e) {
                error_log('Cart error: ' . $e->getMessage());
                $response['message'] = 'Failed to update cart';
            }
        } else {
            // Handle session cart
            if (!isset($_SESSION['cart'])) {
                $_SESSION['cart'] = [];
            }
            
            $found = false;
            foreach ($_SESSION['cart'] as &$item) {
                if ($item['product_id'] == $input['product_id']) {
                    $item['quantity'] = $quantity;
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $_SESSION['cart'][] = [
                    'product_id' => $input['product_id'],
                    'quantity' => $quantity
                ];
            }
            
            $response['success'] = true;
            $response['message'] = 'Cart updated successfully';
        }
        break;

    case 'remove':
        if (!isset($input['product_id'])) {
            $response['message'] = 'Missing product_id parameter';
            break;
        }

        if (isLoggedIn()) {
            // Handle database cart
            try {
                // Get user's cart
                $stmt = $conn->prepare("SELECT cart_id FROM carts WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
                $stmt->execute([$_SESSION['user_id']]);
                $cart = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($cart) {
                    // Remove item
                    $stmt = $conn->prepare("DELETE FROM cart_items WHERE cart_id = ? AND product_id = ?");
                    $stmt->execute([$cart['cart_id'], $input['product_id']]);
                    
                    $response['success'] = true;
                    $response['message'] = 'Item removed successfully';
                    $response['data'] = [
                        'cart_count' => getCartItemsCount($conn, $cart['cart_id']),
                        'cart_total' => getCartTotal($conn, $cart['cart_id'])
                    ];
                }
            } catch (PDOException $e) {
                error_log('Cart error: ' . $e->getMessage());
                $response['message'] = 'Failed to remove item';
            }
        } else {
            // Handle session cart
            if (isset($_SESSION['cart'])) {
                foreach ($_SESSION['cart'] as $key => $item) {
                    if ($item['product_id'] == $input['product_id']) {
                        unset($_SESSION['cart'][$key]);
                        break;
                    }
                }
                // Reindex array
                $_SESSION['cart'] = array_values($_SESSION['cart']);
            }
            
            $response['success'] = true;
            $response['message'] = 'Item removed successfully';
        }
        break;

    case 'clear':
        try {
            $stmt = $conn->prepare("DELETE FROM cart_items WHERE cart_id = ?");
            $stmt->execute([$cart_id]);

            $response['success'] = true;
            $response['message'] = 'Cart cleared';
            $response['data'] = [
                'cart_count' => 0,
                'cart_total' => 0
            ];
        } catch (PDOException $e) {
            error_log('Cart error: ' . $e->getMessage());
            $response['message'] = 'Failed to clear cart';
        }
        break;

    default:
        $response['message'] = 'Invalid action';
        break;
}

// Return JSON response
echo json_encode($response);
?>