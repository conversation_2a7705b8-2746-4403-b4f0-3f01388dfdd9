<?php
$page = 'realtime';
$page_title = 'Real-time Hub';

// Include header
require_once 'includes/header.php';

// Check if realtime_widget.php exists
$realtime_widget_exists = file_exists('includes/realtime_widget.php');
?>

<div class="container-fluid py-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-broadcast-tower me-2"></i>Real-time Hub</h2>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" id="refreshHub">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Real-time Statistics -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-chart-line me-2"></i>Real-time Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="border rounded p-3 text-center">
                                <h3 id="activeUsers" class="mb-1">0</h3>
                                <p class="text-muted mb-0">Active Users</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border rounded p-3 text-center">
                                <h3 id="activeOrders" class="mb-1">0</h3>
                                <p class="text-muted mb-0">Active Orders</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border rounded p-3 text-center">
                                <h3 id="pendingPayments" class="mb-1">0</h3>
                                <p class="text-muted mb-0">Pending Payments</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="border rounded p-3 text-center">
                                <h3 id="activeCarts" class="mb-1">0</h3>
                                <p class="text-muted mb-0">Active Carts</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>System Status</h6>
                        <div class="d-flex align-items-center mb-2">
                            <div id="systemStatusIndicator" class="me-2">
                                <i class="fas fa-circle text-success"></i>
                            </div>
                            <div>
                                <span id="systemStatusText">System Online</span>
                                <small class="text-muted ms-2" id="lastUpdated">Last updated: just now</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Events -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0"><i class="fas fa-bell me-2"></i>Recent Events</h5>
                    <button class="btn btn-sm btn-outline-primary" id="clearEvents">
                        <i class="fas fa-eraser me-1"></i>Clear
                    </button>
                </div>
                <div class="card-body p-0">
                    <div id="eventsContainer" style="height: 300px; overflow-y: auto;">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No recent events</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Online Users -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-users me-2"></i>Online Users</h5>
                </div>
                <div class="card-body p-0">
                    <div id="onlineUsersContainer" style="height: 200px; overflow-y: auto;">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-user-slash fa-2x mb-2"></i>
                            <p>No users online</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Broadcast Message -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-bullhorn me-2"></i>Broadcast Message</h5>
                </div>
                <div class="card-body">
                    <form id="broadcastForm">
                        <div class="mb-3">
                            <label class="form-label">Target</label>
                            <select class="form-select" name="target_type" required>
                                <option value="">Select target...</option>
                                <option value="all">All Users</option>
                                <option value="user">Specific Customer</option>
                                <option value="admin">All Admins</option>
                            </select>
                        </div>
                        <div class="mb-3" id="userIdField" style="display: none;">
                            <label class="form-label">User ID</label>
                            <input type="text" class="form-control" name="user_id" placeholder="Enter user ID">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Message</label>
                            <textarea class="form-control" name="message" rows="3" required placeholder="Enter your message"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Type</label>
                            <select class="form-select" name="message_type">
                                <option value="info">Information</option>
                                <option value="success">Success</option>
                                <option value="warning">Warning</option>
                                <option value="danger">Alert</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane me-1"></i>Send Broadcast
                        </button>
                    </form>
                </div>
            </div>

            <!-- Connection Status -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-network-wired me-2"></i>Connection</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Status:</span>
                        <span id="connectionBadge" class="badge bg-success">Connected</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span>Latency:</span>
                        <span id="latencyValue">0ms</span>
                    </div>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" id="testConnection">
                            <i class="fas fa-vial me-1"></i>Test Connection
                        </button>
                        <button class="btn btn-outline-danger btn-sm" id="resetConnection">
                            <i class="fas fa-power-off me-1"></i>Reset Connection
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Hub JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize variables
        let lastUpdated = new Date();
        let eventCount = 0;
        
        // Update last updated time
        function updateLastUpdated() {
            const now = new Date();
            const diff = Math.floor((now - lastUpdated) / 1000);
            let timeText = '';
            
            if (diff < 60) {
                timeText = `${diff} seconds ago`;
            } else if (diff < 3600) {
                timeText = `${Math.floor(diff / 60)} minutes ago`;
            } else {
                timeText = `${Math.floor(diff / 3600)} hours ago`;
            }
            
            document.getElementById('lastUpdated').textContent = `Last updated: ${timeText}`;
        }
        
        // Simulate real-time data updates
        function simulateDataUpdates() {
            // Update active users (random between 5-20)
            document.getElementById('activeUsers').textContent = Math.floor(Math.random() * 15) + 5;
            
            // Update active orders (random between 2-10)
            document.getElementById('activeOrders').textContent = Math.floor(Math.random() * 8) + 2;
            
            // Update pending payments (random between 1-5)
            document.getElementById('pendingPayments').textContent = Math.floor(Math.random() * 4) + 1;
            
            // Update active carts (random between 3-12)
            document.getElementById('activeCarts').textContent = Math.floor(Math.random() * 9) + 3;
            
            // Update last updated time
            lastUpdated = new Date();
            updateLastUpdated();
            
            // Simulate a new event (20% chance)
            if (Math.random() < 0.2) {
                addNewEvent();
            }
        }
        
        // Add a new simulated event
        function addNewEvent() {
            const eventsContainer = document.getElementById('eventsContainer');
            const emptyState = eventsContainer.querySelector('.text-center');
            
            if (emptyState) {
                emptyState.remove();
            }
            
            const eventTypes = [
                { type: 'order', icon: 'shopping-cart', color: 'primary', text: 'New order placed' },
                { type: 'payment', icon: 'credit-card', color: 'success', text: 'Payment received' },
                { type: 'user', icon: 'user-plus', color: 'info', text: 'New user registered' },
                { type: 'stock', icon: 'box', color: 'warning', text: 'Product stock low' },
                { type: 'system', icon: 'server', color: 'secondary', text: 'System update completed' }
            ];
            
            const event = eventTypes[Math.floor(Math.random() * eventTypes.length)];
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            
            const eventElement = document.createElement('div');
            eventElement.className = 'border-bottom p-3';
            eventElement.innerHTML = `
                <div class="d-flex">
                    <div class="me-3">
                        <div class="bg-${event.color} bg-opacity-10 p-2 rounded">
                            <i class="fas fa-${event.icon} text-${event.color}"></i>
                        </div>
                    </div>
                    <div>
                        <p class="mb-1">${event.text}</p>
                        <small class="text-muted">${timeString}</small>
                    </div>
                </div>
            `;
            
            eventsContainer.prepend(eventElement);
            eventCount++;
            
            // Update event count in sidebar
            const eventsCountBadge = document.getElementById('eventsCount');
            if (eventsCountBadge) {
                eventsCountBadge.textContent = eventCount;
                eventsCountBadge.style.display = 'inline-block';
            }
        }
        
        // Initialize the page
        function initPage() {
            // Start with initial data
            simulateDataUpdates();
            
            // Set up interval for data updates (every 5 seconds)
            setInterval(simulateDataUpdates, 5000);
            
            // Set up interval for updating the "last updated" text (every second)
            setInterval(updateLastUpdated, 1000);
            
            // Set up event handlers
            document.getElementById('refreshHub').addEventListener('click', function() {
                simulateDataUpdates();
                // Show toast notification
                showToast('Hub refreshed successfully', 'success');
            });
            
            document.getElementById('clearEvents').addEventListener('click', function() {
                const eventsContainer = document.getElementById('eventsContainer');
                eventsContainer.innerHTML = `
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>No recent events</p>
                    </div>
                `;
                eventCount = 0;
                
                // Update event count in sidebar
                const eventsCountBadge = document.getElementById('eventsCount');
                if (eventsCountBadge) {
                    eventsCountBadge.textContent = '0';
                    eventsCountBadge.style.display = 'none';
                }
                
                // Show toast notification
                showToast('Events cleared', 'info');
            });
            
            // Set up broadcast form
            const broadcastForm = document.getElementById('broadcastForm');
            const targetTypeSelect = broadcastForm.querySelector('[name="target_type"]');
            const userIdField = document.getElementById('userIdField');
            
            targetTypeSelect.addEventListener('change', function() {
                if (this.value === 'user') {
                    userIdField.style.display = 'block';
                } else {
                    userIdField.style.display = 'none';
                }
            });
            
            broadcastForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                const messageType = formData.get('message_type');
                
                // Show toast notification
                showToast('Broadcast message sent successfully', 'success');
                
                // Reset form
                this.reset();
                userIdField.style.display = 'none';
            });
            
            // Set up connection test
            document.getElementById('testConnection').addEventListener('click', function() {
                const connectionBadge = document.getElementById('connectionBadge');
                const latencyValue = document.getElementById('latencyValue');
                
                // Simulate connection test
                connectionBadge.className = 'badge bg-warning';
                connectionBadge.textContent = 'Testing...';
                
                setTimeout(function() {
                    const latency = Math.floor(Math.random() * 100) + 10;
                    latencyValue.textContent = `${latency}ms`;
                    
                    connectionBadge.className = 'badge bg-success';
                    connectionBadge.textContent = 'Connected';
                    
                    // Show toast notification
                    showToast(`Connection test completed: ${latency}ms`, 'success');
                }, 1000);
            });
            
            document.getElementById('resetConnection').addEventListener('click', function() {
                const connectionBadge = document.getElementById('connectionBadge');
                const latencyValue = document.getElementById('latencyValue');
                
                // Simulate connection reset
                connectionBadge.className = 'badge bg-danger';
                connectionBadge.textContent = 'Disconnected';
                latencyValue.textContent = '0ms';
                
                setTimeout(function() {
                    connectionBadge.className = 'badge bg-success';
                    connectionBadge.textContent = 'Connected';
                    
                    // Show toast notification
                    showToast('Connection reset successfully', 'success');
                }, 2000);
            });
        }
        
        // Helper function to show toast notifications
        function showToast(message, type = 'info') {
            const toastContainer = document.createElement('div');
            toastContainer.className = `toast align-items-center text-white bg-${type} border-0`;
            toastContainer.setAttribute('role', 'alert');
            toastContainer.setAttribute('aria-live', 'assertive');
            toastContainer.setAttribute('aria-atomic', 'true');
            
            toastContainer.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;
            
            // Add to document
            const toastContainerEl = document.createElement('div');
            toastContainerEl.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainerEl.style.zIndex = '1060';
            toastContainerEl.appendChild(toastContainer);
            document.body.appendChild(toastContainerEl);
            
            // Initialize and show the toast
            const toast = new bootstrap.Toast(toastContainer, { delay: 3000 });
            toast.show();
            
            // Remove from DOM after hiding
            toastContainer.addEventListener('hidden.bs.toast', function() {
                toastContainerEl.remove();
            });
        }
        
        // Initialize the page
        initPage();
    });
</script>

<?php require_once 'includes/footer.php'; ?>