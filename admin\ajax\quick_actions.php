<?php
session_start();
require_once '../../includes/db_connect.php';
require_once '../../includes/order_status_functions.php';

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

// Get action from POST data
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'generate_tracking':
        generateTrackingNumber($input);
        break;
        
    case 'notify_customer':
        notifyCustomer($input);
        break;
        
    case 'bulk_update':
        bulkUpdateStatus($input);
        break;
        
    case 'get_order_stats':
        getOrderStats();
        break;
        
    default:
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

/**
 * Generate tracking number for order
 */
function generateTrackingNumber($input) {
    global $conn;
    
    $order_id = (int)($input['order_id'] ?? 0);
    
    if ($order_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid order ID']);
        return;
    }
    
    try {
        // Generate new tracking number
        $tracking_number = 'TW' . date('Ymd') . strtoupper(substr(uniqid(), -8));
        
        // Update order with tracking number
        $stmt = $conn->prepare("UPDATE orders SET tracking_number = ? WHERE order_id = ?");
        $success = $stmt->execute([$tracking_number, $order_id]);
        
        if ($success) {
            // Create notification for customer
            $stmt = $conn->prepare("SELECT user_id FROM orders WHERE order_id = ?");
            $stmt->execute([$order_id]);
            $order = $stmt->fetch();
            
            if ($order) {
                createOrderNotification(
                    $conn,
                    $order_id,
                    $order['user_id'],
                    'Tracking Number Generated',
                    "Your order has been assigned tracking number: {$tracking_number}",
                    'shipping'
                );
            }
            
            echo json_encode([
                'success' => true, 
                'message' => 'Tracking number generated successfully',
                'tracking_number' => $tracking_number
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to update tracking number']);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
}

/**
 * Send notification to customer
 */
function notifyCustomer($input) {
    global $conn;
    
    $order_id = (int)($input['order_id'] ?? 0);
    $message = $input['message'] ?? '';
    
    if ($order_id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid order ID']);
        return;
    }
    
    if (empty($message)) {
        echo json_encode(['success' => false, 'message' => 'Message is required']);
        return;
    }
    
    try {
        // Get order info
        $stmt = $conn->prepare("SELECT user_id, current_status FROM orders WHERE order_id = ?");
        $stmt->execute([$order_id]);
        $order = $stmt->fetch();
        
        if (!$order) {
            echo json_encode(['success' => false, 'message' => 'Order not found']);
            return;
        }
        
        // Create notification
        $success = createOrderNotification(
            $conn,
            $order_id,
            $order['user_id'],
            'Message from Admin',
            $message,
            'general'
        );
        
        if ($success) {
            echo json_encode([
                'success' => true, 
                'message' => 'Notification sent successfully'
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Failed to send notification']);
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
}

/**
 * Bulk update order status
 */
function bulkUpdateStatus($input) {
    global $conn;
    
    $order_ids = $input['order_ids'] ?? [];
    $new_status = $input['status'] ?? '';
    $notes = $input['notes'] ?? '';
    
    if (empty($order_ids) || empty($new_status)) {
        echo json_encode(['success' => false, 'message' => 'Order IDs and status are required']);
        return;
    }
    
    try {
        $success_count = 0;
        $total_count = count($order_ids);
        
        foreach ($order_ids as $order_id) {
            $order_id = (int)$order_id;
            if ($order_id > 0) {
                $result = updateOrderStatus($conn, $order_id, $new_status, $notes, $_SESSION['user_id'], 'admin');
                if ($result) {
                    $success_count++;
                }
            }
        }
        
        echo json_encode([
            'success' => true,
            'message' => "Successfully updated {$success_count} out of {$total_count} orders",
            'success_count' => $success_count,
            'total_count' => $total_count
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
}

/**
 * Get order statistics
 */
function getOrderStats() {
    global $conn;
    
    try {
        $statuses = getOrderStatuses();
        $stats = [];
        
        // Get counts for each status
        foreach ($statuses as $key => $status) {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM orders WHERE current_status = ?");
            $stmt->execute([$key]);
            $result = $stmt->fetch();
            
            $stats[$key] = [
                'count' => (int)$result['count'],
                'label' => $status['label'],
                'color' => $status['color'],
                'icon' => $status['icon']
            ];
        }
        
        // Get today's orders
        $stmt = $conn->query("SELECT COUNT(*) as count FROM orders WHERE DATE(order_date) = CURDATE()");
        $today_orders = $stmt->fetch()['count'];
        
        // Get total revenue today
        $stmt = $conn->query("SELECT SUM(total_amount) as revenue FROM orders WHERE DATE(order_date) = CURDATE()");
        $today_revenue = $stmt->fetch()['revenue'] ?? 0;
        
        // Get pending orders count
        $stmt = $conn->query("SELECT COUNT(*) as count FROM orders WHERE current_status IN ('pending', 'confirmed')");
        $pending_orders = $stmt->fetch()['count'];
        
        echo json_encode([
            'success' => true,
            'stats' => $stats,
            'summary' => [
                'today_orders' => (int)$today_orders,
                'today_revenue' => (float)$today_revenue,
                'pending_orders' => (int)$pending_orders
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
}
?>
