<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

$response = ['success' => false, 'message' => '', 'stock' => 0];

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['product_id']) || !isset($input['quantity'])) {
        throw new Exception('Missing required parameters');
    }
    
    $product_id = (int)$input['product_id'];
    $quantity = (int)$input['quantity'];
    
    if ($quantity <= 0) {
        throw new Exception('Invalid quantity');
    }
    
    // Get current stock first
    $currentStock = getCurrentStock($product_id);
    if ($currentStock === false) {
        throw new Exception('Product not found');
    }
    
    if ($currentStock < $quantity) {
        throw new Exception('Insufficient stock');
    }
    
    // Update stock
    if (updateProductStock($product_id, $quantity)) {
        $newStock = getCurrentStock($product_id);
        $response = [
            'success' => true,
            'message' => 'Stock updated successfully',
            'stock' => $newStock
        ];
    } else {
        throw new Exception('Failed to update stock');
    }
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
