-- Stock Reservation System for TEWUNEED
-- This system ensures stock is only reduced when products are actually purchased

-- Create stock reservations table
CREATE TABLE IF NOT EXISTS `stock_reservations` (
  `reservation_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `reserved_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL 30 MINUTE),
  `status` enum('active','expired','confirmed','cancelled') DEFAULT 'active',
  PRIMARY KEY (`reservation_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add available_stock column to products table (virtual stock minus reservations)
ALTER TABLE `products` 
ADD COLUMN `reserved_stock` int(11) NOT NULL DEFAULT 0 AFTER `stock`;

-- Create function to get available stock (actual stock minus active reservations)
DELIMITER //
CREATE FUNCTION GetAvailableStock(product_id_param INT) 
RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE actual_stock INT DEFAULT 0;
    DECLARE reserved_stock INT DEFAULT 0;
    DECLARE available_stock INT DEFAULT 0;
    
    -- Get actual stock
    SELECT stock INTO actual_stock 
    FROM products 
    WHERE product_id = product_id_param;
    
    -- Get total active reservations
    SELECT COALESCE(SUM(quantity), 0) INTO reserved_stock
    FROM stock_reservations 
    WHERE product_id = product_id_param 
    AND status = 'active' 
    AND expires_at > NOW();
    
    -- Calculate available stock
    SET available_stock = actual_stock - reserved_stock;
    
    -- Ensure it's not negative
    IF available_stock < 0 THEN
        SET available_stock = 0;
    END IF;
    
    RETURN available_stock;
END//
DELIMITER ;

-- Create procedure to reserve stock
DELIMITER //
CREATE PROCEDURE ReserveStock(
    IN p_product_id INT,
    IN p_user_id INT,
    IN p_session_id VARCHAR(255),
    IN p_quantity INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE available_stock INT DEFAULT 0;
    DECLARE existing_reservation INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Database error occurred';
    END;
    
    START TRANSACTION;
    
    -- Clean up expired reservations first
    DELETE FROM stock_reservations 
    WHERE expires_at < NOW() OR status = 'expired';
    
    -- Get available stock
    SET available_stock = GetAvailableStock(p_product_id);
    
    -- Check if there's enough stock
    IF available_stock < p_quantity THEN
        SET p_success = FALSE;
        SET p_message = CONCAT('Insufficient stock. Available: ', available_stock);
        ROLLBACK;
    ELSE
        -- Check if user already has a reservation for this product
        SELECT COALESCE(SUM(quantity), 0) INTO existing_reservation
        FROM stock_reservations
        WHERE product_id = p_product_id
        AND (user_id = p_user_id OR session_id = p_session_id)
        AND status = 'active'
        AND expires_at > NOW();

        -- Create or update reservation
        IF existing_reservation > 0 THEN
            -- Replace existing reservation with new quantity (not add to it)
            UPDATE stock_reservations
            SET quantity = p_quantity,
                expires_at = CURRENT_TIMESTAMP + INTERVAL 30 MINUTE
            WHERE product_id = p_product_id
            AND (user_id = p_user_id OR session_id = p_session_id)
            AND status = 'active'
            AND expires_at > NOW()
            LIMIT 1;
        ELSE
            -- Create new reservation
            INSERT INTO stock_reservations (product_id, user_id, session_id, quantity, expires_at)
            VALUES (p_product_id, p_user_id, p_session_id, p_quantity, CURRENT_TIMESTAMP + INTERVAL 30 MINUTE);
        END IF;
        
        -- Update reserved_stock in products table
        UPDATE products 
        SET reserved_stock = (
            SELECT COALESCE(SUM(quantity), 0) 
            FROM stock_reservations 
            WHERE product_id = p_product_id 
            AND status = 'active' 
            AND expires_at > NOW()
        )
        WHERE product_id = p_product_id;
        
        SET p_success = TRUE;
        SET p_message = 'Stock reserved successfully';
        COMMIT;
    END IF;
END//
DELIMITER ;

-- Create procedure to confirm reservation (when order is placed)
DELIMITER //
CREATE PROCEDURE ConfirmReservation(
    IN p_product_id INT,
    IN p_user_id INT,
    IN p_session_id VARCHAR(255),
    IN p_quantity INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE reserved_quantity INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Database error occurred';
    END;
    
    START TRANSACTION;
    
    -- Get reserved quantity
    SELECT COALESCE(SUM(quantity), 0) INTO reserved_quantity
    FROM stock_reservations 
    WHERE product_id = p_product_id 
    AND (user_id = p_user_id OR session_id = p_session_id)
    AND status = 'active' 
    AND expires_at > NOW();
    
    IF reserved_quantity >= p_quantity THEN
        -- Reduce actual stock
        UPDATE products 
        SET stock = stock - p_quantity
        WHERE product_id = p_product_id;
        
        -- Mark reservation as confirmed and reduce quantity
        UPDATE stock_reservations 
        SET quantity = quantity - p_quantity,
            status = CASE 
                WHEN quantity - p_quantity <= 0 THEN 'confirmed'
                ELSE 'active'
            END
        WHERE product_id = p_product_id 
        AND (user_id = p_user_id OR session_id = p_session_id)
        AND status = 'active' 
        AND expires_at > NOW();
        
        -- Update reserved_stock in products table
        UPDATE products 
        SET reserved_stock = (
            SELECT COALESCE(SUM(quantity), 0) 
            FROM stock_reservations 
            WHERE product_id = p_product_id 
            AND status = 'active' 
            AND expires_at > NOW()
        )
        WHERE product_id = p_product_id;
        
        SET p_success = TRUE;
        SET p_message = 'Reservation confirmed and stock reduced';
        COMMIT;
    ELSE
        SET p_success = FALSE;
        SET p_message = 'Insufficient reserved stock';
        ROLLBACK;
    END IF;
END//
DELIMITER ;

-- Create procedure to cancel reservation
DELIMITER //
CREATE PROCEDURE CancelReservation(
    IN p_product_id INT,
    IN p_user_id INT,
    IN p_session_id VARCHAR(255),
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Database error occurred';
    END;
    
    START TRANSACTION;
    
    -- Cancel reservations
    UPDATE stock_reservations 
    SET status = 'cancelled'
    WHERE product_id = p_product_id 
    AND (user_id = p_user_id OR session_id = p_session_id)
    AND status = 'active';
    
    -- Update reserved_stock in products table
    UPDATE products 
    SET reserved_stock = (
        SELECT COALESCE(SUM(quantity), 0) 
        FROM stock_reservations 
        WHERE product_id = p_product_id 
        AND status = 'active' 
        AND expires_at > NOW()
    )
    WHERE product_id = p_product_id;
    
    SET p_success = TRUE;
    SET p_message = 'Reservation cancelled';
    COMMIT;
END//
DELIMITER ;

-- Create event to automatically clean up expired reservations
CREATE EVENT IF NOT EXISTS CleanupExpiredReservations
ON SCHEDULE EVERY 5 MINUTE
DO
BEGIN
    -- Mark expired reservations
    UPDATE stock_reservations 
    SET status = 'expired' 
    WHERE status = 'active' AND expires_at < NOW();
    
    -- Update reserved_stock for affected products
    UPDATE products p
    SET reserved_stock = (
        SELECT COALESCE(SUM(sr.quantity), 0) 
        FROM stock_reservations sr 
        WHERE sr.product_id = p.product_id 
        AND sr.status = 'active' 
        AND sr.expires_at > NOW()
    );
    
    -- Delete old expired reservations (older than 1 day)
    DELETE FROM stock_reservations 
    WHERE status IN ('expired', 'confirmed', 'cancelled') 
    AND reserved_at < DATE_SUB(NOW(), INTERVAL 1 DAY);
END;

-- Enable event scheduler
SET GLOBAL event_scheduler = ON;
