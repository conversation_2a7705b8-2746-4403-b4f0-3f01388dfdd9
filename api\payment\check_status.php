<?php
/**
 * Payment Status Check API
 * API untuk memeriksa status pembayaran secara real-time
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

session_start();
require_once '../../includes/db_connect.php';
require_once '../../includes/PaymentManager.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['transaction_id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Transaction ID is required']);
        exit;
    }
    
    $transaction_id = $input['transaction_id'];
    $paymentManager = new PaymentManager();
    
    // Get transaction details
    $transaction = $paymentManager->getPaymentTransaction($transaction_id);
    
    if (!$transaction) {
        http_response_code(404);
        echo json_encode(['error' => 'Transaction not found']);
        exit;
    }
    
    // Check if transaction belongs to current user
    if ($transaction['user_id'] != $_SESSION['user_id']) {
        http_response_code(403);
        echo json_encode(['error' => 'Access denied']);
        exit;
    }
    
    // Return transaction status
    echo json_encode([
        'success' => true,
        'transaction_id' => $transaction['transaction_id'],
        'status' => $transaction['status'],
        'amount' => $transaction['total_amount'],
        'payment_method' => $transaction['payment_method_name'],
        'paid_at' => $transaction['paid_at'],
        'expired_at' => $transaction['expired_at']
    ]);
    
} catch (Exception $e) {
    error_log("Payment status check error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
