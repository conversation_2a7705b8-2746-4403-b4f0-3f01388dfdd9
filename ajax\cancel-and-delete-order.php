<?php
session_start();
require_once '../includes/db_connect.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

try {
    // Get request data with enhanced validation
    $raw_input = file_get_contents('php://input');
    error_log("Cancel and Delete Order - Raw input: " . $raw_input);
    
    $input = json_decode($raw_input, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON input: ' . json_last_error_msg());
    }
    
    if (!isset($input['order_id'])) {
        throw new Exception('Missing order_id parameter');
    }
    
    $order_id = (int)$input['order_id'];
    $user_id = (int)$_SESSION['user_id'];
    
    if ($order_id <= 0) {
        throw new Exception('Invalid order ID: ' . $order_id);
    }
    
    if ($user_id <= 0) {
        throw new Exception('Invalid user ID: ' . $user_id);
    }
    
    error_log("Cancel and Delete Order - Processing: Order ID: {$order_id}, User ID: {$user_id}");
    
    // Check if order exists and belongs to user
    $stmt = $conn->prepare("
        SELECT order_id, order_number, order_status, user_id
        FROM orders 
        WHERE order_id = ? AND user_id = ?
    ");
    $stmt->execute([$order_id, $user_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        throw new Exception('Order not found or access denied');
    }
    
    // Handle empty or null status - set default to pending
    $current_status = $order['order_status'];
    if (empty($current_status) || is_null($current_status)) {
        $current_status = 'pending';
        error_log("Order {$order_id} had empty status, treating as 'pending'");
    }
    
    $current_status = strtolower(trim($current_status));
    
    // Enhanced debug logging
    error_log("Cancel and Delete Order Debug - Order ID: {$order_id}, Raw Status: '{$order['order_status']}', Processed Status: '{$current_status}'");
    
    // Check if order can be cancelled
    $cancellable_statuses = ['pending', 'dibuat', 'diproses', 'processing'];
    if (!in_array($current_status, $cancellable_statuses)) {
        throw new Exception("Order cannot be cancelled and deleted at this stage. Current status: '{$current_status}'. Cancellable statuses: " . implode(', ', $cancellable_statuses));
    }
    
    // Start transaction for safe deletion
    $conn->beginTransaction();
    
    try {
        // Step 1: Delete order items first (foreign key constraint)
        $delete_items_stmt = $conn->prepare("DELETE FROM order_items WHERE order_id = ?");
        $delete_items_stmt->execute([$order_id]);
        $deleted_items = $delete_items_stmt->rowCount();
        
        // Step 2: Delete order status history if table exists
        try {
            $delete_history_stmt = $conn->prepare("DELETE FROM order_status_history WHERE order_id = ?");
            $delete_history_stmt->execute([$order_id]);
            $deleted_history = $delete_history_stmt->rowCount();
        } catch (Exception $e) {
            // Table might not exist, continue
            $deleted_history = 0;
            error_log("order_status_history table not found or error: " . $e->getMessage());
        }
        
        // Step 3: Delete the order itself
        $delete_order_stmt = $conn->prepare("DELETE FROM orders WHERE order_id = ? AND user_id = ?");
        $delete_order_stmt->execute([$order_id, $user_id]);
        $deleted_order = $delete_order_stmt->rowCount();
        
        if ($deleted_order === 0) {
            throw new Exception('Failed to delete order');
        }
        
        // Commit transaction
        $conn->commit();
        
        // Log successful deletion
        error_log("Order Cancelled and Deleted - Order ID: {$order_id}, User ID: {$user_id}, Items: {$deleted_items}, History: {$deleted_history}");
        
        echo json_encode([
            'success' => true,
            'message' => 'Order cancelled and deleted successfully',
            'order_id' => $order_id,
            'order_number' => $order['order_number'] ?? "#$order_id",
            'deleted_items' => $deleted_items,
            'deleted_history' => $deleted_history,
            'action' => 'cancelled_and_deleted'
        ]);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw new Exception('Failed to cancel and delete order: ' . $e->getMessage());
    }
    
} catch (Exception $e) {
    // Enhanced error logging
    error_log("Cancel and Delete Order Error - " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    error_log("Cancel and Delete Order Error - Stack trace: " . $e->getTraceAsString());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'user_id' => $_SESSION['user_id'] ?? 'not_set',
            'post_data' => file_get_contents('php://input')
        ]
    ]);
}
?>
