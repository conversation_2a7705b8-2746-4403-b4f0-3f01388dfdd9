<?php
/**
 * TEWUNEED - Analytics Data Provider
 * Provides JSON data for dashboard charts and analytics
 */

session_start();

// Check if user is admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    echo json_encode(['error' => 'Access denied']);
    exit;
}

require_once '../config.php';
require_once '../includes/db_connect.php';

// Set content type to JSON
header('Content-Type: application/json');

$type = $_GET['type'] ?? '';

try {
    switch ($type) {
        case 'sales':
            echo json_encode(getSalesData($conn));
            break;
            
        case 'order_status':
            echo json_encode(getOrderStatusData($conn));
            break;
            
        case 'category_performance':
            echo json_encode(getCategoryPerformanceData($conn));
            break;
            
        case 'user_growth':
            echo json_encode(getUserGrowthData($conn));
            break;
            
        case 'product_performance':
            echo json_encode(getProductPerformanceData($conn));
            break;
            
        case 'revenue_trends':
            echo json_encode(getRevenueTrendsData($conn));
            break;
            
        default:
            echo json_encode(['error' => 'Invalid analytics type']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch analytics data: ' . $e->getMessage()]);
}

/**
 * Get sales data for the last 6 months
 */
function getSalesData($conn) {
    try {
        $stmt = $conn->query("
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COALESCE(SUM(total_amount), 0) as total_sales,
                COUNT(*) as order_count
            FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
            AND order_status != 'cancelled'
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month ASC
        ");
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $labels = [];
        $sales = [];
        $orders = [];
        
        // Generate last 6 months
        for ($i = 5; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-$i months"));
            $monthLabel = date('M Y', strtotime("-$i months"));
            
            $labels[] = $monthLabel;
            
            // Find data for this month
            $found = false;
            foreach ($results as $result) {
                if ($result['month'] === $month) {
                    $sales[] = (float)$result['total_sales'];
                    $orders[] = (int)$result['order_count'];
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $sales[] = 0;
                $orders[] = 0;
            }
        }
        
        return [
            'labels' => $labels,
            'sales' => $sales,
            'orders' => $orders
        ];
    } catch (Exception $e) {
        return [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            'sales' => [0, 0, 0, 0, 0, 0],
            'orders' => [0, 0, 0, 0, 0, 0]
        ];
    }
}

/**
 * Get order status distribution
 */
function getOrderStatusData($conn) {
    try {
        $stmt = $conn->query("
            SELECT 
                order_status,
                COUNT(*) as count
            FROM orders 
            GROUP BY order_status
            ORDER BY count DESC
        ");
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $labels = [];
        $values = [];
        
        $statusLabels = [
            'pending' => 'Pending',
            'processing' => 'Processing',
            'shipped' => 'Shipped',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled'
        ];
        
        foreach ($results as $result) {
            $status = $result['order_status'];
            $labels[] = $statusLabels[$status] ?? ucfirst($status);
            $values[] = (int)$result['count'];
        }
        
        return [
            'labels' => $labels,
            'values' => $values
        ];
    } catch (Exception $e) {
        return [
            'labels' => ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'],
            'values' => [0, 0, 0, 0, 0]
        ];
    }
}

/**
 * Get category performance data
 */
function getCategoryPerformanceData($conn) {
    try {
        $stmt = $conn->query("
            SELECT 
                c.name as category_name,
                COUNT(p.product_id) as product_count,
                COALESCE(SUM(oi.quantity * oi.unit_price), 0) as total_revenue
            FROM categories c
            LEFT JOIN products p ON c.category_id = p.category_id
            LEFT JOIN order_items oi ON p.product_id = oi.product_id
            LEFT JOIN orders o ON oi.order_id = o.order_id AND o.order_status != 'cancelled'
            GROUP BY c.category_id, c.name
            ORDER BY total_revenue DESC
            LIMIT 10
        ");
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $labels = [];
        $revenue = [];
        $products = [];
        
        foreach ($results as $result) {
            $labels[] = $result['category_name'];
            $revenue[] = (float)$result['total_revenue'];
            $products[] = (int)$result['product_count'];
        }
        
        return [
            'labels' => $labels,
            'revenue' => $revenue,
            'products' => $products
        ];
    } catch (Exception $e) {
        return [
            'labels' => [],
            'revenue' => [],
            'products' => []
        ];
    }
}

/**
 * Get user growth data
 */
function getUserGrowthData($conn) {
    try {
        $stmt = $conn->query("
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m') as month,
                COUNT(*) as new_users
            FROM users 
            WHERE role = 'customer'
            AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(created_at, '%Y-%m')
            ORDER BY month ASC
        ");
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $labels = [];
        $users = [];
        
        // Generate last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-$i months"));
            $monthLabel = date('M Y', strtotime("-$i months"));
            
            $labels[] = $monthLabel;
            
            // Find data for this month
            $found = false;
            foreach ($results as $result) {
                if ($result['month'] === $month) {
                    $users[] = (int)$result['new_users'];
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $users[] = 0;
            }
        }
        
        return [
            'labels' => $labels,
            'users' => $users
        ];
    } catch (Exception $e) {
        return [
            'labels' => [],
            'users' => []
        ];
    }
}

/**
 * Get product performance data
 */
function getProductPerformanceData($conn) {
    try {
        $stmt = $conn->query("
            SELECT 
                p.name as product_name,
                COALESCE(SUM(oi.quantity), 0) as total_sold,
                COALESCE(SUM(oi.quantity * oi.unit_price), 0) as total_revenue,
                p.stock as current_stock
            FROM products p
            LEFT JOIN order_items oi ON p.product_id = oi.product_id
            LEFT JOIN orders o ON oi.order_id = o.order_id AND o.order_status != 'cancelled'
            GROUP BY p.product_id, p.name, p.stock
            ORDER BY total_revenue DESC
            LIMIT 10
        ");
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $labels = [];
        $sold = [];
        $revenue = [];
        $stock = [];
        
        foreach ($results as $result) {
            $labels[] = $result['product_name'];
            $sold[] = (int)$result['total_sold'];
            $revenue[] = (float)$result['total_revenue'];
            $stock[] = (int)$result['current_stock'];
        }
        
        return [
            'labels' => $labels,
            'sold' => $sold,
            'revenue' => $revenue,
            'stock' => $stock
        ];
    } catch (Exception $e) {
        return [
            'labels' => [],
            'sold' => [],
            'revenue' => [],
            'stock' => []
        ];
    }
}

/**
 * Get revenue trends data
 */
function getRevenueTrendsData($conn) {
    try {
        $stmt = $conn->query("
            SELECT 
                DATE(created_at) as date,
                COALESCE(SUM(total_amount), 0) as daily_revenue,
                COUNT(*) as daily_orders
            FROM orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            AND order_status != 'cancelled'
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $labels = [];
        $revenue = [];
        $orders = [];
        
        // Generate last 30 days
        for ($i = 29; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $dateLabel = date('M j', strtotime("-$i days"));
            
            $labels[] = $dateLabel;
            
            // Find data for this date
            $found = false;
            foreach ($results as $result) {
                if ($result['date'] === $date) {
                    $revenue[] = (float)$result['daily_revenue'];
                    $orders[] = (int)$result['daily_orders'];
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $revenue[] = 0;
                $orders[] = 0;
            }
        }
        
        return [
            'labels' => $labels,
            'revenue' => $revenue,
            'orders' => $orders
        ];
    } catch (Exception $e) {
        return [
            'labels' => [],
            'revenue' => [],
            'orders' => []
        ];
    }
}
?>
