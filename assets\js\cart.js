// Cart functionality
class CartManager {
    constructor() {
        this.spinnerElement = document.getElementById('spinner');
        this.alertContainer = document.getElementById('alert-container');
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Add event delegation for cart buttons
        document.addEventListener('click', (event) => {
            const removeButton = event.target.closest('.remove-from-cart');
            if (removeButton) {
                const row = removeButton.closest('[data-product-id]');
                if (row) {
                    this.removeFromCart(event, row.dataset.productId);
                }
            }
        });

        // Add event delegation for quantity inputs
        document.addEventListener('change', (event) => {
            const quantityInput = event.target.closest('.cart-quantity-input');
            if (quantityInput) {
                const row = quantityInput.closest('[data-product-id]');
                if (row) {
                    this.updateCartQuantity(event, row.dataset.productId);
                }
            }
        });
    }

    showSpinner() {
        if (this.spinnerElement) {
            this.spinnerElement.style.display = 'block';
        }
    }

    hideSpinner() {
        if (this.spinnerElement) {
            this.spinnerElement.style.display = 'none';
        }
    }

    showAlert(title, message, type = 'success') {
        if (!this.alertContainer) {
            this.alertContainer = document.createElement('div');
            this.alertContainer.id = 'alert-container';
            this.alertContainer.className = 'position-fixed top-0 end-0 p-3 z-index-1000';
            document.body.appendChild(this.alertContainer);
        }

        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            <strong>${title}:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        this.alertContainer.appendChild(alert);

        // Auto dismiss after 5 seconds
        setTimeout(() => {
            alert.classList.remove('show');
            setTimeout(() => alert.remove(), 150);
        }, 5000);
    }

    updateCartCount(count) {
        const cartCount = document.getElementById('cart-count');
        if (cartCount) {
            cartCount.textContent = count;
            cartCount.style.display = count > 0 ? 'inline' : 'none';
        }
    }

    updateCartUI(data) {
        // Update cart count
        if (data.cart_count !== undefined) {
            this.updateCartCount(data.cart_count);
        }

        // Update cart total
        if (data.cart_total !== undefined) {
            const cartTotal = document.getElementById('cart-total');
            if (cartTotal) {
                cartTotal.innerHTML = `<strong>Rp ${this.numberFormat(data.cart_total)}</strong>`;
            }
        }

        // Update subtotal if available
        if (data.subtotal !== undefined && data.product_id !== undefined) {
            const subtotalElement = document.getElementById(`subtotal-${data.product_id}`);
            if (subtotalElement) {
                subtotalElement.textContent = `Rp ${this.numberFormat(data.subtotal)}`;
            }
        }

        // Show empty cart message if needed
        if (data.cart_count === 0) {
            const cartTable = document.getElementById('cart-table');
            if (cartTable) {
                cartTable.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center">
                            <div class="p-4">
                                <i class="fas fa-shopping-cart fa-3x mb-3 text-muted"></i>
                                <h5>Your cart is empty</h5>
                                <p>Looks like you haven't added anything to your cart yet.</p>
                                <a href="index.php" class="btn btn-primary">Continue Shopping</a>
                            </div>
                        </td>
                    </tr>`;
            }
        }
    }

    async addToCart(event, productId, productName, price, imageUrl) {
        event.preventDefault();
        this.showSpinner();

        try {
            const response = await this.makeRequest('update_cart.php', {
                action: 'add',
                product_id: productId,
                name: productName,
                price: price,
                image_url: imageUrl,
                quantity: 1
            });

            this.hideSpinner();

            if (response.success) {
                this.updateCartUI(response);
                // Show notification with product name and quantity
                const quantity = response.data && response.data.quantity ? response.data.quantity : 1;
                this.showAlert('Success', `${quantity} ${productName} telah ditambahkan ke keranjang!`, 'success');
            } else {
                throw new Error(response.message || 'Error adding product to cart');
            }
        } catch (error) {
            this.handleError(error);
        }
    }

    async removeFromCart(event, productId) {
        event.preventDefault();

        if (!confirm('Are you sure you want to remove this item?')) {
            return;
        }

        this.showSpinner();

        try {
            const response = await this.makeRequest('update_cart.php', {
                action: 'remove',
                product_id: productId
            });

            this.hideSpinner();

            if (response.success) {
                const cartRow = document.getElementById(`cart-row-${productId}`);
                if (cartRow) {
                    cartRow.remove();
                }
                
                this.updateCartUI(response);
                this.showAlert('Success', 'Product removed from cart successfully!', 'success');
                
                // Reload if cart is empty and not on cart page
                if (response.cart_count === 0 && !document.getElementById('cart-table')) {
                    location.reload();
                }
            } else {
                throw new Error(response.message || 'Error removing product from cart');
            }
        } catch (error) {
            this.handleError(error);
        }
    }

    async updateCartQuantity(event, productId) {
        const input = event.target;
        const newQuantity = parseInt(input.value);
        const defaultValue = parseInt(input.getAttribute('data-default-value')) || 1;

        if (isNaN(newQuantity) || newQuantity < 1) {
            input.value = defaultValue;
            return;
        }

        this.showSpinner();

        try {
            const response = await this.makeRequest('update_cart.php', {
                action: 'update',
                product_id: productId,
                quantity: newQuantity
            });

            this.hideSpinner();

            if (response.success) {
                input.setAttribute('data-default-value', newQuantity);
                this.updateCartUI(response);
                this.showAlert('Success', 'Cart updated successfully!', 'success');
            } else {
                throw new Error(response.message || 'Error updating cart');
            }
        } catch (error) {
            input.value = defaultValue;
            this.handleError(error);
        }
    }

    async makeRequest(url, data) {
        try {
            const response = await fetch(`/tewuneed2/${url}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const result = await response.json();
            console.log('Response:', result); // Add logging
            return result;
        } catch (error) {
            console.error('Request error:', error); // Add logging
            throw new Error('Network error: ' + error.message);
        }
    }

    handleError(error) {
        console.error('Error:', error);
        this.hideSpinner();
        this.showAlert('Error', error.message, 'danger');
    }

    numberFormat(number) {
        return new Intl.NumberFormat('id-ID').format(number);
    }
}

// Initialize cart manager
const cartManager = new CartManager();

// Export functions for global use
window.addToCart = (...args) => cartManager.addToCart.bind(cartManager)(...args);
window.removeFromCart = (...args) => cartManager.removeFromCart.bind(cartManager)(...args);
window.updateCartQuantity = (...args) => cartManager.updateCartQuantity.bind(cartManager)(...args);
