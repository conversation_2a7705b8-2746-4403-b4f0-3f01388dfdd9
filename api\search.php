<?php
require_once '../config.php';
require_once '../includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if request method is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
    exit;
}

// Get search query
$query = isset($_GET['q']) ? trim($_GET['q']) : '';

if (empty($query)) {
    echo json_encode([
        'success' => false,
        'message' => 'Search query is required'
    ]);
    exit;
}

try {
    $conn = getDbConnection();
    
    // Prepare search query
    $search_query = "%{$query}%";
    
    // Search in products table
    $stmt = $conn->prepare("
        SELECT p.*, c.name as category_name 
        FROM products p 
        JOIN categories c ON p.category_id = c.id 
        WHERE p.is_active = 1 
        AND (
            p.name LIKE ? 
            OR p.description LIKE ? 
            OR c.name LIKE ?
        )
        ORDER BY 
            CASE 
                WHEN p.name LIKE ? THEN 1
                WHEN p.description LIKE ? THEN 2
                ELSE 3
            END,
            p.name ASC
        LIMIT 10
    ");
    
    $stmt->execute([
        $search_query,
        $search_query,
        $search_query,
        $search_query,
        $search_query
    ]);
    
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format results
    $formatted_results = array_map(function($product) {
        return [
            'id' => $product['id'],
            'name' => $product['name'],
            'description' => $product['description'],
            'price' => formatPrice($product['price']),
            'image_url' => $product['image_url'],
            'category_name' => $product['category_name']
        ];
    }, $results);
    
    echo json_encode([
        'success' => true,
        'results' => $formatted_results
    ]);
    
} catch (PDOException $e) {
    error_log("Search Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error performing search'
    ]);
} 