<?php
session_start();
require_once '../includes/db_connect.php';

// Check admin authentication
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit;
}

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['alert_message'] = 'No order ID specified';
    $_SESSION['alert_type'] = 'danger';
    header('Location: order_management.php');
    exit;
}

$order_id = (int)$_GET['id'];

// Function to auto-sync payment status based on order status
function getAutoPaymentStatus($order_status) {
    $sync_rules = [
        'pending' => null,           // No auto-sync for pending
        'diproses' => 'paid',        // Processing requires payment
        'dikirim' => 'paid',         // Shipped requires payment
        'terkirim' => 'paid',        // Delivered requires payment
        'dibatalkan' => null         // Cancelled - keep current payment status
    ];

    return $sync_rules[$order_status] ?? null;
}

// Get order data with fresh data from database
try {
    // First check if payment_status column exists
    $stmt = $conn->query("SHOW COLUMNS FROM orders LIKE 'payment_status'");
    $payment_status_exists = $stmt->rowCount() > 0;

    if (!$payment_status_exists) {
        // Add payment_status column if it doesn't exist
        $conn->exec("ALTER TABLE orders ADD COLUMN payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending'");

        // Set default values based on order status
        $conn->exec("UPDATE orders SET payment_status = 'paid' WHERE order_status IN ('terkirim', 'delivered', 'completed')");
        $conn->exec("UPDATE orders SET payment_status = 'pending' WHERE order_status IN ('pending', 'diproses', 'processing', 'dikirim', 'shipped')");
        $conn->exec("UPDATE orders SET payment_status = 'failed' WHERE order_status IN ('dibatalkan', 'cancelled')");
    }

    $stmt = $conn->prepare("
        SELECT SQL_NO_CACHE o.*, u.full_name as user_name, u.email as user_email, u.phone_number as user_phone
        FROM orders o
        LEFT JOIN users u ON o.user_id = u.user_id
        WHERE o.order_id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$order) {
        $_SESSION['alert_message'] = 'Order not found';
        $_SESSION['alert_type'] = 'danger';
        header('Location: order_management.php');
        exit;
    }

    // Ensure payment_status has a default value
    if (!isset($order['payment_status']) || empty($order['payment_status'])) {
        $order['payment_status'] = 'pending';
    }

    // Get order status history
    $stmt = $conn->prepare("
        SELECT * FROM order_status_history
        WHERE order_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->execute([$order_id]);
    $status_history = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get current status from history if available
    $current_status = $order['order_status'] ?? $order['status'] ?? 'pending';
    if (!empty($status_history)) {
        $latest_history = $status_history[0];
        $current_status = $latest_history['status'] ?? $latest_history['new_status'] ?? $current_status;
    }
    
    // Get order items
    $stmt = $conn->prepare("
        SELECT oi.*, p.NAME as product_name, p.image
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.product_id
        WHERE oi.order_id = ?
    ");
    $stmt->execute([$order_id]);
    $order_items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate totals
    $subtotal = 0;
    foreach ($order_items as $item) {
        $subtotal += $item['price'] * $item['quantity'];
    }
    
    $shipping_cost = $order['shipping_cost'] ?? 0;
    $total = $subtotal + $shipping_cost;
    
} catch (PDOException $e) {
    $_SESSION['alert_message'] = 'Database error: ' . $e->getMessage();
    $_SESSION['alert_type'] = 'danger';
    header('Location: order_management.php');
    exit;
}

// Process AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');

    try {
        // Handle order status update
        if (isset($_POST['order_status'])) {
            $order_status = trim($_POST['order_status']);
            $admin_notes = trim($_POST['notes'] ?? '');

            // Get current order status first
            $stmt = $conn->prepare("SELECT order_status FROM orders WHERE order_id = ?");
            $stmt->execute([$order_id]);
            $current_order = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$current_order) {
                throw new Exception("Order not found");
            }

            $old_status = $current_order['order_status'];

            // Validate order status
            $valid_statuses = ['pending', 'diproses', 'dikirim', 'terkirim', 'dibatalkan'];
            if (!in_array($order_status, $valid_statuses)) {
                throw new Exception("Invalid order status: " . $order_status);
            }

            // Update order status
            $stmt = $conn->prepare("UPDATE orders SET order_status = ?, updated_at = NOW() WHERE order_id = ?");
            $success = $stmt->execute([$order_status, $order_id]);
            $affected = $stmt->rowCount();

            if ($success && $affected > 0) {
                // Try to add to order status history (optional - won't fail if table doesn't exist)
                try {
                    // Check if table exists first
                    $check_table = $conn->prepare("SHOW TABLES LIKE 'order_status_history'");
                    $check_table->execute();
                    $table_exists = $check_table->fetch();

                    if ($table_exists) {
                        $history_stmt = $conn->prepare("
                            INSERT INTO order_status_history
                            (order_id, previous_status, new_status, notes, admin_id, created_at)
                            VALUES (?, ?, ?, ?, ?, NOW())
                        ");
                        $admin_note = $admin_notes ?: "Status updated by admin from {$old_status} to {$order_status}";
                        $history_stmt->execute([
                            $order_id,
                            $old_status,
                            $order_status,
                            $admin_note,
                            $_SESSION['user_id'] ?? 1
                        ]);
                    }
                } catch (Exception $e) {
                    // History insert failed, but status update succeeded - just log it
                    error_log("Order status history insert failed: " . $e->getMessage());
                }

                // Auto-sync payment status based on order status
                $auto_payment_status = getAutoPaymentStatus($order_status);
                $current_payment_status = null;
                $sync_message = null;

                if ($auto_payment_status) {
                    try {
                        // Get current payment status
                        $stmt = $conn->prepare("SELECT payment_status FROM orders WHERE order_id = ?");
                        $stmt->execute([$order_id]);
                        $payment_result = $stmt->fetch(PDO::FETCH_ASSOC);
                        $current_payment_status = $payment_result['payment_status'] ?? 'pending';

                        // Update payment status if different
                        if ($current_payment_status !== $auto_payment_status) {
                            $stmt = $conn->prepare("UPDATE orders SET payment_status = ? WHERE order_id = ?");
                            $stmt->execute([$auto_payment_status, $order_id]);
                            $sync_message = "Payment status auto-synced from '$current_payment_status' to '$auto_payment_status'";
                            $current_payment_status = $auto_payment_status;
                        }
                    } catch (Exception $e) {
                        // If payment_status column doesn't exist, skip auto-sync
                        error_log("Payment status auto-sync failed: " . $e->getMessage());
                        $current_payment_status = 'pending';
                    }
                }

                echo json_encode([
                    'success' => true,
                    'message' => 'Order status updated successfully',
                    'new_status' => $order_status,
                    'old_status' => $old_status,
                    'order_id' => $order_id,
                    'admin_note' => $admin_notes,
                    'timestamp' => time() * 1000,
                    'auto_payment_sync' => $auto_payment_status ? true : false,
                    'new_payment_status' => $current_payment_status,
                    'sync_message' => $sync_message
                ]);
            } else {
                throw new Exception('Failed to update order status - no rows affected');
            }
            exit;
        }

        // Handle cancel order (delete order completely)
        if (isset($_POST['cancel_order'])) {
            $cancel_reason = trim($_POST['cancel_reason'] ?? '');

            if (empty($cancel_reason)) {
                throw new Exception('Cancellation reason is required');
            }

            // Begin transaction for order deletion
            $conn->beginTransaction();

            try {
                // Get order items for inventory restoration
                $stmt = $conn->prepare("SELECT product_id, quantity FROM order_items WHERE order_id = ?");
                $stmt->execute([$order_id]);
                $order_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Restore inventory for each item
                foreach ($order_items as $item) {
                    $stmt = $conn->prepare("UPDATE products SET stock = stock + ? WHERE product_id = ?");
                    $stmt->execute([$item['quantity'], $item['product_id']]);
                }

                // Delete order items first (foreign key constraint)
                $stmt = $conn->prepare("DELETE FROM order_items WHERE order_id = ?");
                $stmt->execute([$order_id]);

                // Delete order status history
                $stmt = $conn->prepare("DELETE FROM order_status_history WHERE order_id = ?");
                $stmt->execute([$order_id]);

                // Delete the order itself
                $stmt = $conn->prepare("DELETE FROM orders WHERE order_id = ?");
                $stmt->execute([$order_id]);

                $conn->commit();

                echo json_encode([
                    'success' => true,
                    'message' => 'Order cancelled and deleted successfully',
                    'redirect' => 'order_management.php'
                ]);
            } catch (Exception $e) {
                $conn->rollBack();
                throw new Exception('Failed to cancel order: ' . $e->getMessage());
            }
            exit;
        }

        // Handle payment status update
        if (isset($_POST['payment_status'])) {
            $payment_status = trim($_POST['payment_status']);

            // Validate payment status
            $valid_payment_statuses = ['pending', 'paid', 'failed', 'refunded'];
            if (!in_array($payment_status, $valid_payment_statuses)) {
                throw new Exception("Invalid payment status");
            }

            try {
                // Update payment status
                $stmt = $conn->prepare("UPDATE orders SET payment_status = ?, updated_at = NOW() WHERE order_id = ?");
                $success = $stmt->execute([$payment_status, $order_id]);
                $affected = $stmt->rowCount();

                if ($success && $affected > 0) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Payment status updated successfully',
                        'new_payment_status' => $payment_status
                    ]);
                } else {
                    throw new Exception('Failed to update payment status');
                }
            } catch (Exception $e) {
                // If payment_status column doesn't exist, try to add it first
                if (strpos($e->getMessage(), 'payment_status') !== false) {
                    try {
                        $conn->exec("ALTER TABLE orders ADD COLUMN payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending'");
                        // Retry the update
                        $stmt = $conn->prepare("UPDATE orders SET payment_status = ?, updated_at = NOW() WHERE order_id = ?");
                        $success = $stmt->execute([$payment_status, $order_id]);
                        if ($success) {
                            echo json_encode([
                                'success' => true,
                                'message' => 'Payment status updated successfully (column added)',
                                'new_payment_status' => $payment_status
                            ]);
                        } else {
                            throw new Exception('Failed to update payment status after adding column');
                        }
                    } catch (Exception $e2) {
                        throw new Exception('Failed to add payment_status column: ' . $e2->getMessage());
                    }
                } else {
                    throw $e;
                }
            }
            exit;
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
}

// Now include header after all redirects are done
$page = 'order_management';
$page_title = 'Order Detail';
require_once 'includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="fs-4">
                Order Detail
                <?php if (isset($order['order_number']) && !empty($order['order_number'])): ?>
                    - <?php echo htmlspecialchars($order['order_number']); ?>
                <?php else: ?>
                    - #<?php echo $order['order_id']; ?>
                <?php endif; ?>
            </h1>
            <a href="order_management.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Orders
            </a>
        </div>
    </div>
    
    <div class="row">
        <!-- Order Summary -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Order Information</h5>
                    <div>
                        <!-- Order Status Badge -->
                        <?php
                        $status = $order['order_status'] ?? 'pending';

                        // Status mapping for display
                        $status_config = [
                            'pending' => ['class' => 'bg-warning', 'label' => 'Pending'],
                            'diproses' => ['class' => 'bg-info', 'label' => 'Processing'],
                            'processing' => ['class' => 'bg-info', 'label' => 'Processing'],
                            'dikirim' => ['class' => 'bg-primary', 'label' => 'Shipped'],
                            'shipped' => ['class' => 'bg-primary', 'label' => 'Shipped'],
                            'terkirim' => ['class' => 'bg-success', 'label' => 'Delivered'],
                            'delivered' => ['class' => 'bg-success', 'label' => 'Delivered'],
                            'dibatalkan' => ['class' => 'bg-danger', 'label' => 'Cancelled'],
                            'cancelled' => ['class' => 'bg-danger', 'label' => 'Cancelled']
                        ];

                        // Use current_status from history if available
                        $display_status = $current_status;
                        $config = $status_config[$display_status] ?? ['class' => 'bg-secondary', 'label' => ucfirst($display_status)];
                        ?>
                        <span class="badge <?php echo $config['class']; ?>" id="orderStatusBadge">
                            <?php echo $config['label']; ?>
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Date and Payment Info -->
                        <div class="col-md-6 mb-4">
                            <h6 class="fw-bold">Order Details</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <tr>
                                        <td class="fw-bold">Order Date:</td>
                                        <td><?php echo isset($order['order_date']) ? date('d M Y H:i', strtotime($order['order_date'])) : 'N/A'; ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Payment Method:</td>
                                        <td><?php echo ucfirst(str_replace('_', ' ', htmlspecialchars($order['payment_method'] ?? 'N/A'))); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Payment Status:</td>
                                        <td>
                                            <?php 
                                            $payment_status = $order['payment_status'] ?? 'pending';
                                            $payment_badge = 'badge bg-warning';
                                            if ($payment_status === 'paid') $payment_badge = 'badge bg-success';
                                            elseif ($payment_status === 'failed') $payment_badge = 'badge bg-danger';
                                            elseif ($payment_status === 'refunded') $payment_badge = 'badge bg-info';
                                            ?>
                                            <span class="<?php echo $payment_badge; ?>" id="paymentStatusBadge"><?php echo ucfirst($payment_status); ?></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Shipping Method:</td>
                                        <td><?php echo ucfirst(str_replace('_', ' ', htmlspecialchars($order['shipping_method'] ?? 'Standard'))); ?></td>
                                    </tr>
                                    <?php if(!empty($order['tracking_number'])): ?>
                                    <tr>
                                        <td class="fw-bold">Tracking Number:</td>
                                        <td><?php echo htmlspecialchars($order['tracking_number']); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Customer Info -->
                        <div class="col-md-6 mb-4">
                            <h6 class="fw-bold">Customer Details</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <tr>
                                        <td class="fw-bold">Name:</td>
                                        <td><?php echo htmlspecialchars($order['shipping_name'] ?? $order['user_name'] ?? 'N/A'); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Email:</td>
                                        <td><?php echo htmlspecialchars($order['shipping_email'] ?? $order['user_email'] ?? 'N/A'); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">Phone:</td>
                                        <td><?php echo htmlspecialchars($order['shipping_phone'] ?? $order['user_phone'] ?? 'N/A'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <!-- Shipping Address -->
                        <div class="col-md-6 mb-4">
                            <h6 class="fw-bold">Shipping Address</h6>
                            <p>
                                <?php 
                                echo htmlspecialchars($order['shipping_address'] ?? 'N/A');
                                if (isset($order['city']) && !empty($order['city'])) {
                                    echo '<br>' . htmlspecialchars($order['city']);
                                }
                                if (isset($order['postal_code']) && !empty($order['postal_code'])) {
                                    echo ', ' . htmlspecialchars($order['postal_code']);
                                }
                                ?>
                            </p>
                        </div>
                        
                        <!-- Update Status -->
                        <div class="col-md-6 mb-4">
                            <h6 class="fw-bold">Update Order Status</h6>
                            <form id="statusUpdateForm">
                                <input type="hidden" name="order_id" value="<?php echo $order_id; ?>">
                                <div class="input-group mb-3">
                                    <select name="order_status" class="form-select" id="orderStatusSelect">
                                        <option value="pending" <?php echo $current_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="diproses" <?php echo $current_status === 'diproses' ? 'selected' : ''; ?>>Processing</option>
                                        <option value="dikirim" <?php echo $current_status === 'dikirim' ? 'selected' : ''; ?>>Shipped</option>
                                        <option value="terkirim" <?php echo $current_status === 'terkirim' ? 'selected' : ''; ?>>Delivered</option>
                                        <option value="dibatalkan" <?php echo $current_status === 'dibatalkan' ? 'selected' : ''; ?>>Cancelled</option>
                                    </select>
                                    <button type="submit" class="btn btn-primary" id="updateStatusBtn">Update</button>
                                </div>
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="2"></textarea>
                                </div>
                            </form>

                            <!-- Debug Results -->
                            <div id="debugResults" class="mt-3" style="display: none;">
                                <h6 class="fw-bold text-info">Debug Information</h6>
                                <div class="alert alert-info">
                                    <pre id="debugContent" style="font-size: 12px; margin: 0;"></pre>
                                </div>
                            </div>

                            <!-- Status Update Results -->
                            <div id="statusUpdateResults" class="mt-3"></div>

                            <!-- Update Payment Status -->
                            <h6 class="fw-bold mt-4">Update Payment Status</h6>
                            <form id="paymentUpdateForm">
                                <input type="hidden" name="order_id" value="<?php echo $order_id; ?>">
                                <div class="input-group mb-3">
                                    <select name="payment_status" class="form-select" id="paymentStatusSelect">
                                        <option value="pending" <?php echo ($order['payment_status'] ?? 'pending') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="paid" <?php echo ($order['payment_status'] ?? '') === 'paid' ? 'selected' : ''; ?>>Paid</option>
                                        <option value="failed" <?php echo ($order['payment_status'] ?? '') === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                        <option value="refunded" <?php echo ($order['payment_status'] ?? '') === 'refunded' ? 'selected' : ''; ?>>Refunded</option>
                                    </select>
                                    <button type="submit" class="btn btn-primary" id="updatePaymentBtn">Update</button>
                                </div>
                                <div class="mb-3">
                                    <label for="payment_notes" class="form-label">Payment Notes</label>
                                    <textarea name="payment_notes" id="payment_notes" class="form-control" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Ordered Items -->
                    <h6 class="fw-bold mt-2">Ordered Items</h6>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th class="text-end">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($order_items) > 0): ?>
                                    <?php foreach ($order_items as $item): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if (isset($item['image']) && !empty($item['image']) && file_exists('../' . $item['image'])): ?>
                                                        <img src="../<?php echo htmlspecialchars($item['image']); ?>" alt="Product" class="img-thumbnail me-2" width="50">
                                                    <?php endif; ?>
                                                    <div>
                                                        <span class="d-block"><?php echo htmlspecialchars($item['product_name'] ?? 'Unknown Product'); ?></span>
                                                        <small class="text-muted">ID: <?php echo $item['product_id'] ?? 'N/A'; ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>Rp <?php echo number_format($item['price']); ?></td>
                                            <td><?php echo $item['quantity']; ?></td>
                                            <td class="text-end">Rp <?php echo number_format($item['price'] * $item['quantity']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center">No items found for this order</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end fw-bold">Subtotal:</td>
                                    <td class="text-end">Rp <?php echo number_format($subtotal); ?></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-end fw-bold">Shipping:</td>
                                    <td class="text-end">Rp <?php echo number_format($shipping_cost); ?></td>
                                </tr>
                                <tr>
                                    <td colspan="3" class="text-end fw-bold">Total:</td>
                                    <td class="text-end fw-bold">Rp <?php echo number_format($total); ?></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Actions -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Order Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addTrackingModal">
                            <i class="fas fa-shipping-fast me-2"></i>Add Tracking Number
                        </button>
                        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#viewPaymentProofModal" <?php echo empty($order['payment_proof']) ? 'disabled' : ''; ?>>
                            <i class="fas fa-receipt me-2"></i>View Payment Proof
                        </button>
                        <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#sendEmailModal">
                            <i class="fas fa-envelope me-2"></i>Send Email to Customer
                        </button>
                        <a href="print-invoice.php?id=<?php echo $order_id; ?>" target="_blank" class="btn btn-outline-secondary">
                            <i class="fas fa-print me-2"></i>Print Invoice
                        </a>
                        <?php if($status !== 'cancelled'): ?>
                        <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelOrderModal">
                            <i class="fas fa-ban me-2"></i>Cancel Order
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Payment Details Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Payment Details</h5>
                </div>
                <div class="card-body">
                    <?php if(isset($order['payment_method']) && $order['payment_method'] == 'bank_transfer'): ?>
                    <div class="mb-3">
                        <h6 class="fw-bold">Bank Transfer Information</h6>
                        <p class="mb-2">
                            <strong>Bank:</strong> <?php echo htmlspecialchars($order['bank_name'] ?? 'Not specified'); ?>
                        </p>
                        <p class="mb-2">
                            <strong>Account Number:</strong> <?php echo htmlspecialchars($order['account_number'] ?? 'Not specified'); ?>
                        </p>
                        <p class="mb-2">
                            <strong>Account Holder:</strong> <?php echo htmlspecialchars($order['account_holder'] ?? 'Not specified'); ?>
                        </p>
                        <p class="mb-0">
                            <strong>Transfer Date:</strong> 
                            <?php echo isset($order['transfer_date']) ? date('d M Y', strtotime($order['transfer_date'])) : 'Not specified'; ?>
                        </p>
                    </div>
                    <?php elseif(isset($order['payment_method']) && $order['payment_method'] == 'cod'): ?>
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-info-circle me-2"></i>This order uses Cash on Delivery
                    </div>
                    <?php else: ?>
                    <div class="alert alert-secondary mb-0">
                        <i class="fas fa-info-circle me-2"></i>No detailed payment information available
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Order Timeline Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Order Timeline</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <?php
                        if(!empty($status_history)):
                        foreach($status_history as $history):
                            $history_status = $history['status'] ?? $history['new_status'] ?? 'pending';
                            $history_note = $history['note'] ?? $history['notes'] ?? 'Status updated';
                            $status_config_history = $status_config[$history_status] ?? ['class' => 'bg-secondary', 'label' => ucfirst($history_status)];
                        ?>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">
                                        <span class="badge <?php echo $status_config_history['class']; ?> me-2">
                                            <?php echo $status_config_history['label']; ?>
                                        </span>
                                    </h6>
                                    <p class="mb-1">
                                        <?php echo nl2br(htmlspecialchars($history_note)); ?>
                                    </p>
                                    <small class="text-muted">
                                        <?php echo date('M j, Y g:i A', strtotime($history['created_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php
                        endforeach;
                        else:
                        ?>
                        <div class="list-group-item">
                            <div class="text-center py-3">
                                <i class="fas fa-clock text-muted mb-2" style="font-size: 2rem;"></i>
                                <p class="mb-0 text-muted">No status history available</p>
                                <small class="text-muted">Status changes will appear here</small>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Order Modal -->
<div class="modal fade" id="cancelOrderModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action will permanently delete the order and cannot be undone.
                    The ordered items will be returned to inventory.
                </div>
                <form id="cancelOrderForm">
                    <input type="hidden" name="order_id" value="<?php echo $order_id; ?>">
                    <input type="hidden" name="cancel_order" value="1">
                    <div class="mb-3">
                        <label for="cancel_reason" class="form-label">Cancellation Reason <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="cancel_reason" name="cancel_reason" rows="3" required
                                  placeholder="Please provide a reason for cancelling this order..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-danger" id="confirmCancel">
                    <i class="fas fa-trash me-2"></i>Delete Order
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Tracking Number Modal -->
<div class="modal fade" id="addTrackingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Tracking Information</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="order_actions.php?action=add_tracking&return=detail" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="order_id" value="<?php echo $order_id; ?>">
                    <div class="mb-3">
                        <label for="tracking_number" class="form-label">Tracking Number</label>
                        <input type="text" class="form-control" id="tracking_number" name="tracking_number" value="<?php echo htmlspecialchars($order['tracking_number'] ?? ''); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="shipping_carrier" class="form-label">Shipping Carrier</label>
                        <select class="form-select" id="shipping_carrier" name="shipping_carrier">
                            <option value="">Select Carrier</option>
                            <option value="jne" <?php echo ($order['shipping_carrier'] ?? '') === 'jne' ? 'selected' : ''; ?>>JNE</option>
                            <option value="pos" <?php echo ($order['shipping_carrier'] ?? '') === 'pos' ? 'selected' : ''; ?>>POS Indonesia</option>
                            <option value="tiki" <?php echo ($order['shipping_carrier'] ?? '') === 'tiki' ? 'selected' : ''; ?>>TIKI</option>
                            <option value="sicepat" <?php echo ($order['shipping_carrier'] ?? '') === 'sicepat' ? 'selected' : ''; ?>>SiCepat</option>
                            <option value="jnt" <?php echo ($order['shipping_carrier'] ?? '') === 'jnt' ? 'selected' : ''; ?>>J&T Express</option>
                            <option value="other" <?php echo ($order['shipping_carrier'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="tracking_url" class="form-label">Tracking URL (Optional)</label>
                        <input type="url" class="form-control" id="tracking_url" name="tracking_url" value="<?php echo htmlspecialchars($order['tracking_url'] ?? ''); ?>">
                        <div class="form-text">Customer can use this URL to track their shipment</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="update_status" name="update_status" value="1" checked>
                            <label class="form-check-label" for="update_status">
                                Update order status to "Shipped"
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="notify_customer" name="notify_customer" value="1" checked>
                            <label class="form-check-label" for="notify_customer">
                                Send notification email to customer
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save Tracking Info</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Payment Proof Modal -->
<div class="modal fade" id="viewPaymentProofModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Payment Proof</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <?php if(!empty($order['payment_proof'])): ?>
                <img src="../uploads/payment_proof/<?php echo htmlspecialchars($order['payment_proof']); ?>" alt="Payment Proof" class="img-fluid">
                <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>No payment proof uploaded
                </div>
                <?php endif; ?>
            </div>
            <div class="modal-footer d-flex justify-content-between">
                <div>
                    <?php if(!empty($order['payment_proof'])): ?>
                    <a href="../uploads/payment_proof/<?php echo htmlspecialchars($order['payment_proof']); ?>" download class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>Download
                    </a>
                    <?php endif; ?>
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Send Email Modal -->
<div class="modal fade" id="sendEmailModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Email to Customer</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="order_actions.php?action=send_email&return=detail" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="order_id" value="<?php echo $order_id; ?>">
                    <div class="mb-3">
                        <label for="email_to" class="form-label">To</label>
                        <input type="email" class="form-control" id="email_to" name="email_to" value="<?php echo htmlspecialchars($order['shipping_email'] ?? $order['user_email'] ?? ''); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_subject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="email_subject" name="email_subject" value="Update for Your Order #<?php echo isset($order['order_number']) ? $order['order_number'] : $order_id; ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="email_template" class="form-label">Template</label>
                        <select class="form-select" id="email_template" name="email_template">
                            <option value="">Select Template</option>
                            <option value="order_confirmation">Order Confirmation</option>
                            <option value="payment_confirmation">Payment Confirmation</option>
                            <option value="shipping_notification">Shipping Notification</option>
                            <option value="delivery_confirmation">Delivery Confirmation</option>
                            <option value="order_cancellation">Order Cancellation</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="email_message" class="form-label">Message</label>
                        <textarea class="form-control" id="email_message" name="email_message" rows="5" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Send Email</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Apply stored status updates from localStorage
        const orderId = <?php echo $order_id; ?>;

        // Apply stored order status
        const storedOrderStatus = localStorage.getItem(`order_${orderId}_status`);
        if (storedOrderStatus) {
            const orderStatusSelect = document.getElementById('orderStatusSelect');
            if (orderStatusSelect && orderStatusSelect.value !== storedOrderStatus) {
                orderStatusSelect.value = storedOrderStatus;
                updateStatusBadge(storedOrderStatus);
                console.log(`Applied stored order status: ${storedOrderStatus}`);
            }
        }

        // Apply stored payment status
        const storedPaymentStatus = localStorage.getItem(`order_${orderId}_payment_status`);
        if (storedPaymentStatus) {
            const paymentStatusSelect = document.getElementById('paymentStatusSelect');
            if (paymentStatusSelect && paymentStatusSelect.value !== storedPaymentStatus) {
                paymentStatusSelect.value = storedPaymentStatus;
                updatePaymentStatusBadge(storedPaymentStatus);
                console.log(`Applied stored payment status: ${storedPaymentStatus}`);
            }
        }

        // Confirm cancel order
        document.getElementById('confirmCancel').addEventListener('click', async function() {
            const form = document.getElementById('cancelOrderForm');
            const textarea = document.getElementById('cancel_reason');
            const confirmBtn = this;

            if (textarea.value.trim() === '') {
                alert('Please provide a reason for cancellation');
                textarea.focus();
                return;
            }

            // Show loading state
            const originalText = confirmBtn.innerHTML;
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Deleting...';

            try {
                const formData = new FormData(form);

                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // Sync order deletion with orders page
                    syncOrderDeletion(<?php echo $order_id; ?>);

                    // Show success message
                    showAdminNotification('Order cancelled and deleted successfully!', 'success');

                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('cancelOrderModal'));
                    modal.hide();

                    // Redirect to orders page after short delay
                    setTimeout(() => {
                        window.location.href = 'order_management.php';
                    }, 1500);
                } else {
                    throw new Error(data.message || 'Failed to cancel order');
                }

            } catch (error) {
                console.error('Error cancelling order:', error);
                showAdminNotification('Error: ' + error.message, 'danger');

                // Reset button
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = originalText;
            }
        });
        
        // Email template selector
        document.getElementById('email_template').addEventListener('change', function() {
            const template = this.value;
            const messageField = document.getElementById('email_message');
            const customerName = '<?php echo htmlspecialchars($order['shipping_name'] ?? $order['user_name'] ?? 'Customer'); ?>';
            const orderNumber = '<?php echo isset($order['order_number']) ? htmlspecialchars($order['order_number']) : $order_id; ?>';
            
            let message = '';
            
            switch(template) {
                case 'order_confirmation':
                    message = `Dear ${customerName},\n\nThank you for your order #${orderNumber}. We're pleased to confirm that we've received your order and it is now being processed.\n\nYou can track your order status by logging into your account. We'll notify you once your items have been shipped.\n\nBest regards,\nThe Team`;
                    break;
                case 'payment_confirmation':
                    message = `Dear ${customerName},\n\nWe're pleased to confirm that we've received your payment for order #${orderNumber}. Your order is now being processed and will be shipped soon.\n\nThank you for your purchase!\n\nBest regards,\nThe Team`;
                    break;
                case 'shipping_notification':
                    message = `Dear ${customerName},\n\nGreat news! Your order #${orderNumber} has been shipped.\n\nTracking Number: <?php echo htmlspecialchars($order['tracking_number'] ?? '[Tracking Number]'); ?>\nCarrier: <?php echo ucfirst(htmlspecialchars($order['shipping_carrier'] ?? '[Carrier]')); ?>\n\nYou can track your package at: <?php echo htmlspecialchars($order['tracking_url'] ?? '[Tracking URL]'); ?>\n\nThank you for shopping with us!\n\nBest regards,\nThe Team`;
                    break;
                case 'delivery_confirmation':
                    message = `Dear ${customerName},\n\nYour order #${orderNumber} has been delivered. We hope you enjoy your purchase!\n\nIf you have any questions or need further assistance, please don't hesitate to contact us.\n\nBest regards,\nThe Team`;
                    break;
                case 'order_cancellation':
                    message = `Dear ${customerName},\n\nWe're writing to confirm that your order #${orderNumber} has been cancelled as requested.\n\nIf you made a payment, a refund will be processed according to our refund policy. If you have any questions, please contact our customer service.\n\nBest regards,\nThe Team`;
                    break;
            }
            
            messageField.value = message;
        });

        // Real-time sync functions
        function syncStatusWithOrdersPage(orderId, newStatus) {
            // Store the status update for orders page to pick up
            const statusUpdate = {
                order_id: orderId,
                new_status: newStatus,
                timestamp: Date.now(),
                source: 'order_detail'
            };

            // Store in localStorage for orders page to detect
            let orderUpdates = JSON.parse(localStorage.getItem('orderStatusUpdates') || '{}');
            orderUpdates[orderId] = statusUpdate;
            localStorage.setItem('orderStatusUpdates', JSON.stringify(orderUpdates));

            console.log(`Synced status update for order ${orderId}: ${newStatus}`);

            // Also sync to user panel
            syncStatusWithUserPanel(orderId, newStatus);
        }

        // Enhanced function to sync with user panel using unified sync system
        function syncStatusWithUserPanel(orderId, newStatus) {
            const adminNote = document.getElementById('notes').value || `Status updated to ${newStatus}`;

            // Use the unified sync system if available
            if (window.unifiedStatusSync) {
                window.unifiedStatusSync.storeStatusUpdate(orderId, newStatus, adminNote, 'Admin');
                console.log(`Synced status update via unified system for order ${orderId}: ${newStatus}`);
            } else if (window.orderStatusSync) {
                // Use the old OrderStatusSync system if available
                window.orderStatusSync.storeStatusUpdate(orderId, newStatus, adminNote, 'Admin');
                console.log(`Synced status update via old system for order ${orderId}: ${newStatus}`);
            } else {
                // Fallback to direct localStorage method
                const userStatusUpdate = {
                    order_id: orderId,
                    new_status: newStatus,
                    original_status: newStatus,
                    admin_note: adminNote,
                    timestamp: Date.now(),
                    source: 'admin_update',
                    admin_name: 'Admin',
                    processed: false
                };

                let userUpdates = JSON.parse(localStorage.getItem('tewuneed_order_status_sync') || '{}');
                userUpdates[orderId] = userStatusUpdate;
                localStorage.setItem('tewuneed_order_status_sync', JSON.stringify(userUpdates));

                console.log(`Synced status to user panel for order ${orderId}: ${newStatus}`);

                // Trigger storage event for cross-tab communication
                window.dispatchEvent(new StorageEvent('storage', {
                    key: 'tewuneed_order_status_sync',
                    newValue: JSON.stringify(userUpdates),
                    oldValue: null
                }));
            }
        }

        function syncOrderDeletion(orderId) {
            // Store deletion notification for orders page
            const deletionUpdate = {
                order_id: orderId,
                action: 'deleted',
                timestamp: Date.now(),
                source: 'order_detail'
            };

            let orderUpdates = JSON.parse(localStorage.getItem('orderStatusUpdates') || '{}');
            orderUpdates[orderId] = deletionUpdate;
            localStorage.setItem('orderStatusUpdates', JSON.stringify(orderUpdates));

            console.log(`Synced order deletion for order ${orderId}`);
        }
    });

    // Real-time status update functionality
    function updateOrderStatusRealtime(orderId, newStatus, adminNote = '') {
        // Update status badge
        const statusBadges = document.querySelectorAll('.badge');
        statusBadges.forEach(badge => {
            if (badge.textContent.toLowerCase().includes('pending') ||
                badge.textContent.toLowerCase().includes('processing') ||
                badge.textContent.toLowerCase().includes('shipped') ||
                badge.textContent.toLowerCase().includes('delivered') ||
                badge.textContent.toLowerCase().includes('cancelled')) {

                // Update badge text and class
                badge.textContent = newStatus.charAt(0).toUpperCase() + newStatus.slice(1);

                // Update badge color
                badge.className = 'badge ';
                switch(newStatus.toLowerCase()) {
                    case 'pending':
                    case 'dibuat':
                        badge.className += 'bg-warning';
                        break;
                    case 'processing':
                    case 'diproses':
                        badge.className += 'bg-info';
                        break;
                    case 'shipped':
                    case 'dikirim':
                        badge.className += 'bg-primary';
                        break;
                    case 'delivered':
                    case 'terkirim':
                        badge.className += 'bg-success';
                        break;
                    case 'cancelled':
                    case 'dibatalkan':
                        badge.className += 'bg-danger';
                        break;
                    default:
                        badge.className += 'bg-secondary';
                }
            }
        });

        // Update select dropdown
        const statusSelect = document.querySelector('select[name="order_status"]');
        if (statusSelect) {
            statusSelect.value = newStatus;
        }

        // Add to timeline
        addToOrderTimeline(newStatus, adminNote);

        // Show notification
        showAdminNotification('Order status updated to: ' + newStatus, 'success');
    }

    function addToOrderTimeline(status, note) {
        const timeline = document.querySelector('.list-group-flush');
        if (!timeline) return;

        const timelineItem = document.createElement('div');
        timelineItem.className = 'list-group-item';
        timelineItem.style.backgroundColor = '#f8f9fa';
        timelineItem.innerHTML = `
            <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">${status.charAt(0).toUpperCase() + status.slice(1)}</h6>
                <small>${new Date().toLocaleDateString('en-GB', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                })}</small>
            </div>
            <p class="mb-1">${note || 'Status updated via real-time sync'}</p>
            <small>By: <?php echo $_SESSION['full_name'] ?? $_SESSION['username'] ?? 'Admin'; ?> <span class="text-success">(Real-time)</span></small>
        `;

        timeline.insertBefore(timelineItem, timeline.firstChild);
    }

    function showAdminNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
        notification.innerHTML = `
            <i class="fas fa-sync-alt me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    function updateStatusBadge(newStatus) {
        // Update the specific order status badge
        const statusBadge = document.getElementById('orderStatusBadge');
        if (statusBadge) {
            // Status display mapping
            const statusConfig = {
                'pending': { label: 'Pending', class: 'bg-warning' },
                'diproses': { label: 'Processing', class: 'bg-info' },
                'processing': { label: 'Processing', class: 'bg-info' },
                'dikirim': { label: 'Shipped', class: 'bg-primary' },
                'shipped': { label: 'Shipped', class: 'bg-primary' },
                'terkirim': { label: 'Delivered', class: 'bg-success' },
                'delivered': { label: 'Delivered', class: 'bg-success' },
                'dibatalkan': { label: 'Cancelled', class: 'bg-danger' },
                'cancelled': { label: 'Cancelled', class: 'bg-danger' }
            };

            const config = statusConfig[newStatus] || { label: newStatus, class: 'bg-secondary' };

            // Update badge
            statusBadge.textContent = config.label;
            statusBadge.className = `badge ${config.class}`;

            console.log(`Status badge updated to: ${config.label} (${config.class})`);
        } else {
            console.error('Status badge element not found');
        }
    }

    function updatePaymentStatusBadge(newStatus) {
        // Update the specific payment status badge
        const paymentBadge = document.getElementById('paymentStatusBadge');
        if (paymentBadge) {
            // Payment status display mapping
            const paymentConfig = {
                'pending': { label: 'Pending', class: 'bg-warning' },
                'paid': { label: 'Paid', class: 'bg-success' },
                'failed': { label: 'Failed', class: 'bg-danger' },
                'refunded': { label: 'Refunded', class: 'bg-info' }
            };

            const config = paymentConfig[newStatus] || { label: newStatus, class: 'bg-secondary' };

            // Update badge
            paymentBadge.textContent = config.label;
            paymentBadge.className = `badge ${config.class}`;

            console.log(`Payment status badge updated to: ${config.label} (${config.class})`);
        } else {
            console.error('Payment status badge element not found');
        }
    }

    // Status update form handler
    document.getElementById('statusUpdateForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        console.log('Status update form submitted');

        const formData = new FormData(this);
        const submitBtn = document.getElementById('updateStatusBtn');
        const originalText = submitBtn.textContent;
        const newStatus = formData.get('order_status');
        const notes = formData.get('notes');

        console.log('Form data:', {
            order_status: newStatus,
            notes: notes,
            order_id: <?php echo $order_id; ?>
        });

        try {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';

            console.log('Sending AJAX request...');
            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const responseText = await response.text();
            console.log('Raw response:', responseText);

            let data;
            try {
                data = JSON.parse(responseText);
            } catch (parseError) {
                console.error('JSON parse error:', parseError);
                throw new Error('Invalid JSON response: ' + responseText.substring(0, 100));
            }

            console.log('Parsed response:', data);

            if (data.success) {
                console.log('Status update successful');

                // Show debug info
                showDebugInfo({
                    action: 'Status Update Success',
                    oldStatus: data.old_status,
                    newStatus: data.new_status,
                    orderId: data.order_id,
                    timestamp: new Date().toLocaleString()
                });

                // PERMANENTLY update the select element
                const statusSelect = document.getElementById('orderStatusSelect');
                statusSelect.value = data.new_status;

                // Update status badge
                updateStatusBadge(data.new_status);

                // Handle payment status sync if available
                if (data.auto_payment_sync && data.new_payment_status) {
                    // Update payment status select
                    const paymentSelect = document.getElementById('paymentStatusSelect');
                    if (paymentSelect) {
                        paymentSelect.value = data.new_payment_status;
                    }

                    // Update payment status badge
                    updatePaymentStatusBadge(data.new_payment_status);

                    // Store payment status in localStorage
                    localStorage.setItem(`order_${<?php echo $order_id; ?>}_payment_status`, data.new_payment_status);

                    // Show sync message
                    if (data.sync_message) {
                        showAdminNotification(data.sync_message, 'info');
                    }
                }

                showStatusResult('Order status updated successfully!', 'success', data);

                // Store in localStorage to persist across page reloads
                localStorage.setItem(`order_${<?php echo $order_id; ?>}_status`, data.new_status);

                // Sync with orders admin page
                if (typeof syncStatusWithOrdersPage === 'function') {
                    syncStatusWithOrdersPage(<?php echo $order_id; ?>, data.new_status);
                }

                // Sync with user panel
                if (typeof syncStatusWithUserPanel === 'function') {
                    syncStatusWithUserPanel(<?php echo $order_id; ?>, data.new_status);
                }

                // Clear notes
                document.getElementById('notes').value = '';
            } else {
                console.error('Status update failed:', data.message);
                showDebugInfo({
                    action: 'Status Update Failed',
                    error: data.message,
                    timestamp: new Date().toLocaleString()
                });
                showStatusResult('Error: ' + data.message, 'danger', data);
            }

        } catch (error) {
            console.error('Status update error:', error);
            showDebugInfo({
                action: 'Status Update Error',
                error: error.message,
                stack: error.stack,
                timestamp: new Date().toLocaleString()
            });
            showStatusResult('Error updating order status: ' + error.message, 'danger', null);
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });

    // Helper function to show debug information
    function showDebugInfo(info) {
        const debugResults = document.getElementById('debugResults');
        const debugContent = document.getElementById('debugContent');

        if (debugResults && debugContent) {
            debugResults.style.display = 'block';
            debugContent.textContent = JSON.stringify(info, null, 2);
        }

        console.log('Debug Info:', info);
    }

    // Helper function to show status update results
    function showStatusResult(message, type, data) {
        const resultsDiv = document.getElementById('statusUpdateResults');
        if (resultsDiv) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const icon = type === 'success' ? '✅' : '❌';

            let content = `
                <div class="alert ${alertClass}">
                    <h6>${icon} ${message}</h6>
            `;

            if (data && data.old_status && data.new_status) {
                content += `
                    <p><strong>From:</strong> ${data.old_status} → <strong>To:</strong> ${data.new_status}</p>
                    <small>Updated at: ${new Date().toLocaleString()}</small>
                `;
            }

            content += '</div>';
            resultsDiv.innerHTML = content;

            // Auto-hide after 5 seconds
            setTimeout(() => {
                resultsDiv.innerHTML = '';
            }, 5000);
        }

        // Also show browser notification if available
        if (typeof showAdminNotification === 'function') {
            showAdminNotification(message, type);
        }
    }

    // Admin notification function
    function showAdminNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px; max-width: 500px;';
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Payment update form handler
    document.getElementById('paymentUpdateForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = document.getElementById('updatePaymentBtn');
        const originalText = submitBtn.textContent;

        try {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';

            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                // PERMANENTLY update the select element
                const paymentSelect = document.getElementById('paymentStatusSelect');
                paymentSelect.value = data.new_payment_status;

                // Update payment status badge
                updatePaymentStatusBadge(data.new_payment_status);
                showAdminNotification('Payment status updated successfully!', 'success');

                // Store in localStorage to persist across page reloads
                localStorage.setItem(`order_${<?php echo $order_id; ?>}_payment_status`, data.new_payment_status);

                // Clear notes
                document.getElementById('payment_notes').value = '';
            } else {
                showAdminNotification('Error: ' + data.message, 'danger');
            }

        } catch (error) {
            console.error('Error:', error);
            showAdminNotification('Error updating payment status', 'danger');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    });

    // Apply stored status updates on page load
    function applyStoredStatusUpdates() {
        const orderId = <?php echo $order_id; ?>;
        const storedStatus = localStorage.getItem(`order_${orderId}_status`);

        if (storedStatus && storedStatus !== 'undefined' && storedStatus !== 'null') {
            console.log(`Applying stored status for order ${orderId}: ${storedStatus}`);

            // Update the select element
            const statusSelect = document.getElementById('orderStatusSelect');
            if (statusSelect && statusSelect.value !== storedStatus) {
                statusSelect.value = storedStatus;

                // Update the status badge
                updateStatusBadge(storedStatus);

                // Show notification that status was restored (only if it's a valid status)
                const displayName = getStatusDisplayName(storedStatus);
                if (displayName && displayName !== 'Unknown') {
                    showAdminNotification(`Status restored: ${displayName}`, 'info');
                }
            }
        }

        // Apply stored payment status
        const storedPaymentStatus = localStorage.getItem(`order_${orderId}_payment_status`);
        if (storedPaymentStatus && storedPaymentStatus !== 'undefined' && storedPaymentStatus !== 'null') {
            const paymentStatusSelect = document.getElementById('paymentStatusSelect');
            if (paymentStatusSelect && paymentStatusSelect.value !== storedPaymentStatus) {
                paymentStatusSelect.value = storedPaymentStatus;
                updatePaymentStatusBadge(storedPaymentStatus);
                console.log(`Applied stored payment status: ${storedPaymentStatus}`);
            }
        }
    }

    // Get display name for status
    function getStatusDisplayName(status) {
        const statusNames = {
            'pending': 'Pending',
            'diproses': 'Processing',
            'processing': 'Processing',
            'dikirim': 'Shipped',
            'shipped': 'Shipped',
            'terkirim': 'Delivered',
            'delivered': 'Delivered',
            'dibatalkan': 'Cancelled',
            'cancelled': 'Cancelled'
        };
        return statusNames[status] || (status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown');
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        // Apply any stored status updates
        setTimeout(applyStoredStatusUpdates, 500);
    });
</script>

<!-- Include Order Status Sync System -->
<script src="../js/order-status-sync.js"></script>
<!-- Unified Status Sync System -->
<script src="../js/unified-status-sync.js"></script>

<?php require_once 'includes/footer.php'; ?>
