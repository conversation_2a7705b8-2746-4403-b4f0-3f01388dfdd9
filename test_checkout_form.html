<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Checkout Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <h1>Test Checkout Form</h1>
        <p>This is a simple test form to check if form submission works properly.</p>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Test Form</h4>
                    </div>
                    <div class="card-body">
                        <form method="post" action="checkout.php" id="test-checkout-form">
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <label for="shipping_name" class="form-label">Full Name *</label>
                                    <input type="text" class="form-control" id="shipping_name" name="shipping_name" value="Test User" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="shipping_email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control" id="shipping_email" name="shipping_email" value="<EMAIL>" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="shipping_phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="shipping_phone" name="shipping_phone" value="************" required>
                            </div>

                            <div class="mb-4">
                                <label for="shipping_address" class="form-label">Shipping Address *</label>
                                <textarea class="form-control" id="shipping_address" name="shipping_address" rows="3" required>Test Address 123, Test City</textarea>
                            </div>

                            <div class="mb-4">
                                <label class="form-label">Shipping Method *</label>
                                <div class="form-check mb-2">
                                    <input type="radio" class="form-check-input" name="shipping_method" id="shipping_standard" value="standard" checked>
                                    <label class="form-check-label" for="shipping_standard">
                                        Standard Shipping (2-3 days) - Rp 10.000
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input type="radio" class="form-check-input" name="shipping_method" id="shipping_express" value="express">
                                    <label class="form-check-label" for="shipping_express">
                                        Express Shipping (1 day) - Rp 25.000
                                    </label>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label">Payment Method *</label>
                                <div class="form-check mb-2">
                                    <input type="radio" class="form-check-input" name="payment_method" id="payment_bank_transfer" value="bank_transfer" checked>
                                    <label class="form-check-label" for="payment_bank_transfer">
                                        Bank Transfer
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input type="radio" class="form-check-input" name="payment_method" id="payment_cod" value="cod">
                                    <label class="form-check-label" for="payment_cod">
                                        Cash on Delivery (COD)
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input type="radio" class="form-check-input" name="payment_method" id="payment_qris" value="qris">
                                    <label class="form-check-label" for="payment_qris">
                                        QRIS
                                    </label>
                                </div>
                            </div>

                            <!-- Bank selection for bank transfer -->
                            <div id="bank_selection" class="mb-4">
                                <label class="form-label">Select Bank:</label>
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="selected_bank" value="bca" id="bank_bca" checked>
                                    <label class="form-check-label" for="bank_bca">BCA</label>
                                </div>
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="selected_bank" value="mandiri" id="bank_mandiri">
                                    <label class="form-check-label" for="bank_mandiri">Mandiri</label>
                                </div>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="cart.php" class="btn btn-outline-secondary">Back to Cart</a>
                                <button type="submit" class="btn btn-primary" id="test-place-order-btn">
                                    <span class="btn-text">Place Order (Test)</span>
                                    <span class="btn-loading d-none">
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        Processing...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Purpose:</strong> Test checkout form submission</p>
                        <p><strong>Action:</strong> checkout.php</p>
                        <p><strong>Method:</strong> POST</p>
                        
                        <h6>Pre-filled Data:</h6>
                        <ul>
                            <li>Name: Test User</li>
                            <li>Email: <EMAIL></li>
                            <li>Phone: ************</li>
                            <li>Address: Test Address 123</li>
                            <li>Payment: Bank Transfer (BCA)</li>
                            <li>Shipping: Standard</li>
                        </ul>
                        
                        <div class="alert alert-info">
                            <strong>Note:</strong> Make sure you're logged in before testing!
                        </div>
                        
                        <div class="mt-3">
                            <a href="login.php" class="btn btn-sm btn-outline-primary">Login</a>
                            <a href="debug_checkout.php" class="btn btn-sm btn-outline-info">Debug Checkout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('test-checkout-form');
            const placeOrderBtn = document.getElementById('test-place-order-btn');
            
            form.addEventListener('submit', function(e) {
                console.log('Test form submission started');
                console.log('Form data:', new FormData(form));
                
                // Show loading state
                placeOrderBtn.disabled = true;
                placeOrderBtn.querySelector('.btn-text').classList.add('d-none');
                placeOrderBtn.querySelector('.btn-loading').classList.remove('d-none');
                
                // Add timeout to reset button
                setTimeout(function() {
                    if (placeOrderBtn.disabled) {
                        console.log('Form submission timeout, resetting button');
                        placeOrderBtn.disabled = false;
                        placeOrderBtn.querySelector('.btn-text').classList.remove('d-none');
                        placeOrderBtn.querySelector('.btn-loading').classList.add('d-none');
                    }
                }, 10000);
            });
            
            // Show/hide bank selection based on payment method
            const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
            const bankSelection = document.getElementById('bank_selection');
            
            paymentMethods.forEach(method => {
                method.addEventListener('change', function() {
                    if (this.value === 'bank_transfer') {
                        bankSelection.style.display = 'block';
                    } else {
                        bankSelection.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
